#!/bin/bash

# Medroid Stop Script
# Safely stops all Medroid services

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/tunnel.pid"
LARAVEL_PID_FILE="$SCRIPT_DIR/laravel.pid"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}🛑 Stopping Medroid services...${NC}"

# Stop tunnel
if [[ -f "$PID_FILE" ]]; then
    tunnel_pid=$(cat "$PID_FILE")
    if kill -0 "$tunnel_pid" 2>/dev/null; then
        kill "$tunnel_pid"
        echo -e "${GREEN}✅ Tunnel stopped${NC}"
    fi
    rm -f "$PID_FILE"
fi

# Stop Laravel
if [[ -f "$LARAVEL_PID_FILE" ]]; then
    laravel_pid=$(cat "$LARAVEL_PID_FILE")
    if kill -0 "$laravel_pid" 2>/dev/null; then
        kill "$laravel_pid"
        echo -e "${GREEN}✅ Laravel server stopped${NC}"
    fi
    rm -f "$LARAVEL_PID_FILE"
fi

# Clean up any remaining processes
pkill -f "cloudflared tunnel run" 2>/dev/null || true
pkill -f "php artisan serve" 2>/dev/null || true

echo -e "${GREEN}✅ All Medroid services stopped${NC}"
