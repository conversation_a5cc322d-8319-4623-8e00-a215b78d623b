<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Provider;
use App\Models\SocialContent;
use App\Models\Story;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;
use App\Services\FileUploadService;

class UserController extends Controller
{
    /**
     * Display a listing of the users.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Check if user has permission to view users
        if (!$request->user()->can('view users')) {
            \Log::info('User lacks view users permission', [
                'user_id' => $request->user()->id,
                'user_permissions' => $request->user()->getAllPermissions()->pluck('name')->toArray(),
                'user_roles' => $request->user()->getRoleNames()->toArray()
            ]);
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = User::query();

        // Apply search filter if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by role if provided (check both role column and Spatie roles)
        if ($request->has('role') && $request->role && $request->role !== 'all') {
            $role = $request->role;
            $query->where(function($q) use ($role) {
                $q->where('role', $role)
                  ->orWhereHas('roles', function($roleQuery) use ($role) {
                      $roleQuery->where('name', $role);
                  });
            });
        }

        // Apply sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDir = $request->input('sort_dir', 'desc');
        $query->orderBy($sortBy, $sortDir);

        // Load relationships based on role
        $query->with('roles');

        // Paginate results
        $perPage = $request->input('per_page', 10);
        $users = $query->paginate($perPage);

        \Log::info('Users query result', [
            'total_users' => $users->total(),
            'current_page' => $users->currentPage(),
            'per_page' => $users->perPage(),
            'data_count' => $users->count()
        ]);

        return response()->json($users);
    }

    /**
     * Store a newly created user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if user has permission to create users
        if (!$request->user()->can('create users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:patient,provider,admin',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'role' => $request->role,
        ]);

        // Assign role
        if ($request->has('role')) {
            // Make sure the role exists in the database
            if (!\Spatie\Permission\Models\Role::where('name', $request->role)->exists()) {
                \Spatie\Permission\Models\Role::create(['name' => $request->role]);
            }
            $user->assignRole($request->role);
        }

        // Create related profile if needed
        if ($request->role === 'provider' && $request->has('provider')) {
            $provider = new Provider([
                'specialization' => $request->provider['specialization'] ?? null,
                'license_number' => $request->provider['license_number'] ?? null,
                'verification_status' => 'pending',
            ]);
            $user->provider()->save($provider);
        }

        // Load relationships
        $user->load('roles');
        if ($user->hasRole('patient')) {
            $user->load('patient');
        } else if ($user->hasRole('provider')) {
            $user->load('provider');
        }

        return response()->json($user, 201);
    }

    /**
     * Display the specified user.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        // Check if user has permission to view users
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::with('roles')->findOrFail($id);

        // Load role-specific relationships
        if ($user->hasRole('patient')) {
            $user->load('patient');
        } else if ($user->hasRole('provider')) {
            $user->load('provider');
        }

        return response()->json($user);
    }

    /**
     * Update the specified user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id = null)
    {
        // If no ID is provided, update the authenticated user's profile
        if ($id === null) {
            return $this->updateProfile($request);
        }

        // Check if user has permission to edit users
        if (!$request->user()->can('edit users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $id,
            'role' => 'sometimes|required|in:patient,provider,admin',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Update basic user information
        if ($request->has('name')) {
            $user->name = $request->name;
        }

        if ($request->has('email')) {
            $user->email = $request->email;
        }

        if ($request->has('role') && $request->role !== $user->role) {
            $user->role = $request->role;

            // Update roles
            // Make sure the role exists in the database
            if (!\Spatie\Permission\Models\Role::where('name', $request->role)->exists()) {
                \Spatie\Permission\Models\Role::create(['name' => $request->role]);
            }
            $user->syncRoles([$request->role]);
        }

        $user->save();

        // Update password if provided
        if ($request->filled('password') && $request->filled('password_confirmation')) {
            $passwordValidator = Validator::make($request->all(), [
                'password' => 'required|string|min:8|confirmed',
            ]);

            if ($passwordValidator->fails()) {
                return response()->json(['errors' => $passwordValidator->errors()], 422);
            }

            $user->password = bcrypt($request->password);
            $user->save();
        }

        // Update provider information if applicable
        if ($user->role === 'provider' && $request->has('provider')) {
            $provider = $user->provider ?? new Provider();

            if (isset($request->provider['specialization'])) {
                $provider->specialization = $request->provider['specialization'];
            }

            if (isset($request->provider['license_number'])) {
                $provider->license_number = $request->provider['license_number'];
            }

            if (isset($request->provider['verification_status'])) {
                $provider->verification_status = $request->provider['verification_status'];
            }

            $user->provider()->save($provider);
        }

        // Reload user with relationships
        $user->load('roles');
        if ($user->role === 'patient') {
            $user->load('patient');
        } else if ($user->role === 'provider') {
            $user->load('provider');
        }

        return response()->json($user);
    }

    /**
     * Update the authenticated user's profile
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $request->user()->id,
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = $request->user();

        if ($request->has('name')) {
            $user->name = $request->name;
        }

        if ($request->has('email')) {
            $user->email = $request->email;
        }

        $user->save();

        // Reload user with relationships
        if ($user->role === 'patient') {
            $user->load('patient');
        } else if ($user->role === 'provider') {
            $user->load('provider');
        }

        return response()->json($user);
    }

    /**
     * Upload profile image for the authenticated user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadProfileImage(Request $request, FileUploadService $fileUploadService)
    {
        $validator = Validator::make($request->all(), [
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = $request->user();

            if ($request->hasFile('profile_image')) {
                // Upload file using the file management system
                $file = $fileUploadService->uploadAndAttachFile(
                    $request->file('profile_image'),
                    $user,
                    'profile_image',
                    'profile_images',
                    'Profile Image for ' . $user->name,
                    'User profile image',
                    true // Make profile images public
                );

                // Update user profile_image field with the file URL
                $user->profile_image = $file->url;
                $user->save();

                Log::info('Profile image uploaded via file management system', [
                    'user_id' => $user->id,
                    'file_id' => $file->id,
                    'file_url' => $file->url
                ]);

                // Reload user with relationships
                if ($user->role === 'patient') {
                    $user->load('patient');
                } else if ($user->role === 'provider') {
                    $user->load('provider');
                }

                return response()->json([
                    'user' => $user,
                    'file' => $file
                ]);
            }

            return response()->json(['message' => 'No image file provided'], 400);
        } catch (\Exception $e) {
            Log::error('Failed to upload profile image', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to upload profile image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle user active/inactive status
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleStatus(Request $request, $id)
    {
        // Check if user has permission to edit users
        if (!$request->user()->can('edit users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::findOrFail($id);

        // Prevent deactivating admin users
        if ($user->hasRole('admin')) {
            return response()->json(['message' => 'Cannot deactivate admin users'], 403);
        }

        $request->validate([
            'is_active' => 'required|boolean'
        ]);

        $user->is_active = $request->is_active;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => $user->is_active ? 'User activated successfully' : 'User deactivated successfully',
            'user' => $user
        ]);
    }

    /**
     * Remove the specified user from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        // Check if user has permission to delete users
        if (!$request->user()->can('delete users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::findOrFail($id);

        // Prevent deleting your own account
        if ($request->user()->id === (int)$id) {
            return response()->json(['message' => 'You cannot delete your own account'], 400);
        }

        // Begin transaction
        DB::beginTransaction();

        try {
            // Remove roles from user before deleting to avoid model_has_roles issues
            $user->roles()->detach();

            // Delete the user
            $user->delete();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete user', [
                'user_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to delete user: ' . $e->getMessage()
            ], 500);
        }

        return response()->json(['message' => 'User deleted successfully']);
    }

    /**
     * Get users without provider profiles for provider creation
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsersWithoutProvider(Request $request)
    {
        // Check if user has permission to view users
        if (!$request->user()->can('view users')) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get users who don't have a provider profile
        $users = User::whereDoesntHave('provider')
            ->where('role', '!=', 'patient') // Exclude patients as they shouldn't be providers
            ->select('id', 'name', 'email', 'role')
            ->orderBy('name')
            ->get();

        return response()->json(['users' => $users]);
    }

    /**
     * Get user profile stats for social media profile
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProfileStats(Request $request)
    {
        try {
            $user = $request->user();

            // Get user's posts count
            $postsCount = SocialContent::where('user_id', $user->id)
                ->where('source', 'internal')
                ->count();

            // Get user's stories count (active stories only)
            $storiesCount = Story::where('user_id', $user->id)
                ->active()
                ->count();

            // Get followers/following counts
            $followersCount = $user->followers()->count();
            $followingCount = $user->following()->count();

            // Get recent posts (last 9 for grid display)
            $recentPosts = SocialContent::where('user_id', $user->id)
                ->where('source', 'internal')
                ->orderBy('created_at', 'desc')
                ->limit(9)
                ->get(['id', 'media_url', 'caption', 'created_at']);

            // Get recent stories (last 6 for display)
            $recentStories = Story::where('user_id', $user->id)
                ->active()
                ->orderBy('created_at', 'desc')
                ->limit(6)
                ->get(['id', 'media_url', 'caption', 'created_at']);

            return response()->json([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'profile_image' => $user->profile_image,
                    'bio' => $user->bio,
                    'is_founder_member' => $user->is_founder_member,
                ],
                'stats' => [
                    'posts' => $postsCount,
                    'followers' => $followersCount,
                    'following' => $followingCount,
                    'stories' => $storiesCount,
                ],
                'posts' => $recentPosts,
                'stories' => $recentStories,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get profile stats', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to load profile stats'
            ], 500);
        }
    }

    /**
     * Update user profile image
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfileImage(Request $request, FileUploadService $fileUploadService)
    {
        try {
            $user = $request->user();

            $validator = Validator::make($request->all(), [
                'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048' // 2MB max to match PHP settings
            ]);

            if ($validator->fails()) {
                Log::warning('Profile image validation failed', [
                    'user_id' => $user->id,
                    'errors' => $validator->errors()
                ]);

                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            if ($request->hasFile('profile_image')) {
                // Find and remove old profile image file usage
                $oldFileUsage = \App\Models\FileUsage::where('usable_type', get_class($user))
                    ->where('usable_id', $user->id)
                    ->where('usage_type', 'profile_image')
                    ->first();

                // Upload new profile image using file management system
                $file = $fileUploadService->uploadAndAttachFile(
                    $request->file('profile_image'),
                    $user,
                    'profile_image',
                    'profile_images',
                    'Profile Image for ' . $user->name,
                    'User profile image',
                    true // Make profile images public
                );

                // Update user profile_image field with the file URL
                $user->update(['profile_image' => $file->url]);

                // Delete old file if it exists
                if ($oldFileUsage && $oldFileUsage->file) {
                    $oldFileUsage->file->delete();
                }

                Log::info('Profile image updated via file management system', [
                    'user_id' => $user->id,
                    'file_id' => $file->id,
                    'file_url' => $file->url
                ]);

                return response()->json([
                    'message' => 'Profile image updated successfully',
                    'profile_image' => $file->url,
                    'file' => $file
                ]);
            }

            return response()->json([
                'message' => 'No image file provided'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Failed to update profile image', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to update profile image'
            ], 500);
        }
    }

    /**
     * Get profile stats for a specific user
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserProfileStats(Request $request, $userId)
    {
        try {
            $currentUser = $request->user();
            $targetUser = User::findOrFail($userId);

            // Get user's posts count
            $postsCount = SocialContent::where('user_id', $userId)
                ->where('filtered_status', 'approved')
                ->count();

            // Get followers/following counts
            $followersCount = $targetUser->followers()->count();
            $followingCount = $targetUser->following()->count();

            // Get stories count
            $storiesCount = Story::where('user_id', $userId)
                ->where('expires_at', '>', now())
                ->count();

            // Get recent posts
            $recentPosts = SocialContent::where('user_id', $userId)
                ->where('filtered_status', 'approved')
                ->orderBy('created_at', 'desc')
                ->limit(9)
                ->get(['id', 'caption', 'media_url', 'created_at', 'engagement_metrics']);

            // Get recent stories
            $recentStories = Story::where('user_id', $userId)
                ->where('expires_at', '>', now())
                ->orderBy('created_at', 'desc')
                ->limit(6)
                ->get(['id', 'media_url', 'created_at']);

            // Check if current user is following this user
            $isFollowing = $request->user()->isFollowing($targetUser->id);

            return response()->json([
                'user' => [
                    'id' => $targetUser->id,
                    'name' => $targetUser->name,
                    'email' => $targetUser->email,
                    'profile_image' => $targetUser->profile_image,
                    'bio' => $targetUser->bio,
                    'is_founder_member' => $targetUser->is_founder_member,
                ],
                'stats' => [
                    'posts' => $postsCount,
                    'followers' => $followersCount,
                    'following' => $followingCount,
                    'stories' => $storiesCount,
                ],
                'posts' => $recentPosts,
                'stories' => $recentStories,
                'is_following' => $isFollowing,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get user profile stats', [
                'user_id' => $userId,
                'current_user_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to load user profile'
            ], 500);
        }
    }

    /**
     * Follow a user
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function followUser(Request $request, $userId)
    {
        try {
            $currentUser = $request->user();
            $targetUser = User::findOrFail($userId);

            if ($currentUser->id === $targetUser->id) {
                return response()->json(['message' => 'Cannot follow yourself'], 400);
            }

            // Check if already following
            if ($currentUser->isFollowing($userId)) {
                return response()->json(['message' => 'Already following this user'], 400);
            }

            // Follow the user
            $currentUser->follow($userId);

            return response()->json([
                'message' => 'User followed successfully',
                'is_following' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to follow user', [
                'user_id' => $userId,
                'current_user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to follow user'], 500);
        }
    }

    /**
     * Unfollow a user
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function unfollowUser(Request $request, $userId)
    {
        try {
            $currentUser = $request->user();
            $targetUser = User::findOrFail($userId);

            if ($currentUser->id === $targetUser->id) {
                return response()->json(['message' => 'Cannot unfollow yourself'], 400);
            }

            // Check if not following
            if (!$currentUser->isFollowing($userId)) {
                return response()->json(['message' => 'Not following this user'], 400);
            }

            // Unfollow the user
            $currentUser->unfollow($userId);

            return response()->json([
                'message' => 'User unfollowed successfully',
                'is_following' => false
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to unfollow user', [
                'user_id' => $userId,
                'current_user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to unfollow user'], 500);
        }
    }

    /**
     * Update user bio
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBio(Request $request)
    {
        try {
            $user = $request->user();

            $validator = Validator::make($request->all(), [
                'bio' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Only allow users to update their own bio
            $user->update(['bio' => $request->bio]);

            Log::info('Bio updated successfully', [
                'user_id' => $user->id,
                'bio_length' => strlen($request->bio ?? '')
            ]);

            return response()->json([
                'message' => 'Bio updated successfully',
                'bio' => $user->bio
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update bio', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to update bio'], 500);
        }
    }

    /**
     * Get followers list for a user
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFollowers(Request $request, $userId)
    {
        try {
            $user = User::findOrFail($userId);
            $followers = $user->followers()
                ->select('users.id', 'users.name', 'users.profile_image', 'users.is_founder_member')
                ->get();

            return response()->json([
                'followers' => $followers,
                'count' => $followers->count()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get followers', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to load followers'], 500);
        }
    }

    /**
     * Get following list for a user
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFollowing(Request $request, $userId)
    {
        try {
            $user = User::findOrFail($userId);
            $following = $user->following()
                ->select('users.id', 'users.name', 'users.profile_image', 'users.is_founder_member')
                ->get();

            return response()->json([
                'following' => $following,
                'count' => $following->count()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get following', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return response()->json(['message' => 'Failed to load following'], 500);
        }
    }

    /**
     * Debug route to check user permissions
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function debugPermissions(Request $request)
    {
        $user = auth()->user();
        return response()->json([
            'user_id' => $user->id,
            'user_role' => $user->role,
            'spatie_roles' => $user->roles->pluck('name'),
            'spatie_permissions' => $user->getAllPermissions()->pluck('name'),
            'can_view_clinics' => $user->can('view clinics'),
            'has_admin_role' => $user->hasRole('admin'),
        ]);
    }


}
