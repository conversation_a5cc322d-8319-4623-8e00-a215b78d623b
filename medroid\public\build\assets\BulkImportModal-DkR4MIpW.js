import{r as p,c as R,d as o,n as u,e as r,i as t,A as v,I as $,x as w,t as n,F as j,p as E,N as V}from"./vendor-BhKTHoN5.js";const N={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},U={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},M={class:"mt-3"},z={class:"flex items-center justify-between mb-6"},L={class:"mb-6"},q={class:"flex items-center space-x-4 mb-4"},O={key:0,class:"space-y-4"},A={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},X={class:"flex flex-wrap gap-2"},K=["disabled"],Y=["href"],G={class:"flex justify-end"},H={key:1,class:"space-y-4"},J={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Q={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},W={key:0},Z={key:1,class:"space-y-2"},ee={class:"font-medium text-gray-900"},te={class:"text-gray-500 text-sm"},se={class:"flex justify-between"},le=["disabled"],oe={key:2,class:"space-y-4"},re={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},ae={key:0,class:"space-y-3"},ie={class:"flex items-center justify-between text-sm"},ne={class:"font-medium"},de={class:"flex items-center justify-between text-sm"},ue={class:"font-medium text-green-600"},me={key:0,class:"flex items-center justify-between text-sm"},pe={class:"font-medium text-red-600"},ce={key:1,class:"mt-4"},ve={class:"max-h-32 overflow-y-auto bg-red-50 border border-red-200 rounded p-3"},fe={key:0,class:"text-red-600 text-sm mt-2"},ge={class:"flex justify-between"},xe=["disabled"],be={key:3,class:"mt-4"},ye={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},we={key:0,class:"mt-3"},he={class:"w-full bg-blue-200 rounded-full h-2"},_e={class:"text-blue-700 text-sm mt-1"},Ce={__name:"BulkImportModal",props:{isOpen:{type:Boolean,default:!1}},emits:["close","imported"],setup(k,{emit:C}){const h=C,a=p(1),x=p(!1),d=p(null),b=p(!1),l=p(null),f=p(!1),c=p(0),S=R(()=>window.location.pathname.includes("/provider/")),y=()=>S.value?"/provider":"/admin",D=async()=>{x.value=!0;try{const s=await window.axios.get(`${y()}/products/import-template`,{responseType:"blob",timeout:3e4});if(!s.data||s.data.size===0)throw new Error("Empty response received");const e=window.URL.createObjectURL(new Blob([s.data],{type:"text/csv"})),i=document.createElement("a");i.href=e,i.setAttribute("download","product_import_template.csv"),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(e),console.log("Template downloaded successfully")}catch(s){console.error("Error downloading template:",s);let e="Error downloading template. ";s.response?s.response.status===403?e+="You do not have permission to download the template.":s.response.status===404?e+="Template not found.":s.response.status>=500?e+="Server error. Please try again later.":e+=`Server returned error ${s.response.status}.`:s.code==="ECONNABORTED"?e+="Request timed out. Please check your connection.":e+="Please check your connection and try again.",alert(e)}finally{x.value=!1}},F=s=>{const e=s.target.files[0];e&&(d.value=e)},P=()=>{d.value=null,l.value=null},T=s=>{if(s===0)return"0 Bytes";const e=1024,i=["Bytes","KB","MB","GB"],g=Math.floor(Math.log(s)/Math.log(e));return parseFloat((s/Math.pow(e,g)).toFixed(2))+" "+i[g]},I=async()=>{if(d.value){b.value=!0;try{const s=new FormData;s.append("file",d.value);const e=await window.axios.post(`${y()}/products/validate-import`,s,{headers:{"Content-Type":"multipart/form-data"}});l.value=e.data,a.value=3}catch(s){console.error("Error validating file:",s),alert("Error validating file. Please check the format and try again.")}finally{b.value=!1}}},B=async()=>{if(!(!d.value||!l.value)){f.value=!0,c.value=0;try{const s=new FormData;s.append("file",d.value);const e=await window.axios.post(`${y()}/products/bulk-import`,s,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:i=>{c.value=Math.round(i.loaded*100/i.total)}});h("imported",e.data),h("close")}catch(s){console.error("Error importing products:",s),alert("Error importing products. Please try again.")}finally{f.value=!1,c.value=0}}};return(s,e)=>{var i,g,_;return k.isOpen?(r(),o("div",N,[t("div",U,[t("div",M,[t("div",z,[e[6]||(e[6]=t("h3",{class:"text-lg font-medium text-gray-900"},"Bulk Import Products",-1)),t("button",{onClick:e[0]||(e[0]=m=>s.$emit("close")),class:"text-gray-400 hover:text-gray-600"},e[5]||(e[5]=[t("i",{class:"fas fa-times text-xl"},null,-1)]))]),t("div",L,[t("div",q,[t("div",{class:v(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",a.value>=1?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"])}," 1 ",2),t("span",{class:v(["text-sm",a.value>=1?"text-blue-600 font-medium":"text-gray-500"])}," Download Template ",2),e[7]||(e[7]=t("div",{class:"flex-1 h-px bg-gray-200"},null,-1)),t("div",{class:v(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",a.value>=2?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"])}," 2 ",2),t("span",{class:v(["text-sm",a.value>=2?"text-blue-600 font-medium":"text-gray-500"])}," Upload File ",2),e[8]||(e[8]=t("div",{class:"flex-1 h-px bg-gray-200"},null,-1)),t("div",{class:v(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",a.value>=3?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"])}," 3 ",2),t("span",{class:v(["text-sm",a.value>=3?"text-blue-600 font-medium":"text-gray-500"])}," Review & Import ",2)])]),a.value===1?(r(),o("div",O,[t("div",A,[e[12]||(e[12]=t("h4",{class:"font-medium text-blue-900 mb-2"},"Step 1: Download Import Template",-1)),e[13]||(e[13]=t("p",{class:"text-blue-800 text-sm mb-4"}," Download the CSV template file and fill it with your product data. Make sure to follow the format exactly. ",-1)),t("div",X,[t("button",{onClick:D,disabled:x.value,class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50"},[e[9]||(e[9]=t("i",{class:"fas fa-download mr-2"},null,-1)),w(" "+n(x.value?"Downloading...":"Download Template"),1)],8,K),e[11]||(e[11]=t("a",{href:"/templates/product_import_template.csv",download:"product_import_template.csv",class:"inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700",title:"Direct download (fallback)"},[t("i",{class:"fas fa-file-csv mr-2"}),w(" Direct Download ")],-1)),t("a",{href:`${y()}/products/import-instructions`,download:"product_import_instructions.txt",class:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700",title:"Download detailed instructions"},e[10]||(e[10]=[t("i",{class:"fas fa-info-circle mr-2"},null,-1),w(" Instructions ")]),8,Y)])]),e[14]||(e[14]=$('<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4"><h5 class="font-medium text-yellow-900 mb-2">Important Notes:</h5><ul class="text-yellow-800 text-sm space-y-1"><li>• All required fields must be filled</li><li>• SKU must be unique for each product</li><li>• Price should be in decimal format (e.g., 29.99)</li><li>• Type should be either &#39;physical&#39; or &#39;digital&#39;</li><li>• Category ID must exist in the system</li></ul></div><div class="bg-blue-50 border border-blue-200 rounded-lg p-4"><h5 class="font-medium text-blue-900 mb-2">Template Format:</h5><div class="text-blue-800 text-sm space-y-2"><p><strong>Required Fields:</strong> name, description, type, category_id, price, sku</p><p><strong>Product Types:</strong></p><ul class="ml-4 space-y-1"><li>• <code>physical</code> - Tangible products requiring shipping</li><li>• <code>digital</code> - Downloadable products (ebooks, courses, etc.)</li></ul><p><strong>Boolean Fields:</strong> Use 1 for true/yes, 0 for false/no (is_featured, is_active)</p><p><strong>Digital Products:</strong> Set stock_quantity to 0, optionally set download_limit and download_expiry_days</p></div></div>',2)),t("div",G,[t("button",{onClick:e[1]||(e[1]=m=>a.value=2),class:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"}," Next: Upload File ")])])):u("",!0),a.value===2?(r(),o("div",H,[t("div",J,[e[19]||(e[19]=t("h4",{class:"font-medium text-green-900 mb-2"},"Step 2: Upload Your CSV File",-1)),e[20]||(e[20]=t("p",{class:"text-green-800 text-sm mb-4"}," Select the CSV file you've prepared with your product data. ",-1)),t("div",Q,[t("input",{ref:"fileInput",type:"file",accept:".csv,.xlsx,.xls",onChange:F,class:"hidden"},null,544),d.value?(r(),o("div",Z,[e[18]||(e[18]=t("i",{class:"fas fa-file-csv text-4xl text-green-500"},null,-1)),t("p",ee,n(d.value.name),1),t("p",te,n(T(d.value.size)),1),t("button",{onClick:P,class:"text-red-600 hover:text-red-800 text-sm"}," Remove file ")])):(r(),o("div",W,[e[15]||(e[15]=t("i",{class:"fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"},null,-1)),e[16]||(e[16]=t("p",{class:"text-gray-600 mb-2"},"Click to select a file or drag and drop",-1)),e[17]||(e[17]=t("p",{class:"text-gray-500 text-sm"},"CSV, XLSX, or XLS files only",-1)),t("button",{onClick:e[2]||(e[2]=m=>s.$refs.fileInput.click()),class:"mt-4 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"}," Select File ")]))])]),t("div",se,[t("button",{onClick:e[3]||(e[3]=m=>a.value=1),class:"px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400"}," Previous "),t("button",{onClick:I,disabled:!d.value||b.value,class:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50"},n(b.value?"Validating...":"Next: Review"),9,le)])])):u("",!0),a.value===3?(r(),o("div",oe,[t("div",re,[e[25]||(e[25]=t("h4",{class:"font-medium text-purple-900 mb-2"},"Step 3: Review & Import",-1)),e[26]||(e[26]=t("p",{class:"text-purple-800 text-sm mb-4"}," Review the validation results and proceed with the import. ",-1)),l.value?(r(),o("div",ae,[t("div",ie,[e[21]||(e[21]=t("span",{class:"text-gray-600"},"Total rows:",-1)),t("span",ne,n(l.value.total_rows),1)]),t("div",de,[e[22]||(e[22]=t("span",{class:"text-green-600"},"Valid rows:",-1)),t("span",ue,n(l.value.valid_rows),1)]),l.value.invalid_rows>0?(r(),o("div",me,[e[23]||(e[23]=t("span",{class:"text-red-600"},"Invalid rows:",-1)),t("span",pe,n(l.value.invalid_rows),1)])):u("",!0)])):u("",!0),((g=(i=l.value)==null?void 0:i.errors)==null?void 0:g.length)>0?(r(),o("div",ce,[e[24]||(e[24]=t("h5",{class:"font-medium text-red-900 mb-2"},"Validation Errors:",-1)),t("div",ve,[(r(!0),o(j,null,E(l.value.errors.slice(0,10),m=>(r(),o("div",{key:m.row,class:"text-red-800 text-sm"}," Row "+n(m.row)+": "+n(m.message),1))),128)),l.value.errors.length>10?(r(),o("div",fe," ... and "+n(l.value.errors.length-10)+" more errors ",1)):u("",!0)])])):u("",!0)]),t("div",ge,[t("button",{onClick:e[4]||(e[4]=m=>a.value=2),class:"px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400"}," Previous "),t("button",{onClick:B,disabled:!l.value||l.value.valid_rows===0||f.value,class:"px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 disabled:opacity-50"},n(f.value?"Importing...":`Import ${((_=l.value)==null?void 0:_.valid_rows)||0} Products`),9,xe)])])):u("",!0),f.value?(r(),o("div",be,[t("div",ye,[e[27]||(e[27]=t("div",{class:"flex items-center space-x-3"},[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),t("span",{class:"text-blue-800 font-medium"},"Importing products...")],-1)),c.value?(r(),o("div",we,[t("div",he,[t("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:V({width:`${c.value}%`})},null,4)]),t("p",_e,n(c.value)+"% complete",1)])):u("",!0)])])):u("",!0)])])])):u("",!0)}}};export{Ce as _};
