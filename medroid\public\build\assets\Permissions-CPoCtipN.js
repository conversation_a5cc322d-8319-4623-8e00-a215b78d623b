import{r as c,o as F,d as o,e as s,f as E,u as j,m as S,g as R,i as e,n as y,F as x,p as v,t as l,j as U,l as q,A as O,v as G,x as z,y as H,P as J}from"./vendor-BhKTHoN5.js";import{_ as K}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const Q={class:"flex items-center justify-between"},W={class:"flex mt-2","aria-label":"Breadcrumb"},X={class:"inline-flex items-center space-x-1 md:space-x-3"},Y={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},Z={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},I={class:"py-12"},ee={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},te={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},se={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},oe={class:"p-6 text-gray-900 dark:text-gray-100"},ae={key:0,class:"text-center py-4"},re={key:1,class:"space-y-3"},le={class:"font-medium"},ne={class:"text-sm text-gray-500 dark:text-gray-400"},ie={class:"flex space-x-2"},de=["onClick"],ue=["onClick"],ce={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},me={class:"p-6 text-gray-900 dark:text-gray-100"},ge={class:"flex items-center justify-between mb-4"},xe={class:"text-sm text-gray-500 dark:text-gray-400"},ye={key:0,class:"text-center py-4"},ve={key:1,class:"space-y-4"},fe={class:"font-medium mb-2 capitalize"},pe={class:"grid grid-cols-2 gap-2"},be={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},he={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},_e={class:"mt-3"},ke={class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},we={class:"mb-4"},Re={key:0,class:"mt-1 text-sm text-red-600"},Ce={class:"mb-6"},Me={class:"max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3"},Pe={class:"font-medium text-gray-900 dark:text-gray-100 mb-2 capitalize"},De={class:"grid grid-cols-2 gap-2"},Ee=["checked","onChange"],je={key:0,class:"mt-1 text-sm text-red-600"},ze={class:"flex justify-end space-x-3"},Be={type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},$e={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ve={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Le={class:"mt-3 text-center"},Ne={class:"mt-2 px-7 py-3"},Te={class:"text-sm text-gray-500 dark:text-gray-400"},He={__name:"Permissions",setup(Ae){const h=[{title:"Dashboard",href:"/dashboard"},{title:"Permissions",href:"/permissions"}],f=c(!1),C=c([]),M=c([]),_=c({}),p=c(!1),k=c(!1),m=c(null),b=c(null),d=c({name:"",permissions:[]}),u=c({}),w=async()=>{f.value=!0;try{const[a,t]=await Promise.all([window.axios.get("/roles-list"),window.axios.get("/permissions-list")]);C.value=a.data,M.value=t.data.permissions||t.data,_.value=t.data.grouped_permissions||{}}catch(a){console.error("Error fetching data:",a),alert("Error loading data. Please refresh the page.")}finally{f.value=!1}},B=()=>{m.value=null,d.value={name:"",permissions:[]},u.value={},p.value=!0},$=a=>{m.value=a,d.value={name:a.name,permissions:a.permissions?a.permissions.map(t=>t.id):[]},u.value={},p.value=!0},P=()=>{p.value=!1,m.value=null,d.value={name:"",permissions:[]},u.value={}},V=async()=>{var a,t,n,r;try{u.value={},m.value?await window.axios.put(`/roles/${m.value.id}`,d.value):await window.axios.post("/roles",d.value),P(),await w(),alert(m.value?"Role updated successfully!":"Role created successfully!")}catch(i){(t=(a=i.response)==null?void 0:a.data)!=null&&t.errors?u.value=i.response.data.errors:alert("Error saving role: "+(((r=(n=i.response)==null?void 0:n.data)==null?void 0:r.message)||i.message))}},L=a=>{b.value=a,k.value=!0},D=()=>{k.value=!1,b.value=null},N=async()=>{var a,t;try{await window.axios.delete(`/roles/${b.value.id}`),D(),await w(),alert("Role deleted successfully!")}catch(n){alert("Error deleting role: "+(((t=(a=n.response)==null?void 0:a.data)==null?void 0:t.message)||n.message))}},T=a=>{const t=d.value.permissions.indexOf(a);t>-1?d.value.permissions.splice(t,1):d.value.permissions.push(a)},A=a=>d.value.permissions.includes(a);return F(()=>{w()}),(a,t)=>(s(),o(x,null,[E(j(S),{title:"Permissions Management"}),E(K,null,{header:R(()=>[e("div",Q,[e("div",null,[t[2]||(t[2]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Permissions Management ",-1)),e("nav",W,[e("ol",X,[(s(),o(x,null,v(h,(n,r)=>e("li",{key:r,class:"inline-flex items-center"},[r<h.length-1?(s(),H(j(J),{key:0,href:n.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:R(()=>[z(l(n.title),1)]),_:2},1032,["href"])):(s(),o("span",Y,l(n.title),1)),r<h.length-1?(s(),o("svg",Z,t[1]||(t[1]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):y("",!0)])),64))])])])])]),default:R(()=>{var n;return[e("div",I,[e("div",ee,[e("div",te,[e("div",se,[e("div",oe,[e("div",{class:"flex items-center justify-between mb-4"},[t[3]||(t[3]=e("h3",{class:"text-lg font-medium"},"Roles",-1)),e("button",{onClick:B,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm"}," Add Role ")]),f.value?(s(),o("div",ae,t[4]||(t[4]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(s(),o("div",re,[(s(!0),o(x,null,v(C.value,r=>(s(),o("div",{key:r.id,class:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"},[e("div",null,[e("h4",le,l(r.display_name),1),e("p",ne,l(r.permissions_count)+" permissions ",1)]),e("div",ie,[e("button",{onClick:i=>$(r),class:"text-blue-600 hover:text-blue-900 text-sm"},"Edit",8,de),r.name!=="admin"?(s(),o("button",{key:0,onClick:i=>L(r),class:"text-red-600 hover:text-red-900 text-sm"},"Delete",8,ue)):y("",!0)])]))),128))]))])]),e("div",ce,[e("div",me,[e("div",ge,[t[5]||(t[5]=e("h3",{class:"text-lg font-medium"},"Permissions",-1)),e("div",xe,l(M.value.length)+" total permissions ",1)]),f.value?(s(),o("div",ye,t[6]||(t[6]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):(s(),o("div",ve,[(s(!0),o(x,null,v(_.value,(r,i)=>(s(),o("div",{key:i,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("h4",fe,l(i),1),e("div",pe,[(s(!0),o(x,null,v(r,g=>(s(),o("div",{key:g.id,class:"text-sm text-gray-600 dark:text-gray-400"},l(g.name),1))),128))])]))),128))]))])])]),t[7]||(t[7]=e("div",{class:"mt-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},[e("div",{class:"p-6 text-gray-900 dark:text-gray-100"},[e("h3",{class:"text-lg font-medium mb-4"},"Role-Permission Matrix"),e("div",{class:"text-center py-8 text-gray-500 dark:text-gray-400"},[e("i",{class:"fas fa-cogs text-4xl mb-4"}),e("p",null,"Role-Permission matrix will be implemented here"),e("p",{class:"text-sm"},"This will allow you to assign permissions to roles")])])],-1))])]),p.value?(s(),o("div",be,[e("div",he,[e("div",_e,[e("h3",ke,l(m.value?"Edit Role":"Create New Role"),1),e("form",{onSubmit:U(V,["prevent"])},[e("div",we,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Role Name ",-1)),q(e("input",{"onUpdate:modelValue":t[0]||(t[0]=r=>d.value.name=r),type:"text",class:O(["w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100",{"border-red-500":u.value.name}]),placeholder:"Enter role name",required:""},null,2),[[G,d.value.name]]),u.value.name?(s(),o("p",Re,l(u.value.name[0]),1)):y("",!0)]),e("div",Ce,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Permissions ",-1)),e("div",Me,[(s(!0),o(x,null,v(_.value,(r,i)=>(s(),o("div",{key:i,class:"mb-4"},[e("h4",Pe,l(i),1),e("div",De,[(s(!0),o(x,null,v(r,g=>(s(),o("label",{key:g.id,class:"flex items-center text-sm text-gray-700 dark:text-gray-300"},[e("input",{type:"checkbox",checked:A(g.id),onChange:Fe=>T(g.id),class:"mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,40,Ee),z(" "+l(g.action),1)]))),128))])]))),128))]),u.value.permissions?(s(),o("p",je,l(u.value.permissions[0]),1)):y("",!0)]),e("div",ze,[e("button",{type:"button",onClick:P,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Cancel "),e("button",Be,l(m.value?"Update Role":"Create Role"),1)])],32)])])])):y("",!0),k.value?(s(),o("div",$e,[e("div",Ve,[e("div",Le,[t[10]||(t[10]=e("div",{class:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100"},[e("svg",{class:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mt-2"},"Delete Role",-1)),e("div",Ne,[e("p",Te,' Are you sure you want to delete the role "'+l((n=b.value)==null?void 0:n.display_name)+'"? This action cannot be undone. ',1)]),e("div",{class:"flex justify-center space-x-3 mt-4"},[e("button",{onClick:D,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Cancel "),e("button",{onClick:N,class:"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"}," Delete Role ")])])])])):y("",!0)]}),_:1})],64))}};export{He as default};
