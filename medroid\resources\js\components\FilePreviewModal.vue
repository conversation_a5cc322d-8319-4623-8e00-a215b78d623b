<template>
    <div v-if="isOpen" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-50" @click="closeModal">
        <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="mt-3">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900 truncate">
                            {{ file?.name || 'File Preview' }}
                        </h3>
                        <p class="text-sm text-gray-500 mt-1">
                            {{ file?.category_display_name }} • {{ formatFileSize(file?.size || 0) }}
                            <span v-if="file?.created_at"> • {{ formatDate(file.created_at) }}</span>
                        </p>
                    </div>
                    <div class="flex items-center space-x-2 ml-4">
                        <!-- Download Button -->
                        <button
                            @click="downloadFile"
                            :disabled="downloading"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            <svg v-if="!downloading" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m5-8H7a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                            </svg>
                            <div v-else class="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                            {{ downloading ? 'Downloading...' : 'Download' }}
                        </button>
                        
                        <!-- Edit Button -->
                        <button
                            @click="editFile"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit
                        </button>
                        
                        <!-- Delete Button -->
                        <button
                            @click="deleteFile"
                            class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete
                        </button>
                        
                        <!-- Close Button -->
                        <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Preview Content -->
                <div class="bg-gray-50 rounded-lg overflow-hidden" style="height: 70vh;">
                    <!-- Image Preview -->
                    <div v-if="file?.is_image" class="h-full flex items-center justify-center bg-black">
                        <img 
                            :src="file.url" 
                            :alt="file.name"
                            class="max-w-full max-h-full object-contain"
                            @load="imageLoaded = true"
                            @error="imageError = true"
                        >
                        <div v-if="!imageLoaded && !imageError" class="text-white">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
                            <p class="mt-4">Loading image...</p>
                        </div>
                        <div v-if="imageError" class="text-white text-center">
                            <svg class="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                            </svg>
                            <p>Failed to load image</p>
                        </div>
                    </div>

                    <!-- Video Preview -->
                    <div v-else-if="file?.is_video" class="h-full flex items-center justify-center bg-black">
                        <video 
                            :src="file.url" 
                            controls 
                            class="max-w-full max-h-full"
                            @error="videoError = true"
                        >
                            Your browser does not support the video tag.
                        </video>
                        <div v-if="videoError" class="text-white text-center">
                            <svg class="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path>
                            </svg>
                            <p>Failed to load video</p>
                        </div>
                    </div>

                    <!-- Document/Other File Preview -->
                    <div v-else class="h-full flex items-center justify-center">
                        <div class="text-center">
                            <div class="mb-4">
                                <!-- PDF Icon -->
                                <svg v-if="file?.extension === 'pdf'" class="w-20 h-20 mx-auto text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                </svg>
                                <!-- Word Document Icon -->
                                <svg v-else-if="['doc', 'docx'].includes(file?.extension)" class="w-20 h-20 mx-auto text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                </svg>
                                <!-- Generic File Icon -->
                                <svg v-else class="w-20 h-20 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ file?.name }}</h3>
                            <p class="text-gray-500 mb-4">{{ file?.extension?.toUpperCase() }} File</p>
                            <button
                                @click="downloadFile"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m5-8H7a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                                </svg>
                                Download to View
                            </button>
                        </div>
                    </div>
                </div>

                <!-- File Details -->
                <div class="mt-4 bg-gray-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-500">Original Name:</span>
                            <p class="text-gray-900 truncate">{{ file?.original_name || file?.name }}</p>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">File Type:</span>
                            <p class="text-gray-900">{{ file?.extension?.toUpperCase() || 'Unknown' }}</p>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">Category:</span>
                            <p class="text-gray-900">{{ file?.category_display_name || 'General' }}</p>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">Downloads:</span>
                            <p class="text-gray-900">{{ file?.download_count || 0 }}</p>
                        </div>
                        <div v-if="file?.description" class="md:col-span-4">
                            <span class="font-medium text-gray-500">Description:</span>
                            <p class="text-gray-900">{{ file.description }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ref } from 'vue'
import axios from 'axios'

export default {
    name: 'FilePreviewModal',
    props: {
        isOpen: {
            type: Boolean,
            default: false
        },
        file: {
            type: Object,
            default: null
        }
    },
    emits: ['close', 'edit', 'delete', 'download'],
    setup(props, { emit }) {
        const downloading = ref(false)
        const imageLoaded = ref(false)
        const imageError = ref(false)
        const videoError = ref(false)

        const closeModal = () => {
            imageLoaded.value = false
            imageError.value = false
            videoError.value = false
            emit('close')
        }

        const downloadFile = async () => {
            if (!props.file) return

            downloading.value = true
            try {
                // Get secure download URL
                const response = await axios.get(`/web-api/files/${props.file.id}/secure-url`)
                const downloadUrl = response.data.url

                // Create temporary link and trigger download
                const link = document.createElement('a')
                link.href = downloadUrl
                link.download = props.file.original_name || props.file.name
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)

                emit('download', props.file)
            } catch (error) {
                console.error('Download failed:', error)
                // Fallback to direct URL
                const link = document.createElement('a')
                link.href = props.file.url
                link.download = props.file.original_name || props.file.name
                link.target = '_blank'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            } finally {
                downloading.value = false
            }
        }

        const editFile = () => {
            emit('edit', props.file)
        }

        const deleteFile = async () => {
            if (!props.file) return

            const confirmed = confirm(`Are you sure you want to delete "${props.file.name}"?`)
            if (confirmed) {
                emit('delete', props.file)
            }
        }

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes'
            const k = 1024
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        const formatDate = (dateString) => {
            const date = new Date(dateString)
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
        }

        return {
            downloading,
            imageLoaded,
            imageError,
            videoError,
            closeModal,
            downloadFile,
            editFile,
            deleteFile,
            formatFileSize,
            formatDate
        }
    }
}
</script>
