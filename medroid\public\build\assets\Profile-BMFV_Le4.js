import{J as _,r as V,G as _e,K as w,u as s,o as W,y as h,e as c,g as n,M,n as O,ak as we,f as l,ae as he,af as Ce,c as T,i as e,d as x,A as re,ad as ae,x as y,z as Be,m as $e,t as z,j as Q,P as Me,al as Pe,l as S,R as De,F as U,p as R,q as Ve,v as ee,S as q,s as te,a as se}from"./vendor-BhKTHoN5.js";import{_ as Oe}from"./HeadingSmall.vue_vue_type_script_setup_true_lang-DVe5uQrS.js";import{_ as H}from"./InputError.vue_vue_type_script_setup_true_lang-B3hvUGHW.js";import{_ as j}from"./index-CFmBC9d8.js";import{c as ze,u as G,d as X,P as le,a as ne,b as de}from"./useForwardPropsEmits-DFe9BlYF.js";import{u as Ae,a as E,f as Ee}from"./useForwardExpose-DjhPD9_V.js";import{P as K,c as I}from"./Primitive-DSQomZit.js";import{g as oe,b as Fe,c as je,_ as Le,a as Se,u as Ie}from"./useBodyScrollLock-CcBLBbmp.js";import{c as Ue}from"./createLucideIcon-YxmScYOV.js";import{_ as P,a as D}from"./Label.vue_vue_type_script_setup_true_lang-dYaAjAby.js";import{_ as Re}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{_ as Te}from"./Layout.vue_vue_type_script_setup_true_lang-CjtMa_dt.js";import"./index-CGRqDMLC.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/**
 * @license lucide-vue-next v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=Ue("XIcon",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),[F,Ne]=ze("DialogRoot"),qe=_({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(p,{emit:u}){const o=p,r=Ae(o,"open",u,{defaultValue:o.defaultOpen,passive:o.open===void 0}),f=V(),m=V(),{modal:g}=_e(o);return Ne({open:r,modal:g,openModal:()=>{r.value=!0},onOpenChange:b=>{r.value=b},onOpenToggle:()=>{r.value=!r.value},contentId:"",titleId:"",descriptionId:"",triggerElement:f,contentElement:m}),(b,v)=>w(b.$slots,"default",{open:s(r)})}}),He=_({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(p){const u=p,o=F(),{forwardRef:i,currentElement:r}=E();return o.contentId||(o.contentId=G(void 0,"reka-dialog-content")),W(()=>{o.triggerElement.value=r.value}),(f,m)=>(c(),h(s(K),M(u,{ref:s(i),type:f.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":s(o).open.value||!1,"aria-controls":s(o).open.value?s(o).contentId:void 0,"data-state":s(o).open.value?"open":"closed",onClick:s(o).onOpenToggle}),{default:n(()=>[w(f.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),Ge=_({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(p){const u=Ee();return(o,i)=>s(u)||o.forceMount?(c(),h(we,{key:0,to:o.to,disabled:o.disabled,defer:o.defer},[w(o.$slots,"default")],8,["to","disabled","defer"])):O("",!0)}}),ie=_({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(p,{emit:u}){const o=p,i=u,r=F(),{forwardRef:f,currentElement:m}=E();return r.titleId||(r.titleId=G(void 0,"reka-dialog-title")),r.descriptionId||(r.descriptionId=G(void 0,"reka-dialog-description")),W(()=>{r.contentElement=m,oe()!==document.body&&(r.triggerElement.value=oe())}),(g,b)=>(c(),h(s(Le),{"as-child":"",loop:"",trapped:o.trapFocus,onMountAutoFocus:b[5]||(b[5]=v=>i("openAutoFocus",v)),onUnmountAutoFocus:b[6]||(b[6]=v=>i("closeAutoFocus",v))},{default:n(()=>[l(s(Fe),M({id:s(r).contentId,ref:s(f),as:g.as,"as-child":g.asChild,"disable-outside-pointer-events":g.disableOutsidePointerEvents,role:"dialog","aria-describedby":s(r).descriptionId,"aria-labelledby":s(r).titleId,"data-state":s(je)(s(r).open.value)},g.$attrs,{onDismiss:b[0]||(b[0]=v=>s(r).onOpenChange(!1)),onEscapeKeyDown:b[1]||(b[1]=v=>i("escapeKeyDown",v)),onFocusOutside:b[2]||(b[2]=v=>i("focusOutside",v)),onInteractOutside:b[3]||(b[3]=v=>i("interactOutside",v)),onPointerDownOutside:b[4]||(b[4]=v=>i("pointerDownOutside",v))}),{default:n(()=>[w(g.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),We=_({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(p,{emit:u}){const o=p,i=u,r=F(),f=X(i),{forwardRef:m,currentElement:g}=E();return Se(g),(b,v)=>(c(),h(ie,M({...o,...s(f)},{ref:s(m),"trap-focus":s(r).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:v[0]||(v[0]=d=>{var B;d.defaultPrevented||(d.preventDefault(),(B=s(r).triggerElement.value)==null||B.focus())}),onPointerDownOutside:v[1]||(v[1]=d=>{const B=d.detail.originalEvent,L=B.button===0&&B.ctrlKey===!0;(B.button===2||L)&&d.preventDefault()}),onFocusOutside:v[2]||(v[2]=d=>{d.preventDefault()})}),{default:n(()=>[w(b.$slots,"default")]),_:3},16,["trap-focus"]))}}),Xe=_({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(p,{emit:u}){const o=p,r=X(u);E();const f=F(),m=V(!1),g=V(!1);return(b,v)=>(c(),h(ie,M({...o,...s(r)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:v[0]||(v[0]=d=>{var B;d.defaultPrevented||(m.value||(B=s(f).triggerElement.value)==null||B.focus(),d.preventDefault()),m.value=!1,g.value=!1}),onInteractOutside:v[1]||(v[1]=d=>{var A;d.defaultPrevented||(m.value=!0,d.detail.originalEvent.type==="pointerdown"&&(g.value=!0));const B=d.target;((A=s(f).triggerElement.value)==null?void 0:A.contains(B))&&d.preventDefault(),d.detail.originalEvent.type==="focusin"&&g.value&&d.preventDefault()})}),{default:n(()=>[w(b.$slots,"default")]),_:3},16))}}),Ye=_({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(p,{emit:u}){const o=p,i=u,r=F(),f=X(i),{forwardRef:m}=E();return(g,b)=>(c(),h(s(le),{present:g.forceMount||s(r).open.value},{default:n(()=>[s(r).modal.value?(c(),h(We,M({key:0,ref:s(m)},{...o,...s(f),...g.$attrs}),{default:n(()=>[w(g.$slots,"default")]),_:3},16)):(c(),h(Xe,M({key:1,ref:s(m)},{...o,...s(f),...g.$attrs}),{default:n(()=>[w(g.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Je=_({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(p){const u=F();return Ie(!0),E(),(o,i)=>(c(),h(s(K),{as:o.as,"as-child":o.asChild,"data-state":s(u).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:n(()=>[w(o.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Ze=_({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(p){const u=F(),{forwardRef:o}=E();return(i,r)=>{var f;return(f=s(u))!=null&&f.modal.value?(c(),h(s(le),{key:0,present:i.forceMount||s(u).open.value},{default:n(()=>[l(Je,M(i.$attrs,{ref:s(o),as:i.as,"as-child":i.asChild}),{default:n(()=>[w(i.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):O("",!0)}}}),ue=_({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(p){const u=p;E();const o=F();return(i,r)=>(c(),h(s(K),M(u,{type:i.as==="button"?"button":void 0,onClick:r[0]||(r[0]=f=>s(o).onOpenChange(!1))}),{default:n(()=>[w(i.$slots,"default")]),_:3},16,["type"]))}}),Qe=_({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(p){const u=p,o=F();return E(),(i,r)=>(c(),h(s(K),M(u,{id:s(o).titleId}),{default:n(()=>[w(i.$slots,"default")]),_:3},16,["id"]))}}),et=_({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(p){const u=p;E();const o=F();return(i,r)=>(c(),h(s(K),M(u,{id:s(o).descriptionId}),{default:n(()=>[w(i.$slots,"default")]),_:3},16,["id"]))}}),tt=_({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(p){const u=p;return(o,i)=>(c(),h(s(Ge),he(Ce(u)),{default:n(()=>[w(o.$slots,"default")]),_:3},16))}}),st=_({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(p,{emit:u}){const r=ne(p,u);return(f,m)=>(c(),h(s(qe),M({"data-slot":"dialog"},s(r)),{default:n(()=>[w(f.$slots,"default")]),_:3},16))}}),ot=_({__name:"DialogClose",props:{asChild:{type:Boolean},as:{}},setup(p){const u=p;return(o,i)=>(c(),h(s(ue),M({"data-slot":"dialog-close"},u),{default:n(()=>[w(o.$slots,"default")]),_:3},16))}}),rt=_({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(p){const u=p,o=T(()=>{const{class:i,...r}=u;return r});return(i,r)=>(c(),h(s(Ze),M({"data-slot":"dialog-overlay"},o.value,{class:s(I)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-white/20 backdrop-blur-sm",u.class)}),{default:n(()=>[w(i.$slots,"default")]),_:3},16,["class"]))}}),at=_({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(p,{emit:u}){const o=p,i=u,r=T(()=>{const{class:m,...g}=o;return g}),f=ne(r,i);return(m,g)=>(c(),h(s(tt),null,{default:n(()=>[l(rt),l(s(Ye),M({"data-slot":"dialog-content"},s(f),{class:s(I)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",o.class)}),{default:n(()=>[w(m.$slots,"default"),l(s(ue),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4"},{default:n(()=>[l(s(Ke)),g[0]||(g[0]=e("span",{class:"sr-only"},"Close",-1))]),_:1})]),_:3},16,["class"])]),_:3}))}}),lt=_({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(p){const u=p,o=T(()=>{const{class:r,...f}=u;return f}),i=de(o);return(r,f)=>(c(),h(s(et),M({"data-slot":"dialog-description"},s(i),{class:s(I)("text-muted-foreground text-sm",u.class)}),{default:n(()=>[w(r.$slots,"default")]),_:3},16,["class"]))}}),nt=_({__name:"DialogFooter",props:{class:{}},setup(p){const u=p;return(o,i)=>(c(),x("div",{"data-slot":"dialog-footer",class:re(s(I)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",u.class))},[w(o.$slots,"default")],2))}}),dt=_({__name:"DialogHeader",props:{class:{}},setup(p){const u=p;return(o,i)=>(c(),x("div",{"data-slot":"dialog-header",class:re(s(I)("flex flex-col gap-2 text-center sm:text-left",u.class))},[w(o.$slots,"default")],2))}}),it=_({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(p){const u=p,o=T(()=>{const{class:r,...f}=u;return f}),i=de(o);return(r,f)=>(c(),h(s(Qe),M({"data-slot":"dialog-title"},s(i),{class:s(I)("text-lg leading-none font-semibold",u.class)}),{default:n(()=>[w(r.$slots,"default")]),_:3},16,["class"]))}}),ut=_({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{}},setup(p){const u=p;return(o,i)=>(c(),h(s(He),M({"data-slot":"dialog-trigger"},u),{default:n(()=>[w(o.$slots,"default")]),_:3},16))}}),ct={class:"space-y-6"},pt={class:"space-y-4 rounded-lg border border-red-200 bg-red-50/50 p-4 dark:border-red-200/10 dark:bg-red-700/10"},mt={class:"grid gap-2"},ft=_({__name:"DeleteUser",setup(p){const u=V(null),o=ae({password:""}),i=f=>{f.preventDefault(),o.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>r(),onError:()=>{var m;return(m=u.value)==null?void 0:m.focus()},onFinish:()=>o.reset()})},r=()=>{o.clearErrors(),o.reset()};return(f,m)=>(c(),x("div",ct,[l(Oe,{title:"Delete account",description:"Delete your account and all of its resources"}),e("div",pt,[m[7]||(m[7]=e("div",{class:"relative space-y-0.5 text-red-600 dark:text-red-100"},[e("p",{class:"font-medium"},"Warning"),e("p",{class:"text-sm"},"Please proceed with caution, this cannot be undone.")],-1)),l(s(st),null,{default:n(()=>[l(s(ut),{"as-child":""},{default:n(()=>[l(s(j),{variant:"destructive"},{default:n(()=>m[1]||(m[1]=[y("Delete account")])),_:1})]),_:1}),l(s(at),null,{default:n(()=>[e("form",{class:"space-y-6",onSubmit:i},[l(s(dt),{class:"space-y-3"},{default:n(()=>[l(s(it),null,{default:n(()=>m[2]||(m[2]=[y("Are you sure you want to delete your account?")])),_:1}),l(s(lt),null,{default:n(()=>m[3]||(m[3]=[y(" Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")])),_:1})]),_:1}),e("div",mt,[l(s(P),{for:"password",class:"sr-only"},{default:n(()=>m[4]||(m[4]=[y("Password")])),_:1}),l(s(D),{id:"password",type:"password",name:"password",ref_key:"passwordInput",ref:u,modelValue:s(o).password,"onUpdate:modelValue":m[0]||(m[0]=g=>s(o).password=g),placeholder:"Password"},null,8,["modelValue"]),l(H,{message:s(o).errors.password},null,8,["message"])]),l(s(nt),{class:"gap-2"},{default:n(()=>[l(s(ot),{"as-child":""},{default:n(()=>[l(s(j),{variant:"secondary",onClick:r},{default:n(()=>m[5]||(m[5]=[y(" Cancel ")])),_:1})]),_:1}),l(s(j),{type:"submit",variant:"destructive",disabled:s(o).processing},{default:n(()=>m[6]||(m[6]=[y(" Delete account ")])),_:1},8,["disabled"])]),_:1})],32)]),_:1})]),_:1})])]))}}),gt={class:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 mb-8"},vt={class:"flex items-center space-x-4"},yt={class:"text-2xl font-bold text-gray-900 dark:text-gray-100"},bt={class:"text-gray-600 dark:text-gray-400 capitalize"},xt={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"},kt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},_t={class:"space-y-2"},wt={class:"space-y-2"},ht={key:0,class:"bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4"},Ct={class:"flex items-start"},Bt={class:"ml-3"},$t={class:"text-sm text-amber-800 dark:text-amber-200"},Mt={key:0,class:"mt-2 text-sm font-medium text-green-600 dark:text-green-400"},Pt={class:"flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700"},Dt={class:"flex items-center space-x-4"},Vt={key:0,class:"flex items-center"},Ot={key:1},zt={class:"flex items-center text-green-600 dark:text-green-400"},At={key:0,class:"space-y-6"},Et={key:0,class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8"},Ft={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4"},jt={class:"flex"},Lt={class:"ml-3"},St={class:"text-sm text-red-800 dark:text-red-200"},It={key:2,class:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4"},Ut={class:"flex"},Rt={class:"ml-3"},Tt={class:"text-sm text-green-800 dark:text-green-200"},Kt={key:3},Nt={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"},qt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ht={class:"space-y-2"},Gt={class:"space-y-2"},Wt={class:"space-y-2"},Xt=["value"],Yt={class:"md:col-span-2 space-y-6 mt-6"},Jt={class:"space-y-2"},Zt={class:"space-y-2"},Qt={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"},es={class:"flex flex-wrap gap-3 mb-6"},ts=["onClick"],ss={class:"flex gap-3"},os={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"},rs={class:"border border-gray-200 rounded-lg p-4 mb-4"},as={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ls={class:"md:col-span-2"},ns={class:"flex items-center"},ds={key:0,class:"space-y-3"},is={class:"flex-1"},us={class:"flex items-center"},cs={class:"text-sm font-medium text-gray-900"},ps={key:0,class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},ms={class:"text-sm text-gray-600"},fs={class:"flex items-center space-x-2"},gs=["onClick"],vs=["onClick"],ys={key:1,class:"text-center py-4 text-gray-500"},bs={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"},xs={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"},ks={class:"mb-4"},_s={class:"flex items-center mb-3"},ws={key:0},hs={class:"flex gap-2 mb-3"},Cs={key:0,class:"flex flex-wrap gap-2"},Bs=["onClick"],$s={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Ms={class:"space-y-2 mb-4"},Ps={class:"text-sm"},Ds=["onClick"],Vs={class:"flex gap-2"},Os={class:"bg-gray-50 dark:bg-gray-900/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700"},zs={class:"flex items-center justify-between"},As={key:0,class:"flex items-center"},Es={key:1,class:"flex items-center"},Fs={class:"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-red-200 dark:border-red-800 p-6"},Ys=_({__name:"Profile",props:{mustVerifyEmail:{type:Boolean},status:{}},setup(p){const u=[{title:"Profile settings",href:"/settings/profile"}],i=Be().props.auth.user,r=ae({name:i.name,email:i.email}),f=T(()=>i.role==="provider"),m=V(!1),g=V(!1),b=V(null),v=V(""),d=V({specialization:"",bio:"",education:"",license_number:"",gender:"",languages:[],practice_locations:[],accepts_insurance:!1,insurance_providers:[],pricing:{consultation:0,follow_up:0},certifications:[]}),B=V(""),L=V(""),A=V(""),$=V({address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1}),ce=[{value:"male",label:"Male"},{value:"female",label:"Female"},{value:"other",label:"Other"}],pe=()=>{r.patch(route("profile.update"),{preserveScroll:!0})},me=async()=>{var C,t;if(f.value){m.value=!0;try{const a=await se.get("/provider/get-profile");if(a.data){const k=a.data;d.value={specialization:k.specialization||"",bio:k.bio||"",education:k.education||"",license_number:k.license_number||"",gender:k.gender||"",languages:k.languages||[],practice_locations:k.practice_locations||[],accepts_insurance:k.accepts_insurance||!1,insurance_providers:k.insurance_providers||[],pricing:{consultation:((C=k.pricing)==null?void 0:C.consultation)||k.consultation_fee||0,follow_up:((t=k.pricing)==null?void 0:t.follow_up)||0},certifications:k.certifications||[]}}}catch(a){console.error("Error fetching provider profile:",a),b.value="Failed to load provider profile data"}finally{m.value=!1}}},fe=async()=>{var C,t;g.value=!0,b.value=null,v.value="";try{await se.post("/provider/profile",d.value),v.value="Provider profile updated successfully!",setTimeout(()=>{v.value=""},3e3)}catch(a){console.error("Error saving provider profile:",a),b.value=((t=(C=a.response)==null?void 0:C.data)==null?void 0:t.message)||"Failed to save provider profile"}finally{g.value=!1}},Y=()=>{B.value.trim()&&!d.value.languages.includes(B.value.trim())&&(d.value.languages.push(B.value.trim()),B.value="")},ge=C=>{d.value.languages.splice(C,1)},J=()=>{L.value.trim()&&(d.value.certifications.push(L.value.trim()),L.value="")},ve=C=>{d.value.certifications.splice(C,1)},ye=()=>{$.value.address.trim()&&(d.value.practice_locations.push({address:$.value.address.trim(),city:$.value.city.trim(),state:$.value.state.trim(),zip_code:$.value.zip_code.trim(),coordinates:$.value.coordinates,is_primary:$.value.is_primary||d.value.practice_locations.length===0}),$.value={address:"",city:"",state:"",zip_code:"",coordinates:[0,0],is_primary:!1})},be=C=>{d.value.practice_locations.splice(C,1)},xe=C=>{d.value.practice_locations.forEach((t,a)=>{t.is_primary=a===C})},Z=()=>{A.value.trim()&&!d.value.insurance_providers.includes(A.value.trim())&&(d.value.insurance_providers.push(A.value.trim()),A.value="")},ke=C=>{d.value.insurance_providers.splice(C,1)};return W(()=>{f.value&&me()}),(C,t)=>(c(),h(Re,{breadcrumbs:u},{default:n(()=>[l(s($e),{title:"Profile settings"}),l(Te,null,{default:n(()=>[e("div",gt,[e("div",vt,[t[19]||(t[19]=e("div",{class:"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[e("h1",yt,z(s(i).name),1),e("p",bt,z(s(i).role)+" Account",1),t[18]||(t[18]=e("div",{class:"flex items-center mt-1"},[e("div",{class:"w-2 h-2 bg-green-500 rounded-full mr-2"}),e("span",{class:"text-sm text-green-600 dark:text-green-400"},"Active")],-1))])])]),e("div",xt,[t[27]||(t[27]=e("div",{class:"flex items-center mb-6"},[e("div",{class:"w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",null,[e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"Basic Information"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Update your personal details and contact information")])],-1)),e("form",{onSubmit:Q(pe,["prevent"]),class:"space-y-6"},[e("div",kt,[e("div",_t,[l(s(P),{for:"name",class:"text-sm font-medium text-gray-700 dark:text-gray-300"},{default:n(()=>t[20]||(t[20]=[y("Full Name")])),_:1}),l(s(D),{id:"name",class:"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100",modelValue:s(r).name,"onUpdate:modelValue":t[0]||(t[0]=a=>s(r).name=a),required:"",autocomplete:"name",placeholder:"Enter your full name"},null,8,["modelValue"]),l(H,{class:"mt-1",message:s(r).errors.name},null,8,["message"])]),e("div",wt,[l(s(P),{for:"email",class:"text-sm font-medium text-gray-700 dark:text-gray-300"},{default:n(()=>t[21]||(t[21]=[y("Email Address")])),_:1}),l(s(D),{id:"email",type:"email",class:"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100",modelValue:s(r).email,"onUpdate:modelValue":t[1]||(t[1]=a=>s(r).email=a),required:"",autocomplete:"username",placeholder:"Enter your email address"},null,8,["modelValue"]),l(H,{class:"mt-1",message:s(r).errors.email},null,8,["message"])])]),C.mustVerifyEmail&&!s(i).email_verified_at?(c(),x("div",ht,[e("div",Ct,[t[24]||(t[24]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-amber-600 dark:text-amber-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",Bt,[e("p",$t,[t[23]||(t[23]=y(" Your email address is unverified. ")),l(s(Me),{href:C.route("verification.send"),method:"post",as:"button",class:"font-medium text-amber-900 dark:text-amber-100 underline hover:no-underline"},{default:n(()=>t[22]||(t[22]=[y(" Click here to resend the verification email. ")])),_:1},8,["href"])]),C.status==="verification-link-sent"?(c(),x("div",Mt," ✓ A new verification link has been sent to your email address. ")):O("",!0)])])])):O("",!0),e("div",Pt,[e("div",Dt,[l(s(j),{disabled:s(r).processing,class:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200"},{default:n(()=>[s(r).processing?(c(),x("div",Vt,t[25]||(t[25]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),y(" Saving... ")]))):(c(),x("span",Ot,"Save Changes"))]),_:1},8,["disabled"]),l(Pe,{"enter-active-class":"transition ease-in-out duration-300","enter-from-class":"opacity-0 transform translate-x-2","leave-active-class":"transition ease-in-out duration-300","leave-to-class":"opacity-0 transform translate-x-2"},{default:n(()=>[S(e("div",zt,t[26]||(t[26]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),e("span",{class:"text-sm font-medium"},"Saved successfully!",-1)]),512),[[De,s(r).recentlySuccessful]])]),_:1})])])],32)]),f.value?(c(),x("div",At,[t[63]||(t[63]=e("div",{class:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6"},[e("div",{class:"flex items-center space-x-4"},[e("div",{class:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",null,[e("h2",{class:"text-xl font-bold text-gray-900 dark:text-gray-100"},"Provider Settings"),e("p",{class:"text-gray-600 dark:text-gray-400"},"Manage your professional profile and practice information")])])],-1)),m.value?(c(),x("div",Et,t[28]||(t[28]=[e("div",{class:"flex items-center justify-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),e("span",{class:"ml-3 text-gray-600 dark:text-gray-400"},"Loading provider settings...")],-1)]))):O("",!0),b.value?(c(),x("div",Ft,[e("div",jt,[t[29]||(t[29]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-500 dark:text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",Lt,[e("p",St,z(b.value),1)])])])):O("",!0),v.value?(c(),x("div",It,[e("div",Ut,[t[30]||(t[30]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-green-500 dark:text-green-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),e("div",Rt,[e("p",Tt,z(v.value),1)])])])):O("",!0),m.value?O("",!0):(c(),x("div",Kt,[e("form",{onSubmit:Q(fe,["prevent"]),class:"space-y-6"},[e("div",Nt,[t[37]||(t[37]=e("div",{class:"flex items-center mb-6"},[e("div",{class:"w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"Professional Information"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Your medical credentials and specialization")])],-1)),e("div",qt,[e("div",Ht,[l(s(P),{for:"specialization",class:"text-sm font-medium text-gray-700 dark:text-gray-300"},{default:n(()=>t[31]||(t[31]=[y("Specialization *")])),_:1}),l(s(D),{id:"specialization",modelValue:d.value.specialization,"onUpdate:modelValue":t[2]||(t[2]=a=>d.value.specialization=a),placeholder:"e.g., Cardiology, Dermatology",required:"",class:"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"},null,8,["modelValue"])]),e("div",Gt,[l(s(P),{for:"license_number",class:"text-sm font-medium text-gray-700 dark:text-gray-300"},{default:n(()=>t[32]||(t[32]=[y("License Number")])),_:1}),l(s(D),{id:"license_number",modelValue:d.value.license_number,"onUpdate:modelValue":t[3]||(t[3]=a=>d.value.license_number=a),placeholder:"Medical license number",class:"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"},null,8,["modelValue"])]),e("div",Wt,[l(s(P),{for:"gender",class:"text-sm font-medium text-gray-700 dark:text-gray-300"},{default:n(()=>t[33]||(t[33]=[y("Gender")])),_:1}),S(e("select",{id:"gender","onUpdate:modelValue":t[4]||(t[4]=a=>d.value.gender=a),class:"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"},[t[34]||(t[34]=e("option",{value:""},"Select Gender",-1)),(c(),x(U,null,R(ce,a=>e("option",{key:a.value,value:a.value},z(a.label),9,Xt)),64))],512),[[Ve,d.value.gender]])])]),e("div",Yt,[e("div",Jt,[l(s(P),{for:"education",class:"text-sm font-medium text-gray-700 dark:text-gray-300"},{default:n(()=>t[35]||(t[35]=[y("Education & Training")])),_:1}),S(e("textarea",{id:"education","onUpdate:modelValue":t[5]||(t[5]=a=>d.value.education=a),rows:"3",placeholder:"Medical school, residency, fellowships...",class:"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100 resize-none"},null,512),[[ee,d.value.education]])]),e("div",Zt,[l(s(P),{for:"bio",class:"text-sm font-medium text-gray-700 dark:text-gray-300"},{default:n(()=>t[36]||(t[36]=[y("Professional Bio")])),_:1}),S(e("textarea",{id:"bio","onUpdate:modelValue":t[6]||(t[6]=a=>d.value.bio=a),rows:"4",placeholder:"Tell patients about yourself, your approach to care, and your experience...",class:"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100 resize-none"},null,512),[[ee,d.value.bio]])])])]),e("div",Qt,[t[40]||(t[40]=e("div",{class:"flex items-center mb-6"},[e("div",{class:"w-10 h-10 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-indigo-600 dark:text-indigo-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"Languages Spoken"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Languages you can communicate with patients in")])],-1)),e("div",es,[(c(!0),x(U,null,R(d.value.languages,(a,k)=>(c(),x("span",{key:k,class:"inline-flex items-center px-4 py-2 rounded-full text-sm bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200 border border-indigo-200 dark:border-indigo-700"},[y(z(a)+" ",1),e("button",{onClick:N=>ge(k),type:"button",class:"ml-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-200 transition-colors"},t[38]||(t[38]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,ts)]))),128))]),e("div",ss,[l(s(D),{modelValue:B.value,"onUpdate:modelValue":t[7]||(t[7]=a=>B.value=a),onKeyup:q(Y,["enter"]),placeholder:"Add a language (e.g., English, Spanish)",class:"flex-1 px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"},null,8,["modelValue"]),l(s(j),{onClick:Y,type:"button",class:"px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-medium transition-colors"},{default:n(()=>t[39]||(t[39]=[y(" Add ")])),_:1})])]),e("div",os,[t[49]||(t[49]=e("div",{class:"flex items-center mb-6"},[e("div",{class:"w-10 h-10 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-emerald-600 dark:text-emerald-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"Practice Locations"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Where you provide medical services")])],-1)),e("div",rs,[t[47]||(t[47]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Add New Location",-1)),e("div",as,[e("div",ls,[l(s(P),{for:"new_address"},{default:n(()=>t[41]||(t[41]=[y("Address")])),_:1}),l(s(D),{id:"new_address",modelValue:$.value.address,"onUpdate:modelValue":t[8]||(t[8]=a=>$.value.address=a),placeholder:"Street address",class:"mt-1"},null,8,["modelValue"])]),e("div",null,[l(s(P),{for:"new_city"},{default:n(()=>t[42]||(t[42]=[y("City")])),_:1}),l(s(D),{id:"new_city",modelValue:$.value.city,"onUpdate:modelValue":t[9]||(t[9]=a=>$.value.city=a),placeholder:"City",class:"mt-1"},null,8,["modelValue"])]),e("div",null,[l(s(P),{for:"new_state"},{default:n(()=>t[43]||(t[43]=[y("State")])),_:1}),l(s(D),{id:"new_state",modelValue:$.value.state,"onUpdate:modelValue":t[10]||(t[10]=a=>$.value.state=a),placeholder:"State",class:"mt-1"},null,8,["modelValue"])]),e("div",null,[l(s(P),{for:"new_zip"},{default:n(()=>t[44]||(t[44]=[y("Postcode")])),_:1}),l(s(D),{id:"new_zip",modelValue:$.value.zip_code,"onUpdate:modelValue":t[11]||(t[11]=a=>$.value.zip_code=a),placeholder:"Postcode",class:"mt-1"},null,8,["modelValue"])]),e("div",ns,[S(e("input",{"onUpdate:modelValue":t[12]||(t[12]=a=>$.value.is_primary=a),type:"checkbox",id:"is_primary_new",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[te,$.value.is_primary]]),t[45]||(t[45]=e("label",{for:"is_primary_new",class:"ml-2 block text-sm text-gray-700"}," Set as primary location ",-1))])]),l(s(j),{onClick:ye,type:"button",variant:"outline",class:"mt-3"},{default:n(()=>t[46]||(t[46]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),y(" Add Location ")])),_:1})]),d.value.practice_locations.length>0?(c(),x("div",ds,[(c(!0),x(U,null,R(d.value.practice_locations,(a,k)=>(c(),x("div",{key:k,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[e("div",is,[e("div",us,[e("span",cs,z(a.address),1),a.is_primary?(c(),x("span",ps," Primary ")):O("",!0)]),e("p",ms,z(a.city)+", "+z(a.state)+" "+z(a.zip_code),1)]),e("div",fs,[a.is_primary?O("",!0):(c(),x("button",{key:0,onClick:N=>xe(k),type:"button",class:"text-sm text-blue-600 hover:text-blue-800"}," Set Primary ",8,gs)),e("button",{onClick:N=>be(k),type:"button",class:"text-red-600 hover:text-red-800"},t[48]||(t[48]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,vs)])]))),128))])):(c(),x("div",ys," No practice locations added yet "))]),e("div",bs,[t[56]||(t[56]=e("div",{class:"flex items-center mb-6"},[e("div",{class:"w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"Pricing & Insurance"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Set your consultation fees and insurance preferences")])],-1)),e("div",xs,[e("div",null,[l(s(P),{for:"consultation_fee"},{default:n(()=>t[50]||(t[50]=[y("Consultation Fee ($)")])),_:1}),l(s(D),{id:"consultation_fee",modelValue:d.value.pricing.consultation,"onUpdate:modelValue":t[13]||(t[13]=a=>d.value.pricing.consultation=a),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"0.00",class:"mt-1"},null,8,["modelValue"])]),e("div",null,[l(s(P),{for:"follow_up_fee"},{default:n(()=>t[51]||(t[51]=[y("Follow-up Fee ($)")])),_:1}),l(s(D),{id:"follow_up_fee",modelValue:d.value.pricing.follow_up,"onUpdate:modelValue":t[14]||(t[14]=a=>d.value.pricing.follow_up=a),modelModifiers:{number:!0},type:"number",min:"0",step:"0.01",placeholder:"0.00",class:"mt-1"},null,8,["modelValue"])])]),e("div",ks,[e("div",_s,[S(e("input",{"onUpdate:modelValue":t[15]||(t[15]=a=>d.value.accepts_insurance=a),type:"checkbox",id:"accepts_insurance",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[te,d.value.accepts_insurance]]),t[52]||(t[52]=e("label",{for:"accepts_insurance",class:"ml-2 block text-sm font-medium text-gray-700"}," Accept Insurance ",-1))])]),d.value.accepts_insurance?(c(),x("div",ws,[l(s(P),{class:"block text-sm font-medium text-gray-700 mb-2"},{default:n(()=>t[53]||(t[53]=[y("Insurance Providers")])),_:1}),e("div",hs,[l(s(D),{modelValue:A.value,"onUpdate:modelValue":t[16]||(t[16]=a=>A.value=a),placeholder:"Add insurance provider",class:"flex-1",onKeyup:q(Z,["enter"])},null,8,["modelValue"]),l(s(j),{onClick:Z,type:"button",variant:"outline"},{default:n(()=>t[54]||(t[54]=[y(" Add ")])),_:1})]),d.value.insurance_providers.length>0?(c(),x("div",Cs,[(c(!0),x(U,null,R(d.value.insurance_providers,(a,k)=>(c(),x("span",{key:k,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"},[y(z(a)+" ",1),e("button",{onClick:N=>ke(k),type:"button",class:"ml-2 text-blue-600 hover:text-blue-800"},t[55]||(t[55]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Bs)]))),128))])):O("",!0)])):O("",!0)]),e("div",$s,[t[59]||(t[59]=e("div",{class:"flex items-center mb-6"},[e("div",{class:"w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"Professional Certifications"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Board certifications and professional credentials")])],-1)),e("div",Ms,[(c(!0),x(U,null,R(d.value.certifications,(a,k)=>(c(),x("div",{key:k,class:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},[e("span",Ps,z(a),1),e("button",{onClick:N=>ve(k),type:"button",class:"text-red-600 hover:text-red-800"},t[57]||(t[57]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ds)]))),128))]),e("div",Vs,[l(s(D),{modelValue:L.value,"onUpdate:modelValue":t[17]||(t[17]=a=>L.value=a),onKeyup:q(J,["enter"]),placeholder:"Add a certification",class:"flex-1"},null,8,["modelValue"]),l(s(j),{onClick:J,type:"button",variant:"outline"},{default:n(()=>t[58]||(t[58]=[y(" Add ")])),_:1})])]),e("div",Os,[e("div",zs,[t[62]||(t[62]=e("div",null,[e("h4",{class:"text-sm font-medium text-gray-900 dark:text-gray-100"},"Save Provider Settings"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Update your professional profile information")],-1)),l(s(j),{type:"submit",disabled:g.value,class:"bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200 shadow-sm"},{default:n(()=>[g.value?(c(),x("div",As,t[60]||(t[60]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),y(" Saving Changes... ")]))):(c(),x("div",Es,t[61]||(t[61]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),y(" Save Provider Settings ")])))]),_:1},8,["disabled"])])])],32)]))])):O("",!0),e("div",Fs,[t[64]||(t[64]=e("div",{class:"flex items-center mb-4"},[e("div",{class:"w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])]),e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-gray-100"},"Danger Zone"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Permanently delete your account and all associated data")])],-1)),l(ft)])]),_:1})]),_:1}))}});export{Ys as default};
