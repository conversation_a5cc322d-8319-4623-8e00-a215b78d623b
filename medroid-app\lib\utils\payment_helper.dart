import 'package:flutter/material.dart';
import '../widgets/payment_modal.dart';

/// Helper function to show payment modal
/// This can be called from anywhere in the app to show a simple payment modal
Future<void> showPaymentModal(
  BuildContext context, {
  required List<Map<String, dynamic>> cartItems,
  required double total,
  required Function(bool success, String? orderId) onPaymentComplete,
}) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.9,
      maxChildSize: 0.95,
      minChildSize: 0.5,
      builder: (context, scrollController) => PaymentModal(
        cartItems: cartItems,
        total: total,
        onPaymentComplete: onPaymentComplete,
      ),
    ),
  );
}

/// Helper function to show payment modal for shopping cart items
Future<void> showCartPaymentModal(
  BuildContext context, {
  required List<dynamic> cartItems,
  required double total,
  Function(bool success, String? orderId)? onPaymentComplete,
}) {
  return showPaymentModal(
    context,
    cartItems: cartItems
        .map((item) => {
              'product_id': item.productId ?? item['product_id'],
              'name': item.product?.name ?? item['name'] ?? 'Product',
              'quantity': item.quantity ?? item['quantity'] ?? 1,
              'price': item.unitPrice ?? item['price'] ?? item['unit_price'] ?? 0,
            })
        .toList(),
    total: total,
    onPaymentComplete: onPaymentComplete ??
        (success, orderId) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Payment successful!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop(); // Close current screen
          }
        },
  );
}
