class FileModel {
  final int id;
  final int userId;
  final String name;
  final String originalName;
  final String path;
  final String disk;
  final String mimeType;
  final int size;
  final String category;
  final String extension;
  final String? description;
  final Map<String, dynamic>? metadata;
  final bool isPublic;
  final DateTime? lastAccessedAt;
  final int downloadCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  FileModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.originalName,
    required this.path,
    required this.disk,
    required this.mimeType,
    required this.size,
    required this.category,
    required this.extension,
    this.description,
    this.metadata,
    required this.isPublic,
    this.lastAccessedAt,
    required this.downloadCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FileModel.fromJson(Map<String, dynamic> json) {
    return FileModel(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      name: json['name'] ?? '',
      originalName: json['original_name'] ?? '',
      path: json['path'] ?? '',
      disk: json['disk'] ?? 'public',
      mimeType: json['mime_type'] ?? '',
      size: json['size'] ?? 0,
      category: json['category'] ?? 'general',
      extension: json['extension'] ?? '',
      description: json['description'],
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
      isPublic: json['is_public'] ?? false,
      lastAccessedAt: json['last_accessed_at'] != null 
          ? DateTime.parse(json['last_accessed_at']) 
          : null,
      downloadCount: json['download_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'original_name': originalName,
      'path': path,
      'disk': disk,
      'mime_type': mimeType,
      'size': size,
      'category': category,
      'extension': extension,
      'description': description,
      'metadata': metadata,
      'is_public': isPublic,
      'last_accessed_at': lastAccessedAt?.toIso8601String(),
      'download_count': downloadCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Computed properties
  String get url {
    // Construct the full URL based on the backend storage configuration
    return 'https://staging-backend.medroid.ai/storage/$path';
  }

  String get sizeFormatted {
    return _formatBytes(size);
  }

  bool get isImage {
    return mimeType.startsWith('image/');
  }

  bool get isVideo {
    return mimeType.startsWith('video/');
  }

  bool get isDocument {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ];
    return documentTypes.contains(mimeType);
  }

  bool get isAudio {
    return mimeType.startsWith('audio/');
  }

  String get fileTypeIcon {
    if (isImage) return '🖼️';
    if (isVideo) return '🎥';
    if (isDocument) return '📄';
    if (isAudio) return '🎵';
    return '📁';
  }

  String get categoryDisplayName {
    const categoryNames = {
      'product_images': 'Product Images',
      'profile_images': 'Profile Images',
      'documents': 'Documents',
      'videos': 'Videos',
      'audio': 'Audio',
      'stories': 'Stories',
      'chat_attachments': 'Chat Attachments',
      'general': 'General',
    };
    return categoryNames[category] ?? category;
  }

  // Helper method to format file size
  String _formatBytes(int bytes) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    int i = 0;
    double size = bytes.toDouble();
    
    while (size >= 1024 && i < suffixes.length - 1) {
      size /= 1024;
      i++;
    }
    
    return '${size.toStringAsFixed(size < 10 ? 1 : 0)} ${suffixes[i]}';
  }

  // Copy with method for updates
  FileModel copyWith({
    int? id,
    int? userId,
    String? name,
    String? originalName,
    String? path,
    String? disk,
    String? mimeType,
    int? size,
    String? category,
    String? extension,
    String? description,
    Map<String, dynamic>? metadata,
    bool? isPublic,
    DateTime? lastAccessedAt,
    int? downloadCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FileModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      originalName: originalName ?? this.originalName,
      path: path ?? this.path,
      disk: disk ?? this.disk,
      mimeType: mimeType ?? this.mimeType,
      size: size ?? this.size,
      category: category ?? this.category,
      extension: extension ?? this.extension,
      description: description ?? this.description,
      metadata: metadata ?? this.metadata,
      isPublic: isPublic ?? this.isPublic,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      downloadCount: downloadCount ?? this.downloadCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FileModel(id: $id, name: $name, category: $category, size: $sizeFormatted)';
  }
}

// File categories enum for easier usage
enum FileCategory {
  productImages('product_images', 'Product Images'),
  profileImages('profile_images', 'Profile Images'),
  documents('documents', 'Documents'),
  videos('videos', 'Videos'),
  audio('audio', 'Audio'),
  stories('stories', 'Stories'),
  chatAttachments('chat_attachments', 'Chat Attachments'),
  general('general', 'General');

  const FileCategory(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static FileCategory fromString(String value) {
    return FileCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => FileCategory.general,
    );
  }
}

// File upload response model
class FileUploadResponse {
  final String message;
  final FileModel file;

  FileUploadResponse({
    required this.message,
    required this.file,
  });

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) {
    return FileUploadResponse(
      message: json['message'] ?? '',
      file: FileModel.fromJson(json['file']),
    );
  }
}

// File list response model
class FileListResponse {
  final List<FileModel> data;
  final int currentPage;
  final int lastPage;
  final int total;
  final int perPage;

  FileListResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.total,
    required this.perPage,
  });

  factory FileListResponse.fromJson(Map<String, dynamic> json) {
    return FileListResponse(
      data: (json['data'] as List<dynamic>?)
          ?.map((item) => FileModel.fromJson(item))
          .toList() ?? [],
      currentPage: json['current_page'] ?? 1,
      lastPage: json['last_page'] ?? 1,
      total: json['total'] ?? 0,
      perPage: json['per_page'] ?? 20,
    );
  }

  bool get hasMorePages => currentPage < lastPage;
}
