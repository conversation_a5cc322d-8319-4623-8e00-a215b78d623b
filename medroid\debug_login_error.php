<?php

/**
 * Debug Login Error <PERSON>
 * 
 * This script specifically debugs the login route 500 error
 */

echo "=== DEBUG LOGIN ERROR SCRIPT ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "ERROR: Run this script from the Laravel root directory.\n";
    exit(1);
}

// 1. Check the latest Laravel logs for login-related errors
echo "1. CHECKING RECENT LOGIN ERRORS\n";
echo "===============================\n";

$logFile = 'storage/logs/laravel.log';
if (file_exists($logFile)) {
    echo "Searching for recent login-related errors...\n";
    
    // Get the last 100 lines and filter for login/auth errors
    $output = shell_exec("tail -100 $logFile | grep -i -A 5 -B 5 'login\\|auth\\|500\\|error\\|exception' 2>&1");
    if ($output) {
        echo "Recent login/auth errors:\n";
        echo $output . "\n";
    } else {
        echo "No recent login/auth errors found in logs.\n";
    }
    
    // Also check for any errors in the last 10 minutes
    echo "\nChecking for very recent errors (last 10 minutes):\n";
    $recentOutput = shell_exec("tail -200 $logFile | grep '" . date('Y-m-d H:') . "' 2>&1");
    if ($recentOutput) {
        echo $recentOutput . "\n";
    } else {
        echo "No errors in the last hour.\n";
    }
} else {
    echo "Laravel log file not found.\n";
}

// 2. Check login routes
echo "\n2. CHECKING LOGIN ROUTES\n";
echo "========================\n";

$routeOutput = shell_exec('php artisan route:list | grep -i login 2>&1');
echo "Login routes:\n";
echo $routeOutput . "\n";

// 3. Check authentication configuration
echo "\n3. CHECKING AUTH CONFIGURATION\n";
echo "==============================\n";

// Check if auth routes are properly defined
if (file_exists('routes/auth.php')) {
    echo "✓ routes/auth.php exists\n";
} else {
    echo "✗ routes/auth.php missing\n";
}

if (file_exists('routes/web.php')) {
    echo "✓ routes/web.php exists\n";
    
    // Check if auth routes are included
    $webContent = file_get_contents('routes/web.php');
    if (strpos($webContent, 'auth.php') !== false || strpos($webContent, 'Auth::routes') !== false) {
        echo "✓ Auth routes are included in web.php\n";
    } else {
        echo "? Auth routes inclusion not clearly found in web.php\n";
    }
} else {
    echo "✗ routes/web.php missing\n";
}

// 4. Check authentication controllers
echo "\n4. CHECKING AUTH CONTROLLERS\n";
echo "============================\n";

$authControllers = [
    'app/Http/Controllers/Auth/LoginController.php',
    'app/Http/Controllers/Auth/RegisterController.php',
    'app/Http/Controllers/AuthController.php',
    'app/Http/Controllers/LoginController.php',
];

foreach ($authControllers as $controller) {
    if (file_exists($controller)) {
        echo "✓ $controller exists\n";
    } else {
        echo "✗ $controller missing\n";
    }
}

// 5. Test database connection
echo "\n5. TESTING DATABASE CONNECTION\n";
echo "==============================\n";

try {
    $dbTest = shell_exec('php artisan tinker --execute="DB::connection()->getPdo(); echo \'Database connection: OK\';" 2>&1');
    echo $dbTest . "\n";
} catch (Exception $e) {
    echo "Database connection error: " . $e->getMessage() . "\n";
}

// 6. Check users table
echo "\n6. CHECKING USERS TABLE\n";
echo "=======================\n";

$userTableCheck = shell_exec('php artisan tinker --execute="echo \'Users count: \' . App\\Models\\User::count();" 2>&1');
echo $userTableCheck . "\n";

// 7. Check session configuration for login
echo "\n7. CHECKING SESSION FOR LOGIN\n";
echo "=============================\n";

$sessionConfig = shell_exec('php artisan config:show session 2>&1');
echo "Session configuration:\n";
echo $sessionConfig . "\n";

// 8. Test a simple login attempt programmatically
echo "\n8. TESTING LOGIN FUNCTIONALITY\n";
echo "==============================\n";

$loginTest = shell_exec('php artisan tinker --execute="
try {
    \$user = App\\Models\\User::first();
    if (\$user) {
        echo \'Test user found: \' . \$user->email . PHP_EOL;
        echo \'Password hash exists: \' . (!empty(\$user->password) ? \'YES\' : \'NO\') . PHP_EOL;
    } else {
        echo \'No users found in database\' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo \'Error: \' . \$e->getMessage() . PHP_EOL;
}
" 2>&1');
echo $loginTest . "\n";

// 9. Check middleware
echo "\n9. CHECKING MIDDLEWARE\n";
echo "======================\n";

$middlewareList = shell_exec('php artisan route:list --columns=uri,name,middleware | grep -i login 2>&1');
echo "Login route middleware:\n";
echo $middlewareList . "\n";

// 10. Check for common login issues
echo "\n10. COMMON LOGIN ISSUES CHECK\n";
echo "=============================\n";

// Check APP_KEY
$appKeyCheck = shell_exec('php artisan tinker --execute="echo \'APP_KEY set: \' . (config(\'app.key\') ? \'YES\' : \'NO\');" 2>&1');
echo $appKeyCheck . "\n";

// Check if bcrypt is working
$bcryptTest = shell_exec('php artisan tinker --execute="echo \'Bcrypt test: \' . bcrypt(\'test\');" 2>&1');
echo "Bcrypt functionality: " . (strpos($bcryptTest, '$2y$') !== false ? 'WORKING' : 'ISSUE') . "\n";

// 11. Create a test login route
echo "\n11. CREATING TEST LOGIN ROUTE\n";
echo "=============================\n";

$testRouteContent = "
Route::get('/test-login-debug', function() {
    try {
        \$user = App\\Models\\User::first();
        if (!\$user) {
            return response()->json(['error' => 'No users found']);
        }
        
        return response()->json([
            'status' => 'Login system check',
            'user_exists' => true,
            'user_email' => \$user->email,
            'auth_working' => class_exists('Illuminate\\Support\\Facades\\Auth'),
            'hash_working' => function_exists('bcrypt'),
            'session_working' => session()->isStarted(),
        ]);
    } catch (Exception \$e) {
        return response()->json(['error' => \$e->getMessage()]);
    }
});
";

// Add test route temporarily
$webRoutesPath = 'routes/web.php';
if (file_exists($webRoutesPath)) {
    $webContent = file_get_contents($webRoutesPath);
    if (strpos($webContent, 'test-login-debug') === false) {
        file_put_contents($webRoutesPath, $webContent . "\n" . $testRouteContent);
        echo "✓ Added test route: /test-login-debug\n";
        echo "Visit https://app.medroid.ai/test-login-debug to test basic functionality\n";
    } else {
        echo "Test route already exists\n";
    }
}

// 12. Final recommendations
echo "\n12. RECOMMENDATIONS\n";
echo "===================\n";
echo "1. Check the Laravel logs above for specific error messages\n";
echo "2. Visit https://app.medroid.ai/test-login-debug to test basic functionality\n";
echo "3. Ensure database connection is working\n";
echo "4. Verify users table has valid data\n";
echo "5. Check if APP_KEY is properly set\n";
echo "6. Clear all caches: php artisan optimize:clear\n\n";

echo "=== DEBUG COMPLETED ===\n";
echo "Please share the output, especially any error messages from the logs.\n";
