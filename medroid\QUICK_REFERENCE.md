# 🏥 Medroid.ai - Quick Reference Card

## 🚀 New Mac Setup (One Command)

```bash
curl -fsSL https://raw.githubusercontent.com/majetyanupam/medroid_app_backend_v1.0/disocver/setup-new-mac.sh | bash
```

---

## 🎮 Daily Development Commands

### **Start Development (3 Terminals):**

#### **Terminal 1: Cloudflare Tunnel**
```bash
cd medroid_app_backend_v1.0
cloudflared tunnel run medroid-backend
```

#### **Terminal 2: Laravel Server**
```bash
cd medroid_app_backend_v1.0
php artisan serve --port=8000
```

#### **Terminal 3: Vite Dev Server**
```bash
cd medroid_app_backend_v1.0
npm run dev
```

### **Quick Aliases (if using automated setup):**
```bash
medroid-start   # Start tunnel
medroid-serve   # Start Laravel
medroid-dev     # Start Vite
medroid-test    # Test connectivity
medroid-cd      # Go to project
```

---

## 🌐 URLs & Endpoints

- **Main App:** https://api.medroid.ai
- **CSRF Cookie:** https://api.medroid.ai/sanctum/csrf-cookie
- **Instagram Callback:** https://api.medroid.ai/auth/instagram/callback
- **Webhooks:** https://api.medroid.ai/webhooks/instagram

---

## 🧪 Testing Commands

```bash
# Test tunnel connectivity
./test-medroid-tunnel.sh

# Test basic connectivity
curl -I https://api.medroid.ai

# Test CSRF endpoint
curl -I https://api.medroid.ai/sanctum/csrf-cookie

# Open in browser
open https://api.medroid.ai
```

---

## 🔧 Troubleshooting

### **Restart Everything:**
```bash
# Kill all processes
pkill -f cloudflared
pkill -f "php artisan serve"
pkill -f vite

# Restart in order
cloudflared tunnel run medroid-backend &
php artisan serve --port=8000 &
npm run dev
```

### **Check What's Running:**
```bash
# Check tunnel status
cloudflared tunnel list

# Check Laravel server
lsof -i :8000

# Check Vite server
lsof -i :5174
```

### **Update Environment:**
```bash
./update-medroid-env.sh
```

---

## 📱 Instagram App Settings

**Facebook Developer Console:** https://developers.facebook.com/apps/951526903656551/

**Update these URLs:**
- **OAuth Redirect:** `https://api.medroid.ai/auth/instagram/callback`
- **Webhook URL:** `https://api.medroid.ai/webhooks/instagram`

---

## 🆘 Emergency Reset

```bash
# Complete reset (if everything breaks)
cd ~
rm -rf medroid_app_backend_v1.0
curl -fsSL https://raw.githubusercontent.com/majetyanupam/medroid_app_backend_v1.0/disocver/setup-new-mac.sh | bash
```

---

## 📋 System Requirements

- **macOS:** 10.15+ (Catalina or newer)
- **RAM:** 8GB+ recommended
- **Storage:** 5GB+ free space
- **Internet:** Required for Cloudflare Tunnel

---

## 🎯 Key Benefits

- ✅ **Stable URL:** Never changes
- ✅ **Professional Domain:** api.medroid.ai
- ✅ **SSL Included:** Automatic HTTPS
- ✅ **Global Performance:** Cloudflare CDN
- ✅ **Team Ready:** Easy onboarding
- ✅ **Mobile Testing:** Real device testing
- ✅ **Production-like:** Same as live environment

---

**🌟 Your professional development environment is ready! 🌟**
