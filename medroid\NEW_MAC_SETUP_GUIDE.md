# 🏥 Medroid.ai - New Mac Setup Guide

## 🚀 Complete Development Environment Setup

This guide will help you set up the complete Medroid development environment on a new Mac with Cloudflare Tunnel and professional domain configuration.

---

## 🎯 Option 1: Automated Setup (Recommended)

### **One-Command Setup:**
```bash
curl -fsSL https://raw.githubusercontent.com/majetyanupam/medroid_app_backend_v1.0/disocver/setup-new-mac.sh | bash
```

### **Or Download and Run:**
```bash
# Download the setup script
wget https://raw.githubusercontent.com/majetyanupam/medroid_app_backend_v1.0/disocver/setup-new-mac.sh

# Make it executable
chmod +x setup-new-mac.sh

# Run the setup
./setup-new-mac.sh
```

**What the automated setup does:**
- ✅ Installs Homebrew (if not present)
- ✅ Installs Node.js, PHP, Composer, Git
- ✅ Installs Cloudflared
- ✅ Clones the Medroid repository
- ✅ Sets up Laravel environment
- ✅ Configures Cloudflare Tunnel
- ✅ Creates helpful aliases
- ✅ Sets up DNS for api.medroid.ai

---

## 🛠️ Option 2: Manual Setup (Step by Step)

### **Step 1: Install Prerequisites**

#### **1.1 Install Homebrew**
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# For Apple Silicon Macs, add to PATH:
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"
```

#### **1.2 Install Development Tools**
```bash
# Install Node.js
brew install node

# Install PHP
brew install php

# Install Composer
brew install composer

# Install Git (if not already installed)
brew install git

# Install Cloudflared
brew install cloudflared
```

### **Step 2: Clone and Setup Project**

#### **2.1 Clone Repository**
```bash
git clone -b disocver https://github.com/majetyanupam/medroid_app_backend_v1.0.git
cd medroid_app_backend_v1.0
```

#### **2.2 Install Dependencies**
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

#### **2.3 Setup Environment**
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### **Step 3: Setup Cloudflare Tunnel**

#### **3.1 Login to Cloudflare**
```bash
cloudflared tunnel login
```
*This will open your browser for authentication*

#### **3.2 Create Tunnel**
```bash
cloudflared tunnel create medroid-backend
```

#### **3.3 Setup DNS Record**
```bash
cloudflared tunnel route dns medroid-backend api.medroid.ai
```

#### **3.4 Create Configuration**
```bash
# Create config directory
mkdir -p ~/.cloudflared

# Get tunnel ID (replace with your actual tunnel ID)
TUNNEL_ID=$(cloudflared tunnel list | grep "medroid-backend" | awk '{print $1}')

# Create config file
cat > ~/.cloudflared/config.yml << EOF
tunnel: $TUNNEL_ID
credentials-file: /Users/<USER>/.cloudflared/$TUNNEL_ID.json

ingress:
  - hostname: api.medroid.ai
    service: http://localhost:8000
  - service: http_status:404
EOF
```

### **Step 4: Update Environment for Custom Domain**
```bash
./update-medroid-env.sh
```

---

## 🎮 Daily Development Workflow

### **Start Development Environment:**

#### **Terminal 1: Start Cloudflare Tunnel**
```bash
cd medroid_app_backend_v1.0
cloudflared tunnel run medroid-backend
```

#### **Terminal 2: Start Laravel Server**
```bash
cd medroid_app_backend_v1.0
php artisan serve --port=8000
```

#### **Terminal 3: Start Vite Development Server**
```bash
cd medroid_app_backend_v1.0
npm run dev
```

### **Quick Access with Aliases:**
If you used the automated setup, you can use these shortcuts:
```bash
medroid-start   # Start Cloudflare tunnel
medroid-serve   # Start Laravel server
medroid-dev     # Start Vite development server
medroid-test    # Test tunnel connectivity
medroid-cd      # Navigate to project directory
```

---

## 🧪 Testing Your Setup

### **Test Tunnel Connectivity:**
```bash
./test-medroid-tunnel.sh
```

### **Manual Testing:**
```bash
# Test basic connectivity
curl -I https://api.medroid.ai

# Test CSRF endpoint
curl -I https://api.medroid.ai/sanctum/csrf-cookie

# Test in browser
open https://api.medroid.ai
```

---

## 🔧 Troubleshooting

### **Common Issues:**

#### **Issue: Cloudflared not found**
```bash
# Reinstall cloudflared
brew uninstall cloudflared
brew install cloudflared
```

#### **Issue: Tunnel login fails**
```bash
# Clear existing credentials and retry
rm -rf ~/.cloudflared/cert.pem
cloudflared tunnel login
```

#### **Issue: DNS not propagating**
```bash
# Check DNS status
dig api.medroid.ai

# Wait 2-5 minutes for propagation
# Test again
./test-medroid-tunnel.sh
```

#### **Issue: CORS errors**
```bash
# Restart Vite with updated config
pkill -f vite
npm run dev
```

#### **Issue: Laravel server not starting**
```bash
# Check if port 8000 is in use
lsof -i :8000

# Kill any processes using port 8000
kill -9 $(lsof -t -i:8000)

# Start Laravel server
php artisan serve --port=8000
```

---

## 📱 Instagram Integration Setup

After your environment is running, update your Instagram app settings:

1. Go to [Facebook Developer Console](https://developers.facebook.com/apps/951526903656551/)
2. Update **Valid OAuth Redirect URIs:**
   ```
   https://api.medroid.ai/auth/instagram/callback
   ```
3. Update **Webhook URL:**
   ```
   https://api.medroid.ai/webhooks/instagram
   ```

---

## 🌟 What You Get

### **Professional Infrastructure:**
- ✅ **Custom Domain:** api.medroid.ai
- ✅ **SSL Certificate:** Automatic Cloudflare SSL
- ✅ **Global CDN:** 200+ data centers worldwide
- ✅ **DDoS Protection:** Enterprise-grade security
- ✅ **Stable URLs:** No more changing development URLs

### **Development Benefits:**
- ✅ **Fast Setup:** One command installation
- ✅ **Team Ready:** Easy onboarding for new developers
- ✅ **Production-like:** Same infrastructure as production
- ✅ **Mobile Testing:** Test on real devices easily
- ✅ **Webhook Testing:** Perfect for Instagram/social integrations

---

## 🆘 Support

If you encounter any issues:

1. **Check the logs:** Look at terminal output for error messages
2. **Test connectivity:** Run `./test-medroid-tunnel.sh`
3. **Restart services:** Kill and restart tunnel/Laravel/Vite
4. **Check documentation:** Review setup steps above
5. **Clean install:** Remove project directory and run setup again

---

## 🎉 Success!

Once setup is complete, you'll have:
- **Development URL:** https://api.medroid.ai
- **Professional domain** with SSL
- **Global performance** via Cloudflare
- **Stable development environment**
- **Team-ready setup**

**Welcome to professional-grade development! 🚀**
