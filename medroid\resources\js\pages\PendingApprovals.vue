<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Pending Approvals', href: '/pending-approvals' },
];

const loading = ref(false);
const pendingProducts = ref([]);
const pendingServices = ref([]);
const activeTab = ref('products');
const rejectionReason = ref('');
const showRejectModal = ref(false);
const selectedItem = ref(null);
const selectedType = ref('');

const fetchPendingProducts = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/pending-products');
        pendingProducts.value = response.data.data || [];
    } catch (error) {
        console.error('Error fetching pending products:', error);
        pendingProducts.value = [];
    } finally {
        loading.value = false;
    }
};

const fetchPendingServices = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get('/pending-services');
        pendingServices.value = response.data.data || [];
    } catch (error) {
        console.error('Error fetching pending services:', error);
        pendingServices.value = [];
    } finally {
        loading.value = false;
    }
};

const approveItem = async (id, type) => {
    try {
        const endpoint = type === 'product' ? `/approve-product/${id}` : `/approve-service/${id}`;
        await window.axios.post(endpoint);
        
        // Refresh the appropriate list
        if (type === 'product') {
            await fetchPendingProducts();
        } else {
            await fetchPendingServices();
        }
        
        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} approved successfully!`);
    } catch (error) {
        console.error(`Error approving ${type}:`, error);
        alert(`Failed to approve ${type}. Please try again.`);
    }
};

const openRejectModal = (item, type) => {
    selectedItem.value = item;
    selectedType.value = type;
    rejectionReason.value = '';
    showRejectModal.value = true;
};

const rejectItem = async () => {
    if (!rejectionReason.value.trim()) {
        alert('Please provide a rejection reason.');
        return;
    }
    
    try {
        const endpoint = selectedType.value === 'product' 
            ? `/reject-product/${selectedItem.value.id}` 
            : `/reject-service/${selectedItem.value.id}`;
            
        await window.axios.post(endpoint, {
            rejection_reason: rejectionReason.value
        });
        
        // Refresh the appropriate list
        if (selectedType.value === 'product') {
            await fetchPendingProducts();
        } else {
            await fetchPendingServices();
        }
        
        showRejectModal.value = false;
        alert(`${selectedType.value.charAt(0).toUpperCase() + selectedType.value.slice(1)} rejected successfully!`);
    } catch (error) {
        console.error(`Error rejecting ${selectedType.value}:`, error);
        alert(`Failed to reject ${selectedType.value}. Please try again.`);
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

onMounted(() => {
    fetchPendingProducts();
    fetchPendingServices();
});
</script>

<template>
    <Head title="Pending Approvals" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Pending Approvals
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <a v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </a>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Tabs -->
                <div class="mb-6">
                    <div class="border-b border-gray-200 dark:border-gray-700">
                        <nav class="-mb-px flex space-x-8">
                            <button
                                @click="activeTab = 'products'"
                                :class="[
                                    activeTab === 'products'
                                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
                                    'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                                ]"
                            >
                                Products ({{ pendingProducts.length }})
                            </button>
                            <button
                                @click="activeTab = 'services'"
                                :class="[
                                    activeTab === 'services'
                                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300',
                                    'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                                ]"
                            >
                                Services ({{ pendingServices.length }})
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Products Tab -->
                <div v-if="activeTab === 'products'" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="pendingProducts.length === 0" class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No pending products found.</p>
                        </div>

                        <div v-else class="space-y-4">
                            <div v-for="product in pendingProducts" :key="product.id" 
                                 class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ product.name }}
                                        </h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                            by {{ product.user?.name || 'Unknown' }} • {{ formatDate(product.created_at) }}
                                        </p>
                                        <p class="text-sm text-gray-700 dark:text-gray-300 mt-2">
                                            {{ product.short_description || product.description }}
                                        </p>
                                        <div class="flex items-center space-x-4 mt-2">
                                            <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                                ${{ product.price }}
                                            </span>
                                            <span class="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded">
                                                {{ product.type }}
                                            </span>
                                            <span class="text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300 px-2 py-1 rounded">
                                                {{ product.category?.name || 'No Category' }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2 ml-4">
                                        <button
                                            @click="approveItem(product.id, 'product')"
                                            class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm font-medium"
                                        >
                                            Approve
                                        </button>
                                        <button
                                            @click="openRejectModal(product, 'product')"
                                            class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm font-medium"
                                        >
                                            Reject
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services Tab -->
                <div v-if="activeTab === 'services'" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="pendingServices.length === 0" class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No pending services found.</p>
                        </div>

                        <div v-else class="space-y-4">
                            <div v-for="service in pendingServices" :key="service.id" 
                                 class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ service.name }}
                                        </h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                            by {{ service.provider?.user?.name || 'Unknown' }} • {{ formatDate(service.created_at) }}
                                        </p>
                                        <p class="text-sm text-gray-700 dark:text-gray-300 mt-2">
                                            {{ service.description }}
                                        </p>
                                        <div class="flex items-center space-x-4 mt-2">
                                            <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                                ${{ service.price }}
                                            </span>
                                            <span class="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded">
                                                {{ service.duration }} min
                                            </span>
                                            <span class="text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300 px-2 py-1 rounded">
                                                {{ service.category || 'No Category' }}
                                            </span>
                                            <span v-if="service.is_telemedicine" class="text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300 px-2 py-1 rounded">
                                                Telemedicine
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2 ml-4">
                                        <button
                                            @click="approveItem(service.id, 'service')"
                                            class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm font-medium"
                                        >
                                            Approve
                                        </button>
                                        <button
                                            @click="openRejectModal(service, 'service')"
                                            class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm font-medium"
                                        >
                                            Reject
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reject Modal -->
        <div v-if="showRejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Reject {{ selectedType.charAt(0).toUpperCase() + selectedType.slice(1) }}
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        Please provide a reason for rejecting "{{ selectedItem?.name }}":
                    </p>
                    <textarea
                        v-model="rejectionReason"
                        rows="4"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        placeholder="Enter rejection reason..."
                    ></textarea>
                    <div class="flex justify-end space-x-3 mt-4">
                        <button
                            @click="showRejectModal = false"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                        >
                            Cancel
                        </button>
                        <button
                            @click="rejectItem"
                            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                        >
                            Reject
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
