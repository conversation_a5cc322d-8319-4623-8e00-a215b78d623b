import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/file.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

class FileService {
  static String get _baseUrl => '${Constants.baseUrl}/api/files';
  final AuthService _authService = AuthService();

  // Get authorization headers
  Future<Map<String, String>> _getHeaders() async {
    final token = await _authService.getToken();
    return {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    };
  }

  // Get files with pagination and filters
  Future<FileListResponse> getFiles({
    int page = 1,
    String? search,
    String? category,
    String? type,
    String sortBy = 'created_at',
    String sortOrder = 'desc',
    int perPage = 20,
  }) async {
    try {
      final headers = await _getHeaders();
      
      // Build query parameters
      final queryParams = <String, String>{
        'page': page.toString(),
        'sort_by': sortBy,
        'sort_order': sortOrder,
        'per_page': perPage.toString(),
      };
      
      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      
      if (category != null && category != 'all') {
        queryParams['category'] = category;
      }
      
      if (type != null && type.isNotEmpty) {
        queryParams['type'] = type;
      }

      final uri = Uri.parse(_baseUrl).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FileListResponse.fromJson(data);
      } else {
        throw Exception('Failed to load files: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading files: $e');
    }
  }

  // Upload a file
  Future<FileUploadResponse> uploadFile({
    required dynamic file, // File for mobile, Uint8List for web
    required String fileName,
    String? category,
    String? name,
    String? description,
    bool isPublic = false,
  }) async {
    try {
      final token = await _authService.getToken();
      
      final request = http.MultipartRequest('POST', Uri.parse(_baseUrl));
      request.headers['Authorization'] = 'Bearer $token';
      request.headers['Accept'] = 'application/json';

      // Add file based on platform
      if (kIsWeb && file is Uint8List) {
        // Web platform
        request.files.add(http.MultipartFile.fromBytes(
          'file',
          file,
          filename: fileName,
        ));
      } else if (file is File) {
        // Mobile platform
        request.files.add(await http.MultipartFile.fromPath(
          'file',
          file.path,
          filename: fileName,
        ));
      } else {
        throw Exception('Invalid file type for current platform');
      }

      // Add other fields
      if (category != null) request.fields['category'] = category;
      if (name != null) request.fields['name'] = name;
      if (description != null) request.fields['description'] = description;
      request.fields['is_public'] = isPublic.toString();

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 201) {
        final data = json.decode(responseBody);
        return FileUploadResponse.fromJson(data);
      } else {
        final errorData = json.decode(responseBody);
        throw Exception(errorData['message'] ?? 'Upload failed');
      }
    } catch (e) {
      throw Exception('Error uploading file: $e');
    }
  }

  // Get a specific file
  Future<FileModel> getFile(int fileId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$fileId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FileModel.fromJson(data);
      } else {
        throw Exception('Failed to load file: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading file: $e');
    }
  }

  // Update file metadata
  Future<FileModel> updateFile({
    required int fileId,
    String? name,
    String? description,
    String? category,
    bool? isPublic,
  }) async {
    try {
      final headers = await _getHeaders();
      headers['Content-Type'] = 'application/json';

      final body = <String, dynamic>{};
      if (name != null) body['name'] = name;
      if (description != null) body['description'] = description;
      if (category != null) body['category'] = category;
      if (isPublic != null) body['is_public'] = isPublic;

      final response = await http.put(
        Uri.parse('$_baseUrl/$fileId'),
        headers: headers,
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FileModel.fromJson(data['file']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Update failed');
      }
    } catch (e) {
      throw Exception('Error updating file: $e');
    }
  }

  // Delete a file
  Future<void> deleteFile(int fileId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.delete(
        Uri.parse('$_baseUrl/$fileId'),
        headers: headers,
      );

      if (response.statusCode != 200) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Delete failed');
      }
    } catch (e) {
      throw Exception('Error deleting file: $e');
    }
  }

  // Bulk delete files
  Future<void> bulkDeleteFiles(List<int> fileIds) async {
    try {
      final headers = await _getHeaders();
      headers['Content-Type'] = 'application/json';

      final response = await http.post(
        Uri.parse('$_baseUrl/bulk-delete'),
        headers: headers,
        body: json.encode({'file_ids': fileIds}),
      );

      if (response.statusCode != 200) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Bulk delete failed');
      }
    } catch (e) {
      throw Exception('Error deleting files: $e');
    }
  }

  // Download file
  Future<String> getDownloadUrl(int fileId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$fileId/secure-url'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['url'];
      } else {
        throw Exception('Failed to get download URL: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting download URL: $e');
    }
  }

  // Get file categories
  Future<Map<String, String>> getCategories() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/categories'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Map<String, String>.from(data);
      } else {
        throw Exception('Failed to load categories: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading categories: $e');
    }
  }

  // Get file statistics
  Future<Map<String, dynamic>> getFileStats() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/stats'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load stats: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading stats: $e');
    }
  }

  // Get thumbnail URL for images
  Future<String?> getThumbnailUrl(int fileId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$fileId/thumbnail'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['url'];
      } else {
        return null; // Thumbnail not available
      }
    } catch (e) {
      return null; // Thumbnail not available
    }
  }

  // Generate thumbnails for an image
  Future<void> generateThumbnails(int fileId) async {
    try {
      final headers = await _getHeaders();
      await http.post(
        Uri.parse('$_baseUrl/$fileId/thumbnails'),
        headers: headers,
      );
    } catch (e) {
      // Silently fail - thumbnails are optional
    }
  }

  // Optimize image
  Future<FileModel> optimizeImage(int fileId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse('$_baseUrl/$fileId/optimize'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return FileModel.fromJson(data['file']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Optimization failed');
      }
    } catch (e) {
      throw Exception('Error optimizing image: $e');
    }
  }
}
