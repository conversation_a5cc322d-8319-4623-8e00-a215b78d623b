import{r as u,c as H,w as ee,d as a,n as p,e as l,i as e,t as r,j as pe,l as F,v as W,F as L,p as O,A as S,x as N,a as P,W as te,J as me,z as ge,o as ve,f as q,u as se,m as fe,g as oe,y as be,P as ye,q as le}from"./vendor-BhKTHoN5.js";import{_ as xe}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{_ as he}from"./ComingSoon-Bg3W8jN1.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const _e={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},we={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},ke={class:"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"},Ce={class:"flex items-center justify-between mb-6"},$e={class:"text-sm text-gray-600"},Se={key:0,class:"mb-4 p-4 bg-green-50 border border-green-200 rounded-lg"},Pe={class:"text-green-800"},je={key:1,class:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg"},Be={class:"text-red-800"},Ae={key:0,class:"p-4 bg-purple-50 rounded-lg"},ze={class:"flex items-center justify-between"},Le={class:"font-medium text-purple-900"},Ne={class:"text-sm text-purple-700"},Ve={class:"text-right"},De={class:"font-bold text-purple-900"},Me={key:0,class:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full"},Te=["min"],Ee={key:1},Oe={key:0,class:"grid grid-cols-2 md:grid-cols-3 gap-2"},Fe=["onClick"],He={key:1,class:"text-center py-4 text-gray-500"},Ue={class:"flex items-center justify-end space-x-4 pt-4 border-t"},qe=["disabled"],We={key:0,class:"fas fa-spinner fa-spin mr-2"},Ge={key:1,class:"fas fa-calendar-plus mr-2"},Ie={__name:"AppointmentBookingModal",props:{isOpen:{type:Boolean,default:!1},service:{type:Object,default:null},provider:{type:Object,default:null}},emits:["close","booked"],setup(k,{emit:G}){const j=k,U=G,d=u({date:"",time_slot:{start_time:"",end_time:""},reason:"",notes:"",is_telemedicine:!1}),g=u(!1),x=u([]),B=u(""),$=u(""),b=u(""),y=H(()=>d.value.date&&d.value.time_slot.start_time&&d.value.time_slot.end_time&&d.value.reason.trim()),h=()=>{U("close"),V()},V=()=>{d.value={date:"",time_slot:{start_time:"",end_time:""},reason:"",notes:"",is_telemedicine:!1},B.value="",x.value=[],$.value="",b.value=""},D=async c=>{if(!(!c||!j.provider))try{const n=await P.get(`/get-providers/${j.provider.id}/available-slots`,{params:{date:c}});x.value=n.data.slots||[]}catch(n){console.error("Error loading available slots:",n),x.value=[]}},I=c=>{d.value.time_slot={start_time:c.start_time,end_time:c.end_time}},R=async()=>{var c,n,_,z;if(y.value){g.value=!0,$.value="",b.value="";try{const w={provider_id:j.provider.id,service_id:(c=j.service)==null?void 0:c.id,date:d.value.date,time_slot:d.value.time_slot,reason:d.value.reason,notes:d.value.notes,is_telemedicine:d.value.is_telemedicine||((n=j.service)==null?void 0:n.is_telemedicine)||!1},m=await P.post("/save-appointment",w);m.data.appointment&&(m.data.payment_required?te.visit(`/appointments/${m.data.appointment.id}/payment`):(b.value="Appointment booked successfully!",U("booked",m.data.appointment),setTimeout(()=>{h(),te.visit("/appointments")},2e3)))}catch(w){console.error("Error booking appointment:",w),$.value=((z=(_=w.response)==null?void 0:_.data)==null?void 0:z.message)||"Failed to book appointment. Please try again."}finally{g.value=!1}}};ee(()=>d.value.date,c=>{c&&D(c)}),ee(()=>j.service,c=>{c&&(d.value.is_telemedicine=c.is_telemedicine||!1)});const M=H(()=>new Date().toISOString().split("T")[0]),A=c=>new Date(`2000-01-01T${c}`).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0});return(c,n)=>{var _,z,w;return k.isOpen?(l(),a("div",_e,[e("div",we,[e("div",{class:"fixed inset-0 transition-opacity bg-white bg-opacity-20 backdrop-blur-sm",onClick:h}),e("div",ke,[e("div",Ce,[e("div",null,[n[3]||(n[3]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Book Appointment",-1)),e("p",$e,r((_=k.service)==null?void 0:_.name)+" with Dr. "+r((w=(z=k.provider)==null?void 0:z.user)==null?void 0:w.name),1)]),e("button",{onClick:h,class:"text-gray-400 hover:text-gray-600"},n[4]||(n[4]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))]),b.value?(l(),a("div",Se,[e("p",Pe,r(b.value),1)])):p("",!0),$.value?(l(),a("div",je,[e("p",Be,r($.value),1)])):p("",!0),e("form",{onSubmit:pe(R,["prevent"]),class:"space-y-6"},[k.service?(l(),a("div",Ae,[e("div",ze,[e("div",null,[e("h4",Le,r(k.service.name),1),e("p",Ne,r(k.service.duration)+" minutes",1)]),e("div",Ve,[e("p",De,"£"+r(k.service.price),1),k.service.is_telemedicine?(l(),a("span",Me," Video Call ")):p("",!0)])])])):p("",!0),e("div",null,[n[5]||(n[5]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Select Date",-1)),F(e("input",{"onUpdate:modelValue":n[0]||(n[0]=m=>d.value.date=m),type:"date",min:M.value,class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",required:""},null,8,Te),[[W,d.value.date]])]),d.value.date?(l(),a("div",Ee,[n[6]||(n[6]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Available Time Slots",-1)),x.value.length>0?(l(),a("div",Oe,[(l(!0),a(L,null,O(x.value,m=>(l(),a("button",{key:`${m.start_time}-${m.end_time}`,type:"button",onClick:Q=>I(m),class:S(["p-3 text-sm border rounded-lg transition-colors",d.value.time_slot.start_time===m.start_time?"border-purple-500 bg-purple-50 text-purple-700":"border-gray-300 hover:border-purple-300 hover:bg-purple-50"])},r(A(m.start_time))+" - "+r(A(m.end_time)),11,Fe))),128))])):(l(),a("div",He," No available slots for this date "))])):p("",!0),e("div",null,[n[7]||(n[7]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Reason for Visit *",-1)),F(e("textarea",{"onUpdate:modelValue":n[1]||(n[1]=m=>d.value.reason=m),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Please describe your symptoms or reason for the appointment...",required:""},null,512),[[W,d.value.reason]])]),e("div",null,[n[8]||(n[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Additional Notes (Optional)",-1)),F(e("textarea",{"onUpdate:modelValue":n[2]||(n[2]=m=>d.value.notes=m),rows:"2",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Any additional information you'd like to share..."},null,512),[[W,d.value.notes]])]),e("div",Ue,[e("button",{type:"button",onClick:h,class:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"}," Cancel "),e("button",{type:"submit",disabled:!y.value||g.value,class:S(["px-6 py-2 rounded-lg font-medium transition-colors",y.value&&!g.value?"bg-purple-600 text-white hover:bg-purple-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[g.value?(l(),a("i",We)):(l(),a("i",Ge)),N(" "+r(g.value?"Booking...":"Book Appointment"),1)],10,qe)])],32)])])])):p("",!0)}}},Re={class:"py-12"},Je={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},Qe={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Ke={class:"p-6 bg-white border-b border-gray-200"},Xe={class:"flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0"},Ye={class:"flex-1"},Ze={class:"flex mt-4 bg-gray-100 rounded-lg p-1 max-w-md"},et={class:"flex flex-col sm:flex-row items-stretch sm:items-center gap-3 lg:min-w-0 lg:flex-shrink-0"},tt={class:"relative flex-1 sm:flex-initial"},st=["placeholder"],ot={key:0,class:"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg border-2 border-white"},lt={key:0},at={key:0,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},rt={key:1},it={key:0,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},nt={class:"p-4"},dt={class:"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-10 gap-3"},ct=["onClick"],ut={class:"text-lg mb-1"},pt={class:"text-xs font-medium leading-tight"},mt={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},gt={class:"p-6"},vt={class:"flex items-center justify-between mb-6"},ft={key:0,class:"text-center py-12"},bt={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},yt={class:"aspect-w-1 aspect-h-1 w-full overflow-hidden bg-gray-200 relative"},xt=["src","alt"],ht={key:1,class:"h-48 w-full flex items-center justify-center bg-gray-200"},_t={key:2,class:"absolute top-3 left-3 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-sm"},wt={key:3,class:"absolute top-3 right-3"},kt={class:"p-4"},Ct={class:"text-lg font-semibold text-gray-900 mb-2"},$t={class:"text-gray-600 text-sm mb-3 line-clamp-2"},St={class:"flex items-center justify-between mb-4"},Pt={class:"flex items-center space-x-2"},jt={class:"text-lg font-bold text-blue-600"},Bt={key:0,class:"text-sm text-gray-500 line-through"},At={key:0,class:"text-xs text-orange-600"},zt=["onClick","disabled"],Lt={key:2,class:"text-center py-12"},Nt={key:1},Vt={key:0,class:"mb-6"},Dt={key:1,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},Mt={class:"p-4"},Tt={class:"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-10 gap-3"},Et=["onClick"],Ot={class:"text-lg mb-1"},Ft={class:"text-xs font-medium leading-tight"},Ht={key:2,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Ut={class:"p-6"},qt={class:"flex items-center justify-between mb-6"},Wt={key:0,class:"text-center py-12"},Gt={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},It=["onClick"],Rt={class:"p-6"},Jt={class:"flex items-center justify-between mb-4"},Qt={class:"flex items-center space-x-3"},Kt={class:"flex-1"},Xt={class:"text-lg font-semibold text-gray-900"},Yt={class:"text-sm text-purple-600 font-medium"},Zt={class:"space-y-2 mb-4"},es={key:0,class:"text-sm text-gray-600 leading-relaxed"},ts={class:"line-clamp-3 break-words"},ss={class:"flex items-center text-sm text-gray-500"},os={class:"flex items-center text-sm text-gray-500"},ls={class:"capitalize"},as={class:"flex items-center text-sm text-gray-500"},rs={key:1,class:"flex items-center text-sm text-gray-500"},is={key:0,class:"mb-4"},ns={class:"flex items-center justify-between text-sm"},ds={class:"font-semibold text-purple-600"},cs={key:2,class:"text-center py-12"},us={key:3,class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},ps={class:"p-6"},ms={class:"border-b border-gray-200 pb-6 mb-6"},gs={class:"flex items-center space-x-4"},vs={class:"flex-1"},fs={class:"text-2xl font-bold text-gray-900"},bs={class:"text-purple-600 font-medium"},ys={class:"flex items-center mt-2 space-x-4 text-sm text-gray-500"},xs={key:0,class:"mt-4"},hs={class:"text-gray-600 leading-relaxed break-words"},_s={key:0,class:"text-center py-12"},ws={key:1},ks={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Cs={class:"p-6"},$s={class:"flex items-center justify-between mb-3"},Ss={class:"text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full"},Ps={key:0,class:"text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full"},js={class:"text-lg font-semibold text-gray-900 mb-2"},Bs={class:"text-gray-600 text-sm mb-4 line-clamp-3"},As={class:"space-y-2 mb-4"},zs={class:"flex items-center justify-between text-sm"},Ls={class:"font-medium"},Ns={class:"flex items-center justify-between text-sm"},Vs={class:"font-bold text-purple-600 text-lg"},Ds=["onClick","disabled"],Ms={key:2,class:"text-center py-12"},Ws=me({__name:"Shop",setup(k){const G=ge(),j=H(()=>{var o;return(o=G.props.auth)==null?void 0:o.user});H(()=>{var o;return((o=j.value)==null?void 0:o.role)==="patient"});const U=[{title:"Shop",href:"/shop"}],d=u(""),g=u("all"),x=u("name"),B=u(!1),$=u(!1),b=u("products"),y=u(null),h=u(!1),V=u(!1),D=u(null),I=u(!1),R=["Health supplements and vitamins","Wellness and fitness products","Medical devices and equipment","Digital health products"],M=u([]),A=u([]),c=u([]),n=u([]),_=u(0),z=[{id:"all",name:"All Specializations",icon:"🏥"},{id:"general-practice",name:"General Practice",icon:"👨‍⚕️"},{id:"cardiology",name:"Cardiology",icon:"❤️"},{id:"dermatology",name:"Dermatology",icon:"🧴"},{id:"psychiatry",name:"Psychiatry",icon:"🧠"},{id:"pediatrics",name:"Pediatrics",icon:"👶"},{id:"orthopedics",name:"Orthopedics",icon:"🦴"},{id:"gynecology",name:"Gynecology",icon:"👩‍⚕️"},{id:"neurology",name:"Neurology",icon:"🧠"},{id:"oncology",name:"Oncology",icon:"🎗️"},{id:"ophthalmology",name:"Ophthalmology",icon:"👁️"}],w=u([{id:"all",name:"All Specializations",icon:"🏥"}]),m=async()=>{B.value=!0;try{const o=await P.get("/providers-list");M.value=o.data.data||o.data.providers||o.data||[],Q()}catch(o){console.error("Error loading providers:",o),M.value=[]}finally{B.value=!1}},Q=()=>{const o=new Set;o.add("all"),M.value.forEach(i=>{if(i.specialization){const v=i.specialization.toLowerCase().replace(/\s+/g,"-");o.add(v)}});const t=z.filter(i=>i.id==="all"?!0:o.has(i.id));w.value=t},T=async()=>{var o;B.value=!0;try{const t={category:g.value!=="all"?g.value:"",search:d.value,sort:x.value},i=await P.get("/shop/products",{params:t});c.value=((o=i.data.products)==null?void 0:o.data)||i.data.products||[]}catch(t){console.error("Error loading products:",t),c.value=[]}finally{B.value=!1}},K=async()=>{var o;try{const t=await P.get("/shop/categories"),i=t.data.categories||t.data||[],v=[];for(const f of i)try{const C=await P.get("/shop/products",{params:{category:f.id,per_page:1}});(((o=C.data.products)==null?void 0:o.data)||C.data.products||[]).length>0&&v.push(f)}catch(C){console.error(`Error checking products for category ${f.id}:`,C)}n.value=v}catch(t){console.error("Error loading categories:",t),n.value=[]}},ae=async()=>{try{const o=await P.get("/shop/cart/count");_.value=o.data.cart_count||0}catch(o){console.error("Error loading cart count:",o),_.value=0}},re=async o=>{$.value=!0;try{const t=await P.get(`/get-providers/${o}/services`);A.value=t.data.services||[]}catch(t){console.error("Error loading provider services:",t),A.value=[]}finally{$.value=!1}},X=H(()=>{let o=M.value;if(g.value!=="all"){const t=g.value.replace("-"," ");o=o.filter(i=>{var f;const v=((f=i.specialization)==null?void 0:f.toLowerCase())||"";return v.includes(t)||v.includes("general")&&t.includes("general")||v===g.value})}if(d.value.trim()){const t=d.value.toLowerCase();o=o.filter(i=>{var v,f,C,s;return((f=(v=i.user)==null?void 0:v.name)==null?void 0:f.toLowerCase().includes(t))||((C=i.specialization)==null?void 0:C.toLowerCase().includes(t))||((s=i.bio)==null?void 0:s.toLowerCase().includes(t))})}switch(x.value){case"name":default:o.sort((t,i)=>{var v,f;return(((v=t.user)==null?void 0:v.name)||"").localeCompare(((f=i.user)==null?void 0:f.name)||"")});break}return o}),ie=o=>{if(!o.weekly_availability)return"Not specified";const t=Object.keys(o.weekly_availability).filter(i=>o.weekly_availability[i]&&o.weekly_availability[i].length>0);return t.length===0?"Not available":t.length===7?"Every day":t.map(i=>i.charAt(0).toUpperCase()+i.slice(1)).join(", ")},Y=o=>!o.practice_locations||o.practice_locations.length===0?"Location not specified":o.practice_locations[0],J=o=>{b.value=o,g.value="all",d.value="",y.value=null,h.value=!1,o==="services"?m():o==="products"&&(T(),K())},ne=async(o,t=1)=>{try{const i=await P.post("/shop/cart/add",{product_id:o.id,quantity:t});i.data.success?(_.value=i.data.cart_count,alert("Product added to cart!")):alert(i.data.message||"Failed to add product to cart")}catch(i){console.error("Error adding to cart:",i),alert("Failed to add product to cart")}},de=async o=>{y.value=o,h.value=!0,await re(o.id)},ce=()=>{y.value=null,h.value=!1,A.value=[]},ue=o=>{D.value=o,V.value=!0},Z=o=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(o);return ve(()=>{T(),K(),ae()}),(o,t)=>(l(),a(L,null,[q(se(fe),{title:"Shop - Medroid"}),q(xe,{breadcrumbs:U},{default:oe(()=>{var i,v,f,C;return[e("div",Re,[e("div",Je,[e("div",Qe,[e("div",Ke,[e("div",Xe,[e("div",Ye,[t[10]||(t[10]=e("h1",{class:"text-2xl font-bold text-gray-900"},"Health and Wellness Shop",-1)),t[11]||(t[11]=e("p",{class:"text-gray-600 mt-1"},"Browse healthcare providers and wellness products",-1)),e("div",Ze,[e("button",{onClick:t[0]||(t[0]=s=>J("products")),class:S(["flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors",b.value==="products"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"])}," 🛒 Products ",2),e("button",{onClick:t[1]||(t[1]=s=>J("services")),class:S(["flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors",b.value==="services"?"bg-white text-purple-600 shadow-sm":"text-gray-600 hover:text-gray-900"])}," 🩺 Services ",2)])]),e("div",et,[e("div",tt,[F(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>d.value=s),type:"text",placeholder:b.value==="products"?"Search products...":"Search providers...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",onInput:t[3]||(t[3]=s=>b.value==="products"?T():m())},null,40,st),[[W,d.value]]),t[12]||(t[12]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))]),b.value==="products"?(l(),be(se(ye),{key:0,href:"/shop/cart",class:"relative inline-flex items-center justify-center px-6 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium whitespace-nowrap"},{default:oe(()=>[t[13]||(t[13]=e("svg",{class:"w-5 h-5 mr-2 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"})],-1)),t[14]||(t[14]=N(" Cart ")),_.value>0?(l(),a("span",ot,r(_.value),1)):p("",!0)]),_:1})):p("",!0)])])])]),b.value==="products"?(l(),a("div",lt,[I.value?(l(),a("div",at,[q(he,{description:"We're working hard to bring you an amazing shopping experience with health and wellness products.",features:R,"action-button":{text:"Browse Healthcare Services",action:"services"},"action-description":"Explore our healthcare providers while we prepare the product catalog",onAction:J})])):(l(),a("div",rt,[n.value.length>0?(l(),a("div",it,[e("div",nt,[t[16]||(t[16]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-3"},"Browse by Category",-1)),e("div",dt,[e("button",{onClick:t[4]||(t[4]=s=>{g.value="all",T()}),class:S(["p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm",g.value==="all"?"border-blue-500 bg-blue-50 text-blue-700 shadow-sm":"border-gray-200 hover:border-blue-300 text-gray-700 hover:bg-gray-50"])},t[15]||(t[15]=[e("div",{class:"text-lg mb-1"},"🏪",-1),e("div",{class:"text-xs font-medium leading-tight"},"All Products",-1)]),2),(l(!0),a(L,null,O(n.value,s=>(l(),a("button",{key:s.id,onClick:E=>{g.value=s.id,T()},class:S(["p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm",g.value===s.id?"border-blue-500 bg-blue-50 text-blue-700 shadow-sm":"border-gray-200 hover:border-blue-300 text-gray-700 hover:bg-gray-50"])},[e("div",ut,r(s.icon||"📦"),1),e("div",pt,r(s.name),1)],10,ct))),128))])])])):p("",!0),e("div",mt,[e("div",gt,[e("div",vt,[t[18]||(t[18]=e("h2",{class:"text-lg font-semibold text-gray-900"},"Products",-1)),F(e("select",{"onUpdate:modelValue":t[5]||(t[5]=s=>x.value=s),onChange:t[6]||(t[6]=s=>T()),class:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[17]||(t[17]=[e("option",{value:"name"},"Sort by Name",-1),e("option",{value:"price_low"},"Price: Low to High",-1),e("option",{value:"price_high"},"Price: High to Low",-1),e("option",{value:"newest"},"Newest First",-1)]),544),[[le,x.value]])]),B.value?(l(),a("div",ft,t[19]||(t[19]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading products...",-1)]))):c.value.length>0?(l(),a("div",bt,[(l(!0),a(L,null,O(c.value,s=>(l(),a("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"},[e("div",yt,[s.primary_image?(l(),a("img",{key:0,src:`/storage/${s.primary_image}`,alt:s.name,class:"h-48 w-full object-cover object-center group-hover:opacity-75"},null,8,xt)):(l(),a("div",ht,t[20]||(t[20]=[e("i",{class:"fas fa-image text-gray-400 text-4xl"},null,-1)]))),s.is_on_sale?(l(),a("div",_t,r(s.discount_percentage)+"% OFF ",1)):p("",!0),s.type==="digital"?(l(),a("div",wt,t[21]||(t[21]=[e("span",{class:"bg-purple-500 text-white text-xs px-2 py-1 rounded shadow-sm"}," Digital ",-1)]))):p("",!0)]),e("div",kt,[e("h3",Ct,r(s.name),1),e("p",$t,r(s.short_description),1),e("div",St,[e("div",Pt,[e("span",jt,r(s.formatted_price),1),s.is_on_sale?(l(),a("span",Bt,r(s.formatted_original_price),1)):p("",!0)]),s.type==="physical"&&s.stock_quantity<=5?(l(),a("span",At," Only "+r(s.stock_quantity)+" left ",1)):p("",!0)]),e("button",{onClick:E=>ne(s),disabled:!s.can_purchase,class:S(["w-full py-2 px-4 rounded-lg transition-colors font-medium",s.can_purchase?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[t[22]||(t[22]=e("i",{class:"fas fa-shopping-cart mr-2"},null,-1)),N(" "+r(s.can_purchase?"Add to Cart":"Out of Stock"),1)],10,zt)])]))),128))])):(l(),a("div",Lt,t[23]||(t[23]=[e("i",{class:"fas fa-box-open text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No products found",-1),e("p",{class:"text-gray-500"},"Try adjusting your search or filter criteria.",-1)])))])])]))])):p("",!0),b.value==="services"?(l(),a("div",Nt,[h.value?(l(),a("div",Vt,[e("button",{onClick:ce,class:"inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},t[24]||(t[24]=[e("i",{class:"fas fa-arrow-left mr-2"},null,-1),N(" Back to Providers ")]))])):p("",!0),!h.value&&w.value.length>1?(l(),a("div",Dt,[e("div",Mt,[t[25]||(t[25]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-3"},"Browse by Specialization",-1)),e("div",Tt,[(l(!0),a(L,null,O(w.value,s=>(l(),a("button",{key:s.id,onClick:E=>g.value=s.id,class:S(["p-3 rounded-lg border transition-colors duration-200 text-center hover:shadow-sm",g.value===s.id?"border-purple-500 bg-purple-50 text-purple-700 shadow-sm":"border-gray-200 hover:border-purple-300 text-gray-700 hover:bg-gray-50"])},[e("div",Ot,r(s.icon),1),e("div",Ft,r(s.name),1)],10,Et))),128))])])])):p("",!0),h.value?(l(),a("div",us,[e("div",ps,[e("div",ms,[e("div",gs,[t[38]||(t[38]=e("div",{class:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-user-md text-purple-600 text-2xl"})],-1)),e("div",vs,[e("h2",fs," Dr. "+r(((v=(i=y.value)==null?void 0:i.user)==null?void 0:v.name)||"Provider"),1),e("p",bs,r((f=y.value)==null?void 0:f.specialization),1),e("div",ys,[e("span",null,[t[37]||(t[37]=e("i",{class:"fas fa-map-marker-alt mr-1"},null,-1)),N(r(Y(y.value)),1)])])])]),(C=y.value)!=null&&C.bio?(l(),a("div",xs,[e("p",hs,r(y.value.bio),1)])):p("",!0)]),$.value?(l(),a("div",_s,t[39]||(t[39]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading services...",-1)]))):A.value.length>0?(l(),a("div",ws,[t[43]||(t[43]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Available Services",-1)),e("div",ks,[(l(!0),a(L,null,O(A.value,s=>(l(),a("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"},[e("div",Cs,[e("div",$s,[e("span",Ss,r(s.category||"Service"),1),s.is_telemedicine?(l(),a("span",Ps," Video Call ")):p("",!0)]),e("h4",js,r(s.name),1),e("p",Bs,r(s.description),1),e("div",As,[e("div",zs,[t[40]||(t[40]=e("span",{class:"text-gray-500"},"Duration:",-1)),e("span",Ls,r(s.duration)+" minutes",1)]),e("div",Ns,[t[41]||(t[41]=e("span",{class:"text-gray-500"},"Price:",-1)),e("span",Vs,r(Z(s.price)),1)])]),e("button",{onClick:E=>ue(s),disabled:!s.active,class:S(["w-full py-2 px-4 rounded-lg transition-colors font-medium",s.active?"bg-purple-600 text-white hover:bg-purple-700":"bg-gray-300 text-gray-500 cursor-not-allowed"])},[t[42]||(t[42]=e("i",{class:"fas fa-calendar-plus mr-2"},null,-1)),N(" "+r(s.active?"Book Appointment":"Unavailable"),1)],10,Ds)])]))),128))])])):(l(),a("div",Ms,t[44]||(t[44]=[e("i",{class:"fas fa-stethoscope text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No services available",-1),e("p",{class:"text-gray-500"},"This provider hasn't listed any services yet.",-1)])))])])):(l(),a("div",Ht,[e("div",Ut,[e("div",qt,[t[27]||(t[27]=e("h2",{class:"text-lg font-semibold text-gray-900"},"Healthcare Providers",-1)),F(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>x.value=s),class:"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"},t[26]||(t[26]=[e("option",{value:"name"},"Sort by Name",-1)]),512),[[le,x.value]])]),B.value?(l(),a("div",Wt,t[28]||(t[28]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading providers...",-1)]))):X.value.length>0?(l(),a("div",Gt,[(l(!0),a(L,null,O(X.value,s=>{var E;return l(),a("div",{key:s.id,class:"bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer",onClick:Ts=>de(s)},[e("div",Rt,[e("div",Jt,[e("div",Qt,[t[29]||(t[29]=e("div",{class:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-user-md text-purple-600 text-xl"})],-1)),e("div",Kt,[e("h3",Xt," Dr. "+r(((E=s.user)==null?void 0:E.name)||"Provider"),1),e("p",Yt,r(s.specialization||"General Practice"),1)])])]),e("div",Zt,[s.bio?(l(),a("div",es,[e("p",ts,r(s.bio),1)])):p("",!0),e("div",ss,[t[30]||(t[30]=e("i",{class:"fas fa-map-marker-alt mr-2 text-gray-400"},null,-1)),e("span",null,r(Y(s)),1)]),e("div",os,[t[31]||(t[31]=e("i",{class:"fas fa-venus-mars mr-2 text-gray-400"},null,-1)),e("span",ls,r(s.gender||"Not specified"),1)]),e("div",as,[t[32]||(t[32]=e("i",{class:"fas fa-calendar mr-2 text-gray-400"},null,-1)),e("span",null,r(ie(s)),1)]),s.languages&&s.languages.length>0?(l(),a("div",rs,[t[33]||(t[33]=e("i",{class:"fas fa-language mr-2 text-gray-400"},null,-1)),e("span",null,r(s.languages.slice(0,2).join(", "))+r(s.languages.length>2?"...":""),1)])):p("",!0)]),s.pricing?(l(),a("div",is,[e("div",ns,[t[34]||(t[34]=e("span",{class:"text-gray-600"},"Consultation:",-1)),e("span",ds,r(Z(s.pricing.consultation||0)),1)])])):p("",!0),t[35]||(t[35]=e("button",{class:"w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors font-medium"},[e("i",{class:"fas fa-eye mr-2"}),N(" View Services ")],-1))])],8,It)}),128))])):(l(),a("div",cs,t[36]||(t[36]=[e("i",{class:"fas fa-user-md text-4xl text-gray-400 mb-4"},null,-1),e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No providers found",-1),e("p",{class:"text-gray-500"},"Try adjusting your search or filter criteria.",-1)])))])]))])):p("",!0)])]),q(Ie,{"is-open":V.value,service:D.value,provider:y.value,onClose:t[8]||(t[8]=s=>{V.value=!1,D.value=null}),onBooked:t[9]||(t[9]=s=>{V.value=!1,D.value=null})},null,8,["is-open","service","provider"])]}),_:1})],64))}});export{Ws as default};
