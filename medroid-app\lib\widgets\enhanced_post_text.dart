import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

class EnhancedPostText extends StatelessWidget {
  final String text;
  final int? maxLines;
  final bool isExpanded;
  final VoidCallback? onToggleExpanded;
  final TextStyle? baseStyle;

  const EnhancedPostText({
    Key? key,
    required this.text,
    this.maxLines,
    this.isExpanded = false,
    this.onToggleExpanded,
    this.baseStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFormattedText(context),
        if (onToggleExpanded != null && _needsExpansion())
          GestureDetector(
            onTap: onToggleExpanded,
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                isExpanded ? 'Show less' : 'See more',
                style: TextStyle(
                  color: AppColors.tealSurge,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFormattedText(BuildContext context) {
    final spans = _parseTextToSpans(text, context);
    
    return Text.rich(
      TextSpan(children: spans),
      maxLines: isExpanded ? null : maxLines,
      overflow: isExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
      style: baseStyle ?? const TextStyle(
        fontSize: 16,
        height: 1.4,
        color: AppColors.midnightNavy,
      ),
    );
  }

  List<TextSpan> _parseTextToSpans(String text, BuildContext context) {
    final List<TextSpan> spans = [];
    final lines = text.split('\n');
    
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      if (line.isEmpty) {
        if (i < lines.length - 1) {
          spans.add(const TextSpan(text: '\n'));
        }
        continue;
      }
      
      // Check if it's a section header (UPPERCASE followed by colon)
      if (_isSectionHeader(line)) {
        spans.add(TextSpan(
          text: line,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 17,
            color: AppColors.midnightNavy,
            height: 1.6,
          ),
        ));
      }
      // Check if it's a bullet point
      else if (_isBulletPoint(line)) {
        spans.add(TextSpan(
          text: line,
          style: const TextStyle(
            fontSize: 15,
            height: 1.5,
            color: AppColors.slateGrey,
          ),
        ));
      }
      // Check if it contains emojis at the start (likely a title or emphasis)
      else if (_startsWithEmoji(line)) {
        spans.add(TextSpan(
          text: line,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            height: 1.5,
          ),
        ));
      }
      // Check if it's a hashtag line
      else if (_isHashtagLine(line)) {
        spans.add(TextSpan(
          text: line,
          style: TextStyle(
            fontSize: 14,
            height: 1.4,
            color: AppColors.tealSurge,
            fontWeight: FontWeight.w500,
          ),
        ));
      }
      // Regular text
      else {
        spans.add(TextSpan(
          text: line,
          style: const TextStyle(
            fontSize: 15,
            height: 1.5,
          ),
        ));
      }
      
      // Add line break if not the last line
      if (i < lines.length - 1) {
        spans.add(const TextSpan(text: '\n'));
      }
    }
    
    return spans;
  }

  bool _isSectionHeader(String line) {
    // Check if line is in ALL CAPS and ends with colon
    final trimmed = line.trim();
    return trimmed.length > 2 && 
           trimmed.toUpperCase() == trimmed && 
           trimmed.endsWith(':') &&
           !trimmed.contains(RegExp(r'[a-z]'));
  }

  bool _isBulletPoint(String line) {
    final trimmed = line.trim();
    return trimmed.startsWith('- ') || 
           trimmed.startsWith('• ') ||
           trimmed.startsWith('* ') ||
           RegExp(r'^[🔸🔹▪️▫️🌟💊🚨]').hasMatch(trimmed);
  }

  bool _startsWithEmoji(String line) {
    if (line.isEmpty) return false;
    final firstChar = line.codeUnitAt(0);
    // Basic emoji range check (this is simplified)
    return firstChar >= 0x1F600 && firstChar <= 0x1F64F || // Emoticons
           firstChar >= 0x1F300 && firstChar <= 0x1F5FF || // Misc Symbols
           firstChar >= 0x1F680 && firstChar <= 0x1F6FF || // Transport
           firstChar >= 0x2600 && firstChar <= 0x26FF;     // Misc symbols
  }

  bool _isHashtagLine(String line) {
    final trimmed = line.trim();
    return trimmed.contains('#') && 
           RegExp(r'^[#\s\w]+$').hasMatch(trimmed);
  }

  bool _needsExpansion() {
    if (maxLines == null) return false;
    final lines = text.split('\n');
    return lines.length > maxLines! || text.length > 200;
  }
}