<?php

/**
 * Quick Fix for 500 Error
 * 
 * This script attempts to fix the missing importUserMedia method issue
 */

echo "=== QUICK FIX FOR 500 ERROR ===\n\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "ERROR: Run this script from the Laravel root directory.\n";
    exit(1);
}

// 1. Check the current InstagramService
$servicePath = 'app/Services/InstagramService.php';
$controllerPath = 'app/Http/Controllers/InstagramController.php';

if (!file_exists($servicePath)) {
    echo "ERROR: InstagramService.php not found at $servicePath\n";
    exit(1);
}

if (!file_exists($controllerPath)) {
    echo "ERROR: InstagramController.php not found at $controllerPath\n";
    exit(1);
}

echo "1. ANALYZING CURRENT CODE\n";
echo "=========================\n";

// Read the service file
$serviceContent = file_get_contents($servicePath);
$controllerContent = file_get_contents($controllerPath);

// Check if importUserMedia method exists
if (strpos($serviceContent, 'function importUserMedia') !== false) {
    echo "✓ importUserMedia method already exists in InstagramService\n";
} else {
    echo "✗ importUserMedia method is missing from InstagramService\n";
    
    // Check what methods are available
    echo "\nAvailable methods in InstagramService:\n";
    preg_match_all('/public function (\w+)\(/', $serviceContent, $matches);
    if (!empty($matches[1])) {
        foreach ($matches[1] as $method) {
            echo "- $method()\n";
        }
    }
    
    // Look for similar methods that might be the correct one
    $possibleMethods = [];
    foreach ($matches[1] as $method) {
        if (stripos($method, 'import') !== false || stripos($method, 'sync') !== false || stripos($method, 'media') !== false) {
            $possibleMethods[] = $method;
        }
    }
    
    if (!empty($possibleMethods)) {
        echo "\nPossible alternative methods:\n";
        foreach ($possibleMethods as $method) {
            echo "- $method()\n";
        }
        
        // Suggest the most likely replacement
        $suggestedMethod = $possibleMethods[0];
        echo "\nSuggested replacement: $suggestedMethod\n";
        
        // 2. Apply the fix
        echo "\n2. APPLYING FIX\n";
        echo "===============\n";
        
        // Create backup
        $backupPath = $controllerPath . '.backup.' . date('Y-m-d-H-i-s');
        copy($controllerPath, $backupPath);
        echo "✓ Created backup: $backupPath\n";
        
        // Replace the method call
        $fixedContent = str_replace('importUserMedia', $suggestedMethod, $controllerContent);
        
        if ($fixedContent !== $controllerContent) {
            file_put_contents($controllerPath, $fixedContent);
            echo "✓ Replaced importUserMedia with $suggestedMethod in controller\n";
        } else {
            echo "✗ No changes made - method call not found in expected format\n";
        }
    } else {
        echo "\n2. CREATING MISSING METHOD\n";
        echo "==========================\n";
        
        // Add a basic importUserMedia method
        $methodToAdd = '
    /**
     * Import user media (alias for syncUserContent)
     * 
     * @param string $userId
     * @return array
     */
    public function importUserMedia($userId)
    {
        // This is a temporary fix - redirect to existing sync method
        if (method_exists($this, \'syncUserContent\')) {
            return $this->syncUserContent($userId);
        }
        
        if (method_exists($this, \'syncContent\')) {
            return $this->syncContent($userId);
        }
        
        // Fallback - return empty result
        \Log::warning("importUserMedia called but no sync method available", [\'userId\' => $userId]);
        return [\'success\' => false, \'message\' => \'Sync method not available\'];
    }
';
        
        // Find the last method in the class and add after it
        $lastMethodPos = strrpos($serviceContent, 'public function');
        if ($lastMethodPos !== false) {
            // Find the end of the last method
            $pos = strpos($serviceContent, '}', $lastMethodPos);
            $nextBrace = $pos;
            $braceCount = 1;
            
            // Find the matching closing brace
            while ($braceCount > 0 && $nextBrace < strlen($serviceContent) - 1) {
                $nextBrace++;
                if ($serviceContent[$nextBrace] === '{') {
                    $braceCount++;
                } elseif ($serviceContent[$nextBrace] === '}') {
                    $braceCount--;
                }
            }
            
            // Insert the new method before the final class closing brace
            $classEndPos = strrpos($serviceContent, '}');
            $newServiceContent = substr($serviceContent, 0, $classEndPos) . $methodToAdd . "\n" . substr($serviceContent, $classEndPos);
            
            // Create backup
            $serviceBackupPath = $servicePath . '.backup.' . date('Y-m-d-H-i-s');
            copy($servicePath, $serviceBackupPath);
            echo "✓ Created backup: $serviceBackupPath\n";
            
            // Write the updated service
            file_put_contents($servicePath, $newServiceContent);
            echo "✓ Added importUserMedia method to InstagramService\n";
        } else {
            echo "✗ Could not find insertion point in service file\n";
        }
    }
}

// 3. Clear caches
echo "\n3. CLEARING CACHES\n";
echo "==================\n";

$commands = [
    'php artisan config:clear',
    'php artisan route:clear',
    'php artisan cache:clear',
    'php artisan optimize:clear'
];

foreach ($commands as $command) {
    echo "Running: $command\n";
    $output = shell_exec($command . ' 2>&1');
    echo $output . "\n";
}

// 4. Test the fix
echo "\n4. TESTING THE FIX\n";
echo "==================\n";

echo "Checking if the method now exists...\n";
$updatedServiceContent = file_get_contents($servicePath);
if (strpos($updatedServiceContent, 'function importUserMedia') !== false) {
    echo "✓ importUserMedia method is now present\n";
} else {
    echo "✗ importUserMedia method is still missing\n";
}

echo "\n=== QUICK FIX COMPLETED ===\n";
echo "Please test your application now.\n";
echo "If issues persist, run the debug script for more information.\n";
