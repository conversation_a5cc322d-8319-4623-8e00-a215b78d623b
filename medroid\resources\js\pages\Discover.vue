<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import ComingSoon from '@/components/ComingSoon.vue'
import UserProfile from '@/components/UserProfile.vue'

// Simplified props for coming soon page
const props = defineProps({
    initialFeed: {
        type: Object,
        default: () => ({ data: [], current_page: 1, last_page: 1, total: 0 })
    },
    availableTopics: {
        type: Array,
        default: () => []
    },
    initialStories: {
        type: Array,
        default: () => []
    }
})

const breadcrumbs = [
    {
        title: 'Discover',
        href: '/discover',
    },
]

// Temporary lock for discover functionality
const isDiscoverLocked = ref(false) // Set to false when ready to go live

// Discover coming soon features
const discoverFeatures = [
    'Health and wellness content discovery',
    'Personalized health recommendations',
    'Community health discussions',
    'Expert health tips and articles'
]

// Reactive state
const state = reactive({
    posts: props.initialFeed.data || [],
    currentPage: props.initialFeed.current_page || 1,
    lastPage: props.initialFeed.last_page || 1,
    total: props.initialFeed.total || 0,
    loading: false,
    selectedTopic: null,
    sortBy: 'relevance',
    stories: props.initialStories || []
})

// Share modal state
const shareModal = reactive({
    show: false,
    post: null,
    shareUrl: '',
    shareText: ''
})

// UI state
const showCreatePost = ref(false)
const showCreateStory = ref(false)
const showImageCropper = ref(false)
const showUserProfile = ref(false)
const selectedUserId = ref(null)
const showPostDetail = ref(false)
const selectedPost = ref(null)
const showComments = ref({})
const showPostMenu = ref({})
const showAllTopics = ref({})
const showFullText = ref({})
const commentsData = ref({})
const loadMoreTrigger = ref(null)

// Notification system
const notifications = ref([])
let notificationId = 0

// Notification panel
const showNotificationPanel = ref(false)
const activityNotifications = ref([])
const notificationCount = computed(() => activityNotifications.value.filter(n => !n.read).length)
const cropperImageFile = ref(null)

// Comment management state
const showCommentMenu = ref({})
const showReplyForm = ref({})
const replyContent = ref({})

// Saved posts counter and page
const totalSavedPosts = ref(0)
const showSavedPostsPage = ref(false)
const savedPostsList = ref([])

// Form state
const newPost = reactive({
    caption: '',
    hashtags: [],
    suggestedHashtags: [],
    media: null,
    mediaPreview: null
})

// AI Writing state
const aiWriting = reactive({
    isGenerating: false,
    showAiModal: false,
    prompt: '',
    generatedContent: ''
})

// Search state
const searchState = reactive({
    query: '',
    results: [],
    isSearching: false,
    recentSearches: []
})

// Comment state
const newComment = reactive({
    content: '',
    postId: null
})

// Video playback state
const videoStates = reactive({})
const currentPlayingVideo = ref(null)
const intersectionObserver = ref(null)
const globalMuteState = ref(true) // Global mute state for all videos



// Computed
const hasMorePosts = computed(() => state.currentPage < state.lastPage)


// Feed Methods
const loadMorePosts = async () => {
    console.log('🔄 loadMorePosts called - loading:', state.loading, 'hasMorePosts:', hasMorePosts.value)
    if (state.loading || !hasMorePosts.value) return
    
    state.loading = true
    try {
        const params = {
            page: state.currentPage + 1,
            sort_by: state.sortBy
        }
        
        if (state.selectedTopic) {
            params.content_type = state.selectedTopic
        }
        
        console.log('📡 Fetching page', params.page, 'with params:', params)
        const response = await axios.get('/web-api/feed', { params })
        const data = response.data

        console.log('📦 Received', data.data?.length || 0, 'new posts')
        console.log('📄 Page info - current:', data.current_page, 'last:', data.last_page, 'total:', data.total)

        state.posts.push(...(data.data || []))
        state.currentPage = data.current_page
        state.lastPage = data.last_page
        state.total = data.total
    } catch (error) {
        console.error('Error loading more posts:', error)
    } finally {
        state.loading = false
    }
}





// Content Creation Methods
const createPost = async () => {
    if (!newPost.caption.trim()) return

    try {
        const formData = new FormData()
        formData.append('caption', newPost.caption)
        formData.append('hashtags', JSON.stringify(newPost.hashtags))
        formData.append('source', 'internal')

        if (newPost.media) {
            formData.append('media', newPost.media)
        }

        const response = await axios.post('/web-api/feed/create', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })

        // Add new post to the beginning of the list
        state.posts.unshift(response.data.post)

        // Reset form
        resetPostForm()
        showCreatePost.value = false
    } catch (error) {
        console.error('Error creating post:', error)
    }
}

const resetPostForm = () => {
    newPost.caption = ''
    newPost.hashtags = []
    newPost.suggestedHashtags = []
    newPost.media = null
    newPost.mediaPreview = null
}

// AI Writing Methods
const openAiWriter = () => {
    aiWriting.showAiModal = true
    aiWriting.prompt = ''
    aiWriting.generatedContent = ''
}

const generateAiContent = async () => {
    if (!aiWriting.prompt.trim()) {
        alert('Please enter a prompt for AI to generate content')
        return
    }

    aiWriting.isGenerating = true
    try {
        // Get fresh CSRF token before making the request
        const csrfToken = document.head.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        console.log('Current CSRF token:', csrfToken)

        const response = await axios.post('/web-api/ai/generate-content', {
            prompt: aiWriting.prompt,
            type: 'social_post'
        }, {
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })

        if (response.data.success) {
            aiWriting.generatedContent = response.data.content
        } else {
            throw new Error(response.data.message || 'Failed to generate content')
        }
    } catch (error) {
        console.error('Error generating AI content:', error)

        // More specific error handling
        if (error.response?.status === 419) {
            alert('Session expired. Please refresh the page and try again.')
        } else if (error.response?.status === 422) {
            alert('Invalid input. Please check your prompt and try again.')
        } else {
            alert('Failed to generate content. Please try again.')
        }
    } finally {
        aiWriting.isGenerating = false
    }
}

const useAiContent = () => {
    if (aiWriting.generatedContent) {
        newPost.caption = aiWriting.generatedContent

        // Extract hashtags from AI content and add to selected hashtags
        generateHashtagSuggestions(aiWriting.generatedContent)

        aiWriting.showAiModal = false
    }
}

const closeAiModal = () => {
    aiWriting.showAiModal = false
    aiWriting.prompt = ''
    aiWriting.generatedContent = ''
}

// Generate hashtag suggestions from caption
const generateHashtagSuggestions = (text) => {
    if (!text || text.length < 10) {
        newPost.suggestedHashtags = []
        newPost.hashtags = []
        return
    }

    // Extract existing hashtags from the content and add to selected hashtags
    const existingHashtags = []
    const hashtagRegex = /#\w+/g
    const matches = text.match(hashtagRegex)
    if (matches) {
        // Clear existing selected hashtags to avoid duplicates
        newPost.hashtags = []

        matches.forEach(hashtag => {
            existingHashtags.push(hashtag.toLowerCase())

            // Add to selected hashtags if not already there (case-insensitive check)
            const isDuplicate = newPost.hashtags.some(existingTag =>
                existingTag.toLowerCase() === hashtag.toLowerCase()
            )
            if (!isDuplicate) {
                newPost.hashtags.push(hashtag)
            }
        })
    } else {
        // No hashtags in content, clear selected hashtags
        newPost.hashtags = []
    }

    // Health-related keywords for hashtag suggestions
    const healthKeywords = [
        'health', 'wellness', 'fitness', 'nutrition', 'mental', 'physical', 'exercise', 'diet',
        'meditation', 'yoga', 'sleep', 'stress', 'anxiety', 'depression', 'therapy', 'mindfulness',
        'cardio', 'strength', 'weight', 'muscle', 'protein', 'vitamins', 'supplements', 'organic',
        'natural', 'healing', 'recovery', 'prevention', 'immune', 'energy', 'balance', 'lifestyle',
        'doctor', 'medical', 'treatment', 'symptoms', 'diagnosis', 'medicine', 'healthcare',
        'selfcare', 'skincare', 'beauty', 'aging', 'longevity', 'chronic', 'pain', 'injury'
    ]

    const words = text.toLowerCase().split(/\s+/)
    const suggestions = []

    // Find health-related words in the text
    words.forEach(word => {
        const cleanWord = word.replace(/[^\w]/g, '')
        if (cleanWord.length > 3 && healthKeywords.some(keyword =>
            cleanWord.includes(keyword) || keyword.includes(cleanWord)
        )) {
            const hashtag = `#${cleanWord}`
            const hashtagLower = hashtag.toLowerCase()

            // Check if hashtag already exists in content or suggestions (case-insensitive)
            if (!existingHashtags.includes(hashtagLower) &&
                !suggestions.some(tag => tag.toLowerCase() === hashtagLower) &&
                suggestions.length < 8) {
                suggestions.push(hashtag)
            }
        }
    })

    // Add some general health hashtags if not enough found
    if (suggestions.length < 3) {
        const generalTags = ['#health', '#wellness', '#lifestyle', '#selfcare', '#mindfulness']
        generalTags.forEach(tag => {
            const tagLower = tag.toLowerCase()
            // Check if general tag already exists in content or suggestions (case-insensitive)
            if (!existingHashtags.includes(tagLower) &&
                !suggestions.some(suggestedTag => suggestedTag.toLowerCase() === tagLower) &&
                suggestions.length < 5) {
                suggestions.push(tag)
            }
        })
    }

    newPost.suggestedHashtags = suggestions
}

// Add hashtag to post
const addHashtag = (hashtag) => {
    // Check if hashtag already exists in the content (case-insensitive)
    const hashtagLower = hashtag.toLowerCase()
    const existingHashtagsInContent = []
    const hashtagRegex = /#\w+/g
    const matches = newPost.caption.match(hashtagRegex)
    if (matches) {
        matches.forEach(tag => {
            existingHashtagsInContent.push(tag.toLowerCase())
        })
    }
    const isDuplicateInContent = existingHashtagsInContent.includes(hashtagLower)

    if (!isDuplicateInContent) {
        // Add to content after existing hashtags
        let updatedCaption = newPost.caption.trim()

        // If content doesn't end with a hashtag, add some spacing
        if (updatedCaption && !updatedCaption.match(/#\w+\s*$/)) {
            // If there are existing hashtags in content, add space
            if (existingHashtagsInContent.length > 0) {
                updatedCaption += ' '
            } else {
                // If no hashtags exist, add double line break for separation
                updatedCaption += '\n\n'
            }
        } else if (updatedCaption && existingHashtagsInContent.length > 0) {
            // If content ends with hashtag, just add space
            updatedCaption += ' '
        }

        // Add the new hashtag
        updatedCaption += hashtag

        newPost.caption = updatedCaption

        // Regenerate suggestions and update selected hashtags automatically
        generateHashtagSuggestions(newPost.caption)
    }
}

// Remove hashtag from post
const removeHashtag = (hashtag) => {
    // Remove from content (case-insensitive)
    let updatedCaption = newPost.caption

    // Create regex to match the hashtag (case-insensitive) with optional surrounding spaces
    const hashtagRegex = new RegExp(`\\s*${hashtag.replace('#', '#')}\\s*`, 'gi')
    updatedCaption = updatedCaption.replace(hashtagRegex, ' ')

    // Clean up extra spaces and line breaks
    updatedCaption = updatedCaption.replace(/\s+/g, ' ').trim()

    newPost.caption = updatedCaption

    // Regenerate suggestions and update selected hashtags automatically
    generateHashtagSuggestions(newPost.caption)
}

// Watch for changes in post caption to automatically update selected hashtags
watch(() => newPost.caption, (newCaption) => {
    generateHashtagSuggestions(newCaption)
}, { debounce: 500 })

// Search functionality
const performSearch = async (query) => {
    if (!query || query.length < 2) {
        searchState.results = []
        return
    }

    searchState.isSearching = true
    try {
        const response = await axios.get('/web-api/feed/search', {
            params: { q: query }
        })

        searchState.results = response.data.posts || []

        // Add to recent searches if not already there
        if (!searchState.recentSearches.includes(query)) {
            searchState.recentSearches.unshift(query)
            if (searchState.recentSearches.length > 5) {
                searchState.recentSearches.pop()
            }
        }
    } catch (error) {
        console.error('Search error:', error)
        searchState.results = []
    } finally {
        searchState.isSearching = false
    }
}

// Debounced search
let searchTimeout = null
const handleSearchInput = (query) => {
    searchState.query = query

    if (searchTimeout) {
        clearTimeout(searchTimeout)
    }

    searchTimeout = setTimeout(() => {
        performSearch(query)
    }, 300)
}

// Clear search
const clearSearch = () => {
    searchState.query = ''
    searchState.results = []
    searchState.isSearching = false
}

// Post interaction methods
const toggleLike = async (post) => {
    try {
        const response = await axios.post(`/web-api/feed/like/${post.id}`)

        // Update the post's like status and count
        post.liked = response.data.liked
        post.engagement_metrics.likes = response.data.like_count

        // Store original Instagram counts for reference (if provided by backend)
        if (response.data.original_instagram_likes !== undefined) {
            post.original_instagram_likes = response.data.original_instagram_likes
        }

        // Show notification
        showNotification(`Post ${response.data.liked ? 'liked' : 'unliked'}!`, 'success')

        // Add activity notification
        if (response.data.liked) {
            addActivityNotification('like', post, 'You liked this post')
        }
    } catch (error) {
        console.error('Error toggling like:', error)
        showNotification('Failed to like post', 'error')
    }
}

const toggleSave = async (post) => {
    try {
        const response = await axios.post(`/web-api/feed/save/${post.id}`)

        // Update the post's save status and count
        post.saved = response.data.saved
        post.engagement_metrics.saves = response.data.save_count

        // Store original Instagram saves for reference (if provided by backend)
        if (response.data.original_instagram_saves !== undefined) {
            post.original_instagram_saves = response.data.original_instagram_saves
        }

        // Update total saved posts counter
        if (response.data.saved) {
            totalSavedPosts.value += 1
        } else {
            totalSavedPosts.value = Math.max(0, totalSavedPosts.value - 1)
            // Remove from saved posts list if it's currently displayed
            if (showSavedPostsPage.value) {
                const index = savedPostsList.value.findIndex(p => p.id === post.id)
                if (index > -1) {
                    savedPostsList.value.splice(index, 1)
                }
            }
        }

        // Show notification
        showNotification(`Post ${response.data.saved ? 'saved' : 'unsaved'}!`, 'success')

        // Add activity notification
        if (response.data.saved) {
            addActivityNotification('save', post, 'You saved this post')
        }
    } catch (error) {
        console.error('Error toggling save:', error)
        showNotification('Failed to save post', 'error')
    }
}

const toggleComments = (postId) => {
    showComments.value[postId] = !showComments.value[postId]

    // Load comments if showing for the first time
    if (showComments.value[postId] && !commentsData.value[postId]) {
        loadComments(postId)
    }
}

const toggleAllTopics = (postId) => {
    showAllTopics.value[postId] = !showAllTopics.value[postId]
}

const toggleFullText = (postId) => {
    showFullText.value[postId] = !showFullText.value[postId]
}

// Notification system
const showNotification = (message, type = 'info') => {
    const id = ++notificationId
    const notification = {
        id,
        message,
        type, // 'success', 'error', 'info', 'warning'
        timestamp: Date.now()
    }

    notifications.value.push(notification)

    // Auto-remove after 3 seconds
    setTimeout(() => {
        removeNotification(id)
    }, 3000)
}

const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
        notifications.value.splice(index, 1)
    }
}

// Activity notification system
const addActivityNotification = (type, post, message) => {
    const notification = {
        id: Date.now() + Math.random(),
        type, // 'like', 'comment', 'save', 'share'
        post_id: post.id,
        post_title: post.caption ? post.caption.substring(0, 50) + '...' : 'Post',
        post_image: post.media_url,
        message,
        timestamp: new Date(),
        read: false
    }

    activityNotifications.value.unshift(notification)

    // Keep only last 50 notifications
    if (activityNotifications.value.length > 50) {
        activityNotifications.value = activityNotifications.value.slice(0, 50)
    }
}

const markNotificationAsRead = (notificationId) => {
    const notification = activityNotifications.value.find(n => n.id === notificationId)
    if (notification) {
        notification.read = true
    }
}

const markAllNotificationsAsRead = () => {
    activityNotifications.value.forEach(n => n.read = true)
}

// Share functionality
const sharePost = async (post) => {
    try {
        if (navigator.share) {
            // Use native share API if available
            await navigator.share({
                title: 'Check out this health post!',
                text: post.caption || 'Interesting health content',
                url: window.location.href
            })

            addActivityNotification('share', post, 'You shared this post')
            showNotification('Post shared successfully!', 'success')
        } else {
            // Fallback: copy link to clipboard
            await navigator.clipboard.writeText(window.location.href)
            showNotification('Link copied to clipboard!', 'success')
            addActivityNotification('share', post, 'You copied the link to this post')
        }
    } catch (error) {
        console.error('Error sharing post:', error)
        showNotification('Failed to share post', 'error')
    }
}

// Infinite scroll setup
const setupInfiniteScroll = () => {
    console.log('🔄 Setting up infinite scroll...')
    console.log('loadMoreTrigger.value:', loadMoreTrigger.value)
    console.log('hasMorePosts:', hasMorePosts.value)
    console.log('state.currentPage:', state.currentPage, 'state.lastPage:', state.lastPage)
    
    if (!loadMoreTrigger.value) {
        console.log('❌ loadMoreTrigger ref is null, cannot setup infinite scroll')
        return
    }

    // Disconnect existing observer
    if (intersectionObserver.value) {
        intersectionObserver.value.disconnect()
    }

    const observer = new IntersectionObserver(
        (entries) => {
            const [entry] = entries
            console.log('📍 Intersection observed:', entry.isIntersecting, 'hasMorePosts:', hasMorePosts.value, 'loading:', state.loading)
            if (entry.isIntersecting && hasMorePosts.value && !state.loading) {
                console.log('🔄 Loading more posts via infinite scroll...')
                loadMorePosts()
            }
        },
        {
            root: null,
            rootMargin: '100px', // Start loading 100px before the trigger comes into view
            threshold: 0.1
        }
    )

    observer.observe(loadMoreTrigger.value)

    // Store observer for cleanup
    intersectionObserver.value = observer
    console.log('✅ Infinite scroll setup complete')
}

const loadComments = async (postId) => {
    try {
        const response = await axios.get(`/web-api/feed/${postId}/comments`)
        commentsData.value[postId] = response.data.comments || []
    } catch (error) {
        console.error('Error loading comments:', error)
        commentsData.value[postId] = []
    }
}

const addComment = async (postId) => {
    if (!newComment.content.trim()) return

    try {
        const response = await axios.post(`/web-api/feed/${postId}/comments`, {
            content: newComment.content
        })

        // Add the new comment to the list
        if (!commentsData.value[postId]) {
            commentsData.value[postId] = []
        }
        commentsData.value[postId].unshift(response.data.comment)

        // Update comment count
        const post = (searchState.query ? searchState.results : state.posts).find(p => p.id === postId)
        if (post) {
            // Store original Instagram comments if this is the first interaction
            if (!post.original_instagram_comments && post.source === 'instagram') {
                post.original_instagram_comments = post.engagement_metrics?.comments || 0
            }

            // For Instagram posts, combine original Instagram comments with local comments
            if (post.source === 'instagram') {
                const originalInstagramComments = post.original_instagram_comments || 0
                const localComments = response.data.local_comment_count || (post.engagement_metrics.comments - originalInstagramComments + 1)
                post.engagement_metrics.comments = originalInstagramComments + localComments
            } else {
                // For local posts, just increment
                post.engagement_metrics.comments = (post.engagement_metrics.comments || 0) + 1
            }
        }

        // Reset comment form
        newComment.content = ''
        newComment.postId = null

        // Show notification
        showNotification('Comment added successfully!', 'success')

        // Add activity notification
        const targetPost = (searchState.query ? searchState.results : state.posts).find(p => p.id === postId)
        if (targetPost) {
            addActivityNotification('comment', targetPost, 'You commented on this post')
        }
    } catch (error) {
        console.error('Error adding comment:', error)
        showNotification('Failed to add comment', 'error')
    }
}

// Comment management methods
const toggleCommentMenu = (commentId) => {
    // Close all other comment menus first
    Object.keys(showCommentMenu.value).forEach(id => {
        if (id !== commentId.toString()) {
            showCommentMenu.value[id] = false
        }
    })

    // Toggle the current menu
    showCommentMenu.value[commentId] = !showCommentMenu.value[commentId]
}

const deleteComment = async (postId, commentId) => {
    if (confirm('Are you sure you want to delete this comment?')) {
        try {
            await axios.delete(`/web-api/feed/${postId}/comments/${commentId}`)

            // Remove comment from the list
            if (commentsData.value[postId]) {
                const index = commentsData.value[postId].findIndex(c => c.id === commentId)
                if (index > -1) {
                    commentsData.value[postId].splice(index, 1)
                }
            }

            // Update comment count
            const post = (searchState.query ? searchState.results : state.posts).find(p => p.id === postId)
            if (post) {
                post.engagement_metrics.comments = Math.max(0, (post.engagement_metrics.comments || 0) - 1)
            }

            alert('Comment deleted successfully.')
        } catch (error) {
            console.error('Error deleting comment:', error)
            alert('Failed to delete comment. Please try again.')
        }
    }
}

const toggleCommentLike = async (postId, commentId) => {
    try {
        const response = await axios.post(`/web-api/feed/${postId}/comments/${commentId}/react`, {
            reaction_type: 'like'
        })

        // Update the comment's reaction data
        const comments = commentsData.value[postId] || []
        const updateComment = (commentsList) => {
            commentsList.forEach(comment => {
                if (comment.id === commentId) {
                    comment.user_reaction = response.data.user_reaction
                    comment.reaction_counts = response.data.reaction_counts
                }
                // Also check replies
                if (comment.replies) {
                    updateComment(comment.replies)
                }
            })
        }
        updateComment(comments)
    } catch (error) {
        console.error('Error toggling comment like:', error)
    }
}

const toggleCommentReply = (commentId) => {
    showReplyForm.value[commentId] = !showReplyForm.value[commentId]
    if (!showReplyForm.value[commentId]) {
        replyContent.value[commentId] = ''
    }
}

const addReply = async (postId, commentId) => {
    if (!replyContent.value[commentId]?.trim()) return

    try {
        const response = await axios.post(`/web-api/feed/${postId}/comments/${commentId}/reply`, {
            content: replyContent.value[commentId]
        })

        // Add the reply to the comment's replies
        const comments = commentsData.value[postId] || []
        const findAndAddReply = (commentsList) => {
            commentsList.forEach(comment => {
                if (comment.id === commentId) {
                    if (!comment.replies) {
                        comment.replies = []
                    }
                    comment.replies.push(response.data.comment)
                }
            })
        }
        findAndAddReply(comments)

        // Update comment count
        const post = (searchState.query ? searchState.results : state.posts).find(p => p.id === postId)
        if (post) {
            post.engagement_metrics.comments = (post.engagement_metrics.comments || 0) + 1
        }

        // Reset reply form
        replyContent.value[commentId] = ''
        showReplyForm.value[commentId] = false
    } catch (error) {
        console.error('Error adding reply:', error)
    }
}

const cancelReply = (commentId) => {
    replyContent.value[commentId] = ''
    showReplyForm.value[commentId] = false
}

// Saved posts functionality
const toggleSavedPosts = async () => {
    if (!showSavedPostsPage.value) {
        // Load saved posts when opening
        try {
            const response = await axios.get('/web-api/saved-posts')
            savedPostsList.value = response.data.posts || []
        } catch (error) {
            console.error('Error loading saved posts:', error)
            savedPostsList.value = []
        }
    }
    showSavedPostsPage.value = !showSavedPostsPage.value
}

const reportPost = async (post) => {
    if (confirm('Are you sure you want to report this post?')) {
        try {
            await axios.post(`/web-api/feed/${post.id}/report`, {
                reason: 'inappropriate_content'
            })
            alert('Post reported successfully. Thank you for helping keep our community safe.')
        } catch (error) {
            console.error('Error reporting post:', error)
            alert('Failed to report post. Please try again.')
        }
    }
}

const deletePost = async (post) => {
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
        try {
            await axios.delete(`/web-api/feed/${post.id}`)

            // Remove post from the list
            const posts = searchState.query ? searchState.results : state.posts
            const index = posts.findIndex(p => p.id === post.id)
            if (index > -1) {
                posts.splice(index, 1)
            }

            alert('Post deleted successfully.')
        } catch (error) {
            console.error('Error deleting post:', error)
            alert('Failed to delete post. Please try again.')
        }
    }
}

// Share functionality
const openShareModal = (post) => {
    shareModal.post = post
    shareModal.shareUrl = generateShareUrl(post)
    shareModal.shareText = generateShareText(post)
    shareModal.show = true
}

const closeShareModal = () => {
    shareModal.show = false
    shareModal.post = null
    shareModal.shareUrl = ''
    shareModal.shareText = ''
}

const generateShareUrl = (post) => {
    const baseUrl = window.location.origin
    return `${baseUrl}/discover?post=${post.id}`
}

const generateShareText = (post) => {
    const platform = post.source === 'instagram' ? 'Instagram' : 'Medroid AI'
    const username = post.source === 'instagram'
        ? `@${post.instagram_username || post.username || 'Instagram User'}`
        : `${post.user?.name || 'Medroid AI User'}`

    const caption = post.caption ? post.caption.substring(0, 100) + '...' : 'Check out this health post'

    return `${caption}\n\nShared from ${platform} by ${username} on Medroid AI Health Community`
}

const shareToWhatsApp = async () => {
    const text = encodeURIComponent(`${shareModal.shareText}\n\n${shareModal.shareUrl}`)
    window.open(`https://wa.me/?text=${text}`, '_blank')
    await trackShare('whatsapp')
}

const shareToLinkedIn = async () => {
    const url = encodeURIComponent(shareModal.shareUrl)
    const title = encodeURIComponent('Health Post from Medroid AI Community')
    const summary = encodeURIComponent(shareModal.shareText)
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${summary}`, '_blank')
    await trackShare('linkedin')
}

const shareToEmail = async () => {
    const subject = encodeURIComponent('Health Post from Medroid AI Community')
    const body = encodeURIComponent(`${shareModal.shareText}\n\nView post: ${shareModal.shareUrl}`)
    window.open(`mailto:?subject=${subject}&body=${body}`)
    await trackShare('email')
}

const shareToSMS = async () => {
    const text = encodeURIComponent(`${shareModal.shareText}\n\n${shareModal.shareUrl}`)
    window.open(`sms:?body=${text}`)
    await trackShare('sms')
}

const copyShareLink = async () => {
    try {
        await navigator.clipboard.writeText(shareModal.shareUrl)
        alert('Link copied to clipboard!')
        await trackShare('copy')
    } catch (error) {
        console.error('Failed to copy link:', error)
        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = shareModal.shareUrl
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        alert('Link copied to clipboard!')
        await trackShare('copy')
    }
}

// Track share analytics
const trackShare = async (platform) => {
    try {
        if (!shareModal.post) return

        await axios.post(`/web-api/feed/${shareModal.post.id}/share`, {
            platform: platform
        })

        // Update the post's share count in the UI
        const postIndex = state.posts.findIndex(p => p.id === shareModal.post.id)
        if (postIndex !== -1) {
            const currentShares = state.posts[postIndex].engagement_metrics?.shares || 0
            state.posts[postIndex].engagement_metrics = {
                ...state.posts[postIndex].engagement_metrics,
                shares: currentShares + 1
            }
        }

        console.log(`Share tracked: ${platform}`)
    } catch (error) {
        console.error('Failed to track share:', error)
        // Don't show error to user as sharing still worked
    }
}

const togglePostMenu = (postId) => {
    // Close all other menus first
    Object.keys(showPostMenu.value).forEach(id => {
        if (id !== postId.toString()) {
            showPostMenu.value[id] = false
        }
    })

    // Toggle the current menu
    showPostMenu.value[postId] = !showPostMenu.value[postId]
}

const handlePostMediaChange = (event) => {
    const file = event.target.files[0]
    if (file) {
        // Compress image if it's larger than 1MB
        if (file.size > 1024 * 1024) {
            compressImage(file, (compressedFile) => {
                processImageForPost(compressedFile)
            })
        } else {
            processImageForPost(file)
        }
    }
}

const processImageForPost = (file) => {
    // Check if image needs cropping
    const img = new Image()
    img.onload = () => {
        const aspectRatio = img.width / img.height
        const targetAspectRatio = 4 / 3
        const tolerance = 0.05

        if (Math.abs(aspectRatio - targetAspectRatio) > tolerance) {
            // Show cropper
            cropperImageFile.value = file
            showImageCropper.value = true
        } else {
            // Use image as is
            newPost.media = file
            const reader = new FileReader()
            reader.onload = (e) => {
                newPost.mediaPreview = e.target.result
            }
            reader.readAsDataURL(file)
        }
    }
    img.src = URL.createObjectURL(file)
}

const handleCroppedImage = (croppedBlob) => {
    if (croppedBlob) {
        // Convert blob to file
        const file = new File([croppedBlob], 'cropped-image.jpg', { type: 'image/jpeg' })

        // Compress the cropped image if it's still large
        if (file.size > 1024 * 1024) {
            compressImage(file, (compressedFile) => {
                newPost.media = compressedFile
                const reader = new FileReader()
                reader.onload = (e) => {
                    newPost.mediaPreview = e.target.result
                }
                reader.readAsDataURL(compressedFile)
            })
        } else {
            newPost.media = file
            const reader = new FileReader()
            reader.onload = (e) => {
                newPost.mediaPreview = e.target.result
            }
            reader.readAsDataURL(file)
        }
    }
    showImageCropper.value = false
    cropperImageFile.value = null
}

const cancelCropping = () => {
    showImageCropper.value = false
    cropperImageFile.value = null
}

// Stories Methods
const showStoryViewer = ref(false)
const currentStoryGroup = ref(null)
const currentStoryIndex = ref(0)
const userStories = ref([])
const storyTimer = ref(null)
const storyProgress = ref(0)

const viewStory = async (storyGroup) => {
    console.log('Viewing story:', storyGroup)
    currentStoryGroup.value = storyGroup
    currentStoryIndex.value = 0

    // Load individual stories for this user
    try {
        const response = await axios.get(`/web-api/stories/user/${storyGroup.user_id}`)
        userStories.value = response.data.stories || []
        showStoryViewer.value = true
        startStoryTimer()
        document.addEventListener('keydown', handleStoryKeydown)
    } catch (error) {
        console.error('Error loading user stories:', error)
    }
}

const closeStoryViewer = () => {
    showStoryViewer.value = false
    currentStoryGroup.value = null
    currentStoryIndex.value = 0
    userStories.value = []
    clearStoryTimer()
    document.removeEventListener('keydown', handleStoryKeydown)
}

const pauseStory = () => {
    clearStoryTimer()
}

const resumeStory = () => {
    if (showStoryViewer.value) {
        startStoryTimer()
    }
}

const handleStoryKeydown = (event) => {
    if (!showStoryViewer.value) return

    switch (event.key) {
        case 'ArrowLeft':
            event.preventDefault()
            previousStory()
            break
        case 'ArrowRight':
        case ' ':
            event.preventDefault()
            nextStory()
            break
        case 'Escape':
            event.preventDefault()
            closeStoryViewer()
            break
    }
}

const nextStory = () => {
    if (userStories.value && currentStoryIndex.value < userStories.value.length - 1) {
        currentStoryIndex.value++
        startStoryTimer()
    } else {
        closeStoryViewer()
    }
}

const previousStory = () => {
    if (currentStoryIndex.value > 0) {
        currentStoryIndex.value--
        startStoryTimer()
    }
}

const startStoryTimer = () => {
    clearStoryTimer()
    storyProgress.value = 0

    const duration = 5000 // 5 seconds
    const interval = 50 // Update every 50ms
    const increment = (interval / duration) * 100

    storyTimer.value = setInterval(() => {
        storyProgress.value += increment
        if (storyProgress.value >= 100) {
            nextStory()
        }
    }, interval)
}

const clearStoryTimer = () => {
    if (storyTimer.value) {
        clearInterval(storyTimer.value)
        storyTimer.value = null
    }
    storyProgress.value = 0
}

const currentStory = computed(() => {
    if (userStories.value && userStories.value[currentStoryIndex.value]) {
        return userStories.value[currentStoryIndex.value]
    }
    return null
})

// User Profile Methods
const openUserProfile = (userId = null) => {
    selectedUserId.value = userId
    showUserProfile.value = true
}

const closeUserProfile = () => {
    showUserProfile.value = false
    selectedUserId.value = null
}

// Post Detail Methods
const openPostDetail = (post) => {
    selectedPost.value = post
    showPostDetail.value = true
}

const closePostDetail = () => {
    showPostDetail.value = false
    selectedPost.value = null
}

// Story form state
const newStory = reactive({
    media: null,
    mediaPreview: null,
    caption: ''
})

const createStory = async () => {
    if (!newStory.media) {
        alert('Please select an image or video for your story')
        return
    }

    try {
        const formData = new FormData()
        formData.append('media', newStory.media)

        if (newStory.caption.trim()) {
            formData.append('caption', newStory.caption)
        }

        console.log('Creating story with:', {
            media: newStory.media.name,
            caption: newStory.caption
        })

        const response = await axios.post('/web-api/stories', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })

        console.log('Story creation response:', response.data)

        // Reload stories to get the updated list
        await loadStories()

        // Reset form
        resetStoryForm()
        showCreateStory.value = false

        alert('Story created successfully!')
    } catch (error) {
        console.error('Error creating story:', error)
        if (error.response?.data?.message) {
            alert('Failed to create story: ' + error.response.data.message)
        } else {
            alert('Failed to create story. Please try again.')
        }
    }
}

const resetStoryForm = () => {
    newStory.media = null
    newStory.mediaPreview = null
    newStory.caption = ''
}

const handleStoryMediaChange = (event) => {
    const file = event.target.files[0]
    if (file) {
        console.log('Story file selected:', file.name, file.type, file.size)

        // Check file size (limit to 10MB)
        const maxSize = 10 * 1024 * 1024 // 10MB
        if (file.size > maxSize) {
            alert('File size too large. Please choose a file smaller than 10MB.')
            return
        }

        // Compress image if it's an image file
        if (file.type.startsWith('image/')) {
            compressImage(file, (compressedFile) => {
                console.log('Image compressed:', compressedFile.name, compressedFile.size)
                newStory.media = compressedFile
                const reader = new FileReader()
                reader.onload = (e) => {
                    newStory.mediaPreview = e.target.result
                    console.log('Story preview created')
                }
                reader.readAsDataURL(compressedFile)
            })
        } else {
            // For videos, use as is but check size
            console.log('Using video file as-is')
            newStory.media = file
            const reader = new FileReader()
            reader.onload = (e) => {
                newStory.mediaPreview = e.target.result
                console.log('Video preview created')
            }
            reader.readAsDataURL(file)
        }
    }
}

// Image compression utility
const compressImage = (file, callback, quality = 0.8, maxWidth = 1080) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img
        if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
        }

        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height)
        canvas.toBlob(callback, 'image/jpeg', quality)
    }

    img.src = URL.createObjectURL(file)
}

// Utility Methods
const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
}



// Close menus when clicking outside
const handleClickOutside = (event) => {
    // Close post menus if clicking outside
    if (!event.target.closest('.post-menu-container')) {
        Object.keys(showPostMenu.value).forEach(id => {
            showPostMenu.value[id] = false
        })
    }

    // Close comment menus if clicking outside
    if (!event.target.closest('.comment-menu-container')) {
        Object.keys(showCommentMenu.value).forEach(id => {
            showCommentMenu.value[id] = false
        })
    }
}

// Load feed data
const loadFeed = async () => {
    state.loading = true
    try {
        const response = await axios.get('/web-api/feed')
        const data = response.data

        state.posts = data.data || []
        state.currentPage = data.current_page || 1
        state.lastPage = data.last_page || 1
        state.total = data.total || 0

        // Count saved posts
        totalSavedPosts.value = state.posts.filter(post => post.saved).length

        console.log('Feed loaded with', state.posts.length, 'posts')
        console.log('Current page:', state.currentPage, 'Last page:', state.lastPage)

        // Trigger video observation and setup infinite scroll after DOM updates
        nextTick(() => {
            observeVideos()
            setupInfiniteScroll()
        })

    } catch (error) {
        console.error('Error loading feed:', error)
        state.posts = []
    } finally {
        state.loading = false
    }
}

// Load stories data
const loadStories = async () => {
    try {
        const response = await axios.get('/web-api/stories')
        console.log('Stories response:', response.data)

        // Combine regular stories with Instagram stories
        const regularStories = response.data.stories || []
        const instagramStories = await loadInstagramStories()

        // Merge stories, prioritizing Instagram stories
        state.stories = [...instagramStories, ...regularStories]
        console.log('Updated stories state:', state.stories)
    } catch (error) {
        console.error('Error loading stories:', error)
        state.stories = []
    }
}

// Load Instagram Stories
const loadInstagramStories = async () => {
    try {
        const response = await axios.get('/web-api/instagram/stories')
        if (response.data.success) {
            return response.data.stories.map(story => ({
                ...story,
                source: 'instagram',
                user_id: story.user_id || 'instagram_user',
                username: story.username || 'Instagram User',
                profile_image: story.profile_image || '/images/instagram-avatar.svg',
                story_count: story.story_count || 1,
                latest_story_time: story.latest_story_time || story.created_at
            }))
        }
        return []
    } catch (error) {
        console.error('Error loading Instagram stories:', error)
        return []
    }
}

// Instagram integration state
const instagramState = reactive({
    connected: false,
    account: null,
    loading: false,
    syncing: false,
    connecting: false,
    progress: {
        status: 'idle',
        step: '',
        progress: 0,
        message: '',
        imported_count: 0
    }
})

// Instagram Methods
const checkInstagramStatus = async () => {
    try {
        const response = await axios.get('/web-api/instagram/account-status')
        if (response.data.success) {
            instagramState.connected = response.data.connected
            instagramState.account = response.data.account
        }
    } catch (error) {
        console.error('Error checking Instagram status:', error)
        // Always reset state on error - ensures connect button is visible
        instagramState.connected = false
        instagramState.account = null
    }
}

const checkConnectionProgress = async () => {
    try {
        const response = await axios.get('/web-api/instagram/connection-progress')
        if (response.data.success) {
            instagramState.progress = response.data.progress

            // If connecting or importing, continue checking
            if (['connecting', 'importing', 'syncing'].includes(instagramState.progress.status)) {
                setTimeout(checkConnectionProgress, 1000) // Check every second
            } else if (instagramState.progress.status === 'completed') {
                // Connection completed, refresh account status
                instagramState.connecting = false
                await checkInstagramStatus()
                
                // Force a reload of the page state
                setTimeout(() => {
                    checkInstagramStatus()
                }, 1000)

                // Show success message
                if (instagramState.progress.imported_count > 0) {
                    alert(`Instagram connected successfully! Imported ${instagramState.progress.imported_count} health-related posts.`)
                } else {
                    alert('Instagram connected successfully! No health-related posts found to import.')
                }
            } else if (instagramState.progress.status === 'error') {
                instagramState.connecting = false
                alert(`Instagram connection failed: ${instagramState.progress.message}`)
            }
        }
    } catch (error) {
        console.error('Error checking connection progress:', error)
    }
}

const connectInstagram = async () => {
    try {
        instagramState.loading = true
        instagramState.connecting = true

        const response = await axios.get('/web-api/instagram/auth-url')
        if (response.data.success) {
            // Start checking progress
            setTimeout(checkConnectionProgress, 2000) // Start checking after 2 seconds
            window.location.href = response.data.auth_url
        }
    } catch (error) {
        console.error('Error getting Instagram auth URL:', error)
        alert('Failed to connect Instagram. Please try again.')
        instagramState.connecting = false
    } finally {
        instagramState.loading = false
    }
}

const disconnectInstagram = async () => {
    if (!confirm('Are you sure you want to disconnect your Instagram account?')) {
        return
    }

    try {
        instagramState.loading = true
        const response = await axios.post('/web-api/instagram/disconnect')
        if (response.data.success) {
            instagramState.connected = false
            instagramState.account = null
            alert('Instagram account disconnected successfully')
        }
    } catch (error) {
        console.error('Error disconnecting Instagram:', error)
        alert('Failed to disconnect Instagram. Please try again.')
    } finally {
        instagramState.loading = false
    }
}

const syncInstagramContent = async () => {
    try {
        instagramState.syncing = true
        const response = await axios.post('/web-api/instagram/sync')
        if (response.data.success) {
            alert(`Successfully imported ${response.data.imported_count} health-related posts`)
            // Refresh the feed to show new content
            loadFeed()
        }
    } catch (error) {
        console.error('Error syncing Instagram content:', error)
        if (error.response?.status === 401) {
            alert('Instagram access token has expired. Please reconnect your account.')
        } else {
            alert('Failed to sync Instagram content. Please try again.')
        }
    } finally {
        instagramState.syncing = false
    }
}



// Video control methods
const initializeVideoState = (postId) => {
    if (!videoStates[postId]) {
        videoStates[postId] = {
            playing: false,
            loaded: false,
            unavailable: false
        }
    }
}

const getVideoElement = (postId) => {
    // Use document.querySelector as a more reliable method
    return document.querySelector(`video[data-post-id="${postId}"]`)
}

const toggleVideoPlay = (postId) => {
    console.log('Toggle video play for post:', postId)
    const video = getVideoElement(postId)
    console.log('Video element found:', video)

    if (!video) {
        console.error('Video element not found for post:', postId)
        return
    }

    initializeVideoState(postId)

    if (videoStates[postId].playing) {
        pauseVideo(postId)
    } else {
        playVideo(postId)
    }
}

const playVideo = async (postId) => {
    const video = getVideoElement(postId)
    if (!video) {
        console.error('Video element not found for play:', postId)
        return
    }

    // Pause currently playing video
    if (currentPlayingVideo.value && currentPlayingVideo.value !== postId) {
        pauseVideo(currentPlayingVideo.value)
    }

    initializeVideoState(postId)

    // Apply global mute state
    video.muted = globalMuteState.value

    console.log('Attempting to play video:', postId, 'muted:', video.muted)

    // Check if video has valid sources before attempting to play
    if (video.readyState === 0) {
        console.log('Video not ready, waiting for load...', postId)
        
        // Try to load the video first
        try {
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Video load timeout'))
                }, 10000) // 10 second timeout
                
                video.addEventListener('loadeddata', () => {
                    clearTimeout(timeout)
                    resolve()
                }, { once: true })
                
                video.addEventListener('error', (e) => {
                    clearTimeout(timeout)
                    reject(e)
                }, { once: true })
                
                video.load()
            })
        } catch (error) {
            console.error('Video failed to load:', error, postId)
            await handleVideoError(postId, error)
            return
        }
    }

    try {
        await video.play()
        console.log('Video playing successfully:', postId)
        videoStates[postId].playing = true
        currentPlayingVideo.value = postId
    } catch (error) {
        console.error('Video play failed:', error, postId)
        await handleVideoError(postId, error)
    }
}

// Handle video errors and attempt recovery
const handleVideoError = async (postId, error) => {
    console.log('Handling video error for post:', postId, error)
    
    const post = [...discoveryPosts.value, ...socialPosts.value].find(p => p.id === postId)
    if (!post) return
    
    // If it's an Instagram video, try to refresh the media URL
    if (post.source === 'instagram') {
        try {
            console.log('Attempting to refresh Instagram video URL for post:', postId)
            const response = await fetch('/api/instagram/refresh-media', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ post_id: postId })
            })
            
            if (response.ok) {
                const result = await response.json()
                if (result.success && result.new_media_url) {
                    console.log('Successfully refreshed Instagram video URL:', result.new_media_url)
                    
                    // Update the post's video URL
                    post.video_url = result.new_media_url
                    post.media_url = result.new_media_url
                    
                    // Update the video element src
                    const video = getVideoElement(postId)
                    if (video) {
                        video.src = result.new_media_url
                        video.load()
                        
                        // Try to play again after refresh
                        setTimeout(() => {
                            video.play().then(() => {
                                console.log('Video playing after refresh:', postId)
                                videoStates[postId].playing = true
                                currentPlayingVideo.value = postId
                            }).catch(retryError => {
                                console.error('Video still failed after refresh:', retryError)
                                markVideoAsUnavailable(postId)
                            })
                        }, 500)
                    }
                } else {
                    console.error('Failed to refresh video URL:', result)
                    markVideoAsUnavailable(postId)
                }
            } else {
                console.error('Video refresh request failed:', response.status)
                markVideoAsUnavailable(postId)
            }
        } catch (refreshError) {
            console.error('Error refreshing video URL:', refreshError)
            markVideoAsUnavailable(postId)
        }
    } else {
        markVideoAsUnavailable(postId)
    }
}

// Mark video as unavailable for playback
const markVideoAsUnavailable = (postId) => {
    if (videoStates[postId]) {
        videoStates[postId].unavailable = true
        videoStates[postId].playing = false
    }
    console.log('Video marked as unavailable:', postId)
}

// Handle video load errors from the video element
const handleVideoLoadError = (postId, event) => {
    console.error('Video load error for post:', postId, event)
    const video = event.target
    
    // If this is the last source that failed, mark as unavailable
    if (video && video.error) {
        console.error('Video error code:', video.error.code, 'message:', video.error.message)
        
        // Try to handle the error
        setTimeout(() => {
            handleVideoError(postId, video.error)
        }, 100)
    }
}

// Retry loading a video that was marked as unavailable
const retryVideoLoad = async (postId) => {
    console.log('Retrying video load for post:', postId)
    
    if (videoStates[postId]) {
        videoStates[postId].unavailable = false
    }
    
    // Force reload of the video element by updating the post
    await nextTick()
    
    // Try to play the video again
    setTimeout(() => {
        toggleVideoPlay(postId)
    }, 500)
}

const pauseVideo = (postId) => {
    const video = getVideoElement(postId)
    if (!video) {
        console.error('Video element not found for pause:', postId)
        return
    }

    initializeVideoState(postId)
    console.log('Pausing video:', postId)
    video.pause()
    videoStates[postId].playing = false

    if (currentPlayingVideo.value === postId) {
        currentPlayingVideo.value = null
    }
}

const toggleVideoMute = () => {
    console.log('Toggle global mute state from:', globalMuteState.value)

    // Toggle global mute state
    globalMuteState.value = !globalMuteState.value

    // Apply the new mute state to all video elements
    const allVideos = document.querySelectorAll('video[data-post-id]')
    allVideos.forEach(video => {
        video.muted = globalMuteState.value
    })

    console.log('Global mute state changed to:', globalMuteState.value)
    console.log('Applied to', allVideos.length, 'videos')
}

const isVideoPlaying = (postId) => {
    return videoStates[postId]?.playing || false
}

const isVideoMuted = () => {
    return globalMuteState.value // Use global mute state
}

const onVideoLoaded = (postId) => {
    console.log('Video loaded for post:', postId)
    initializeVideoState(postId)
    videoStates[postId].loaded = true

    // Apply global mute state
    const video = getVideoElement(postId)
    if (video) {
        video.muted = globalMuteState.value
    }
}



// Auto-play functionality
const setupVideoAutoPlay = () => {
    intersectionObserver.value = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const video = entry.target
            const postId = video.getAttribute('data-post-id')

            console.log(`Video ${postId} intersection:`, {
                isIntersecting: entry.isIntersecting,
                intersectionRatio: entry.intersectionRatio
            })

            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                // Video is more than 50% visible, auto-play it
                console.log(`Auto-playing video ${postId}`)
                setTimeout(() => {
                    if (entry.isIntersecting) { // Double check it's still visible
                        playVideo(postId)
                    }
                }, 200)
            } else {
                // Video is not visible enough, pause it
                console.log(`Auto-pausing video ${postId}`)
                pauseVideo(postId)
            }
        })
    }, {
        threshold: [0.5], // Trigger when 50% of video is visible
        rootMargin: '0px'
    })
}

const observeVideos = () => {
    // Observe all video elements
    nextTick(() => {
        // Disconnect existing observer first
        if (intersectionObserver.value) {
            intersectionObserver.value.disconnect()
        }

        // Setup new observer
        setupVideoAutoPlay()

        const videos = document.querySelectorAll('video[data-post-id]')
        console.log(`Found ${videos.length} videos to observe`)

        videos.forEach(video => {
            const postId = video.getAttribute('data-post-id')
            console.log(`Observing video for post ${postId}`)

            // Apply global mute state to all videos
            video.muted = globalMuteState.value

            intersectionObserver.value?.observe(video)
        })
    })
}

// Initialize
onMounted(() => {
    // Component initialization
    console.log('🚀 Discover component mounted')
    console.log('Initial stories from props:', props.initialStories)
    console.log('Initial stories state:', state.stories)

    // Load content first
    loadFeed().then(() => {
        console.log('📺 Feed loaded, setting up video auto-play...')
        // Setup video auto-play after content is loaded
        setTimeout(() => {
            console.log('🔍 Calling observeVideos...')
            observeVideos()
        }, 1000) // Increased timeout to ensure DOM is ready
    })

    loadStories()
    checkInstagramStatus()

    // Check for Instagram callback results
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('instagram_success')) {
        instagramState.connecting = true
        // Start checking progress immediately and refresh status
        checkConnectionProgress()
        // Also force refresh the status
        setTimeout(() => {
            checkInstagramStatus()
        }, 3000) // Check status after 3 seconds
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname)
    } else if (urlParams.get('instagram_error')) {
        const error = urlParams.get('instagram_error')
        alert(`Instagram connection failed: ${decodeURIComponent(error)}`)
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname)
    }

    // Add click outside listener
    document.addEventListener('click', handleClickOutside)

    // Setup infinite scroll observer
    nextTick(() => {
        setupInfiniteScroll()
    })
})

// Watch for posts changes to observe new videos and setup infinite scroll
watch(() => state.posts, () => {
    nextTick(() => {
        observeVideos()
        setupInfiniteScroll()
    })
}, { deep: true, flush: 'post' })

// Cleanup
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
    intersectionObserver.value?.disconnect()
})
</script>

<template>
    <Head title="Discover - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <!-- Coming Soon Lock Screen -->
        <div v-if="isDiscoverLocked">
            <ComingSoon
                description="We're working hard to bring you an amazing discovery experience with health content, community posts, and social feeds."
                :features="discoverFeatures"
                :action-button="{
                    text: 'Continue Chatting with AI Doctor',
                    action: '/chat'
                }"
                action-description="Get instant health advice while we prepare the discover feature"
                min-height="min-h-screen"
                @action="(url) => window.location.href = url"
            />
        </div>

        <!-- Saved Posts Page -->
        <div v-else-if="showSavedPostsPage">
            <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">Saved Posts</h1>
                            <p class="text-xs text-gray-500">{{ totalSavedPosts }} saved posts</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Saved Posts Content -->
            <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <!-- Empty State -->
                <div v-if="savedPostsList.length === 0" class="text-center py-16">
                    <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">No saved posts yet</h3>
                    <p class="text-gray-500 mb-6 max-w-md mx-auto">Posts you save will appear here for easy access later.</p>
                    <button
                        @click="showSavedPostsPage = false"
                        class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium rounded-full hover:from-blue-600 hover:to-cyan-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Discover Posts
                    </button>
                </div>

                <!-- Saved Posts List -->
                <div v-else class="space-y-6">
                    <article
                        v-for="post in savedPostsList"
                        :key="post.id"
                        class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200"
                    >
                        <!-- Post Header -->
                        <div class="p-6 pb-4">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">{{ post.user?.name || 'Anonymous' }}</p>
                                        <p class="text-sm text-gray-500">{{ formatDate(post.created_at) }}</p>
                                    </div>
                                </div>
                                <button
                                    @click="toggleSave(post)"
                                    class="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors"
                                >
                                    <svg class="w-5 h-5" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                    </svg>
                                </button>
                            </div>


                        </div>

                        <!-- Media -->
                        <div v-if="post.media_url" class="px-2 sm:px-6 pb-4">
                            <div class="relative rounded-xl overflow-hidden bg-gray-100">
                                <!-- Video Content -->
                                <div v-if="post.content_type === 'video'" class="relative">
                                    <video
                                        :data-post-id="post.id"
                                        :src="post.video_url || post.media_url"
                                        :poster="post.thumbnail_url || post.media_url"
                                        class="w-full object-cover cursor-pointer"
                                        style="aspect-ratio: 9/16; height: 85vh; min-height: 600px;"
                                        muted
                                        loop
                                        playsinline
                                        preload="metadata"
                                        @click.stop="toggleVideoPlay(post.id)"
                                        @loadedmetadata="onVideoLoaded(post.id)"
                                        @play="videoStates[post.id] && (videoStates[post.id].playing = true)"
                                        @pause="videoStates[post.id] && (videoStates[post.id].playing = false)"
                                    >
                                        Your browser does not support the video tag.
                                    </video>

                                    <!-- Play/Pause Button Overlay -->
                                    <div
                                        v-show="!isVideoPlaying(post.id)"
                                        class="absolute inset-0 flex items-center justify-center"
                                        @click.stop="toggleVideoPlay(post.id)"
                                    >
                                        <div class="bg-black bg-opacity-50 text-white rounded-full p-3 sm:p-4 hover:bg-opacity-70 transition-all cursor-pointer">
                                            <svg class="w-6 h-6 sm:w-8 sm:h-8" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                                            </svg>
                                        </div>
                                    </div>

                                    <!-- Video Badge -->
                                    <div class="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs font-medium">
                                        <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                                        </svg>
                                        Video
                                    </div>

                                    <!-- Mute/Unmute Button -->
                                    <button
                                        @click.stop="toggleVideoMute()"
                                        class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white rounded-full p-2 hover:bg-opacity-80 transition-all z-10"
                                    >
                                        <svg v-if="isVideoMuted()" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                        <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.414A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                                <!-- Image Content -->
                                <img
                                    v-else
                                    :src="post.media_url"
                                    :alt="post.caption"
                                    class="w-full h-auto object-cover"
                                    style="max-height: 70vh; min-height: 200px;"
                                />
                            </div>
                        </div>

                        <!-- Post Content - Below Media -->
                        <div v-if="post.caption" class="px-6 pb-4">
                            <div class="text-gray-800 leading-relaxed text-sm">
                                <!-- Truncated text (2-3 lines) -->
                                <p
                                    v-if="!showFullText[post.id]"
                                    class="line-clamp-3"
                                    style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;"
                                >
                                    {{ post.caption }}
                                </p>

                                <!-- Full text -->
                                <p v-else class="whitespace-pre-wrap">{{ post.caption }}</p>

                                <!-- Show more/less button -->
                                <button
                                    v-if="post.caption && post.caption.length > 150"
                                    @click="toggleFullText(post.id)"
                                    class="text-gray-500 hover:text-gray-700 text-sm font-medium mt-1 transition-colors"
                                >
                                    {{ showFullText[post.id] ? 'Show less' : 'Show more' }}
                                </button>
                            </div>

                            <!-- Health Topics - Only show for local posts -->
                            <div v-if="post.source !== 'instagram' && post.health_topics && post.health_topics.length > 0" class="flex flex-wrap gap-2 mt-3">
                                <!-- Show first 2 topics -->
                                <span
                                    v-for="topic in (showAllTopics[post.id] ? post.health_topics : post.health_topics.slice(0, 2))"
                                    :key="topic"
                                    class="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-xs font-medium rounded-full"
                                >
                                    #{{ topic }}
                                </span>

                                <!-- Show count and expand button if more than 2 topics -->
                                <button
                                    v-if="post.health_topics.length > 2 && !showAllTopics[post.id]"
                                    @click="toggleAllTopics(post.id)"
                                    class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"
                                >
                                    +{{ post.health_topics.length - 2 }} more
                                </button>

                                <!-- Show less button when expanded -->
                                <button
                                    v-if="post.health_topics.length > 2 && showAllTopics[post.id]"
                                    @click="toggleAllTopics(post.id)"
                                    class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"
                                >
                                    Show less
                                </button>
                            </div>
                        </div>

                        <!-- Post Actions -->
                        <div class="px-6 py-4 border-t border-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <button
                                        @click="toggleLike(post)"
                                        :class="[
                                            'flex items-center space-x-2 text-sm font-medium transition-all duration-200',
                                            post.liked
                                                ? 'text-red-600 hover:text-red-700'
                                                : 'text-gray-500 hover:text-red-600'
                                        ]"
                                    >
                                        <svg class="w-5 h-5" :fill="post.liked ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                        </svg>
                                        <span>{{ post.engagement_metrics?.likes || 0 }}</span>
                                    </button>

                                    <div class="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                        <span>{{ post.engagement_metrics?.comments || 0 }}</span>
                                    </div>

                                    <div class="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                        <span>{{ post.engagement_metrics?.shares || 0 }}</span>
                                    </div>
                                </div>

                                <button
                                    @click="toggleSave(post)"
                                    :class="[
                                        'flex items-center space-x-2 text-sm font-medium transition-all duration-200',
                                        post.saved
                                            ? 'text-purple-600 hover:text-purple-700'
                                            : 'text-gray-500 hover:text-purple-600'
                                    ]"
                                >
                                    <svg class="w-5 h-5" :fill="post.saved ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                    </svg>
                                    <span>{{ post.engagement_metrics?.saves || 0 }}</span>
                                </button>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
        </div>

        <!-- Main Discover Page -->
        <div v-else class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200 sticky top-0 z-40">
                <div class="max-w-6xl mx-auto px-3 sm:px-4 lg:px-8">
                    <div class="flex items-center justify-between py-3 sm:py-4">
                        <div class="flex items-center space-x-3 sm:space-x-4">
                            <div class="flex items-center space-x-2 sm:space-x-3">
                                <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-full flex items-center justify-center" style="background: linear-gradient(135deg, #17C3B2 0%, #8BE9C8 100%);">
                                    <svg class="w-4 h-4 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h1 class="text-lg sm:text-xl font-bold text-gray-900">Discover</h1>
                                    <p class="text-xs text-gray-500 hidden sm:block">Health insights & community</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-2 sm:space-x-3">
                            <!-- Notification Icon -->
                            <button
                                @click="showNotificationPanel = true"
                                class="relative p-2.5 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-full transition-all duration-200 shadow-sm border border-gray-200 hover:border-gray-300 bg-white"
                            >
                                <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                                </svg>
                                <!-- Notification Count Badge -->
                                <span
                                    v-if="notificationCount > 0"
                                    class="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center shadow-lg border-2 border-white"
                                >
                                    {{ notificationCount > 99 ? '99+' : notificationCount }}
                                </span>
                            </button>

                            <button
                                @click="showCreatePost = true"
                                class="medroid-create-btn inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 text-white text-xs sm:text-sm font-medium rounded-full transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                                <span class="hidden sm:inline">Create Post</span>
                                <span class="sm:hidden">Post</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="max-w-6xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-6">
                <div class="flex gap-3 xl:gap-6 items-start">
                    <!-- Left Sidebar - Profile & Search -->
                    <div class="w-56 xl:w-72 flex-shrink-0 hidden lg:block">
                        <!-- Fixed Sidebar Container -->
                        <div class="sticky top-28 w-56 xl:w-72 h-[calc(100vh-7rem)] overflow-y-auto pr-4">
                            <!-- Quick Profile Card -->
                            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6 h-[180px] flex flex-col justify-center hover:shadow-md transition-all duration-200">
                                <div class="text-center">
                                    <button
                                        @click="openUserProfile()"
                                        class="w-12 h-12 mx-auto mb-2 rounded-full overflow-hidden border-2 border-gray-200 hover:border-teal-400 transition-colors"
                                    >
                                        <img
                                            :src="$page.props.auth.user?.profile_image ?
                                                ($page.props.auth.user.profile_image.startsWith('http') ?
                                                    $page.props.auth.user.profile_image :
                                                    `/storage/${$page.props.auth.user.profile_image}`) :
                                                '/images/default-avatar.svg'"
                                            alt="Profile"
                                            class="w-full h-full object-cover"
                                        />
                                    </button>
                                    <h3 class="text-sm font-semibold text-gray-900 mb-1">Your Profile</h3>
                                    <p class="text-xs text-gray-500 mb-3">View your posts & stats</p>
                                    <button
                                        @click="openUserProfile()"
                                        class="medroid-profile-btn w-full py-2 text-white text-sm font-medium rounded-lg transition-all duration-200"
                                    >
                                        View Profile
                                    </button>
                                </div>
                            </div>



                            <!-- Search Card -->
                            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-4">
                            <h3 class="text-sm font-semibold text-gray-900 mb-3">Search Posts</h3>
                            <div class="space-y-3">
                                <div class="relative">
                                    <input
                                        v-model="searchState.query"
                                        @input="handleSearchInput($event.target.value)"
                                        type="text"
                                        placeholder="Search by hashtags, keywords..."
                                        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm"
                                    />
                                    <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                    <button
                                        v-if="searchState.query"
                                        @click="clearSearch"
                                        class="absolute right-3 top-2.5 w-4 h-4 text-gray-400 hover:text-gray-600"
                                    >
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                <!-- Recent Searches -->
                                <div v-if="searchState.recentSearches.length > 0 && !searchState.query" class="space-y-2">
                                    <p class="text-xs text-gray-500 font-medium">Recent Searches</p>
                                    <div class="flex flex-wrap gap-1">
                                        <button
                                            v-for="search in searchState.recentSearches"
                                            :key="search"
                                            @click="handleSearchInput(search)"
                                            class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors"
                                        >
                                            {{ search }}
                                        </button>
                                    </div>
                                </div>

                                <!-- Search Loading -->
                                <div v-if="searchState.isSearching" class="text-center py-2">
                                    <div class="w-4 h-4 border-2 border-teal-200 border-t-teal-500 rounded-full animate-spin mx-auto"></div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="flex-1 max-w-2xl mx-auto lg:mx-0">
                        <!-- Search Results Header -->
                        <div v-if="searchState.query && searchState.results.length > 0" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    Search Results for "{{ searchState.query }}"
                                </h3>
                                <span class="text-sm text-gray-500">{{ searchState.results.length }} posts found</span>
                            </div>
                        </div>

                        <!-- No Search Results -->
                        <div v-if="searchState.query && searchState.results.length === 0 && !searchState.isSearching" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6 text-center">
                            <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No posts found</h3>
                            <p class="text-gray-500">Try searching with different keywords or hashtags</p>
                        </div>

                        <!-- Stories Section -->
                        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6 h-[140px] hover:shadow-md transition-all duration-200">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-semibold text-gray-900">Stories</h3>
                                <button
                                    @click="showCreateStory = true"
                                    class="text-xs font-medium"
                                    style="color: #17C3B2;"
                                >
                                    Add Story
                                </button>
                            </div>
                            <div class="flex space-x-3 overflow-x-auto pb-2 h-20">
                                <!-- Add Story Button -->
                                <div class="flex-shrink-0">
                                    <button
                                        @click="showCreateStory = true"
                                        class="w-12 h-12 rounded-full flex items-center justify-center border-2 border-dashed transition-all duration-200 hover:scale-105 active:scale-95 shadow-sm hover:shadow-md"
                                        style="background: linear-gradient(135deg, #17C3B2, #20D7C5); border-color: #17C3B2;"
                                    >
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                                        </svg>
                                    </button>
                                    <p class="text-xs text-gray-500 text-center mt-1">Add Story</p>
                                </div>

                                <!-- Story Items -->
                                <div
                                    v-for="story in state.stories"
                                    :key="`${story.source || 'local'}-${story.user_id}`"
                                    class="flex-shrink-0 cursor-pointer"
                                    @click="viewStory(story)"
                                >
                                    <div class="relative">
                                        <!-- Instagram Story Ring -->
                                        <div
                                            v-if="story.source === 'instagram'"
                                            class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400 p-0.5"
                                        >
                                            <div class="w-full h-full rounded-full bg-white p-0.5">
                                                <img
                                                    :src="story.profile_image || story.user_avatar || '/images/default-avatar.svg'"
                                                    :alt="story.username"
                                                    class="w-full h-full rounded-full object-cover"
                                                />
                                            </div>
                                        </div>
                                        <!-- Regular Story Ring -->
                                        <div
                                            v-else
                                            class="w-12 h-12 rounded-full bg-gradient-to-br from-pink-400 to-purple-500 p-0.5"
                                        >
                                            <div class="w-full h-full rounded-full bg-white p-0.5">
                                                <img
                                                    :src="story.user_avatar || story.profile_image || '/images/default-avatar.svg'"
                                                    :alt="story.username"
                                                    class="w-full h-full rounded-full object-cover"
                                                />
                                            </div>
                                        </div>

                                        <!-- Story Count Badge -->
                                        <div v-if="story.story_count > 1" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                            {{ story.story_count }}
                                        </div>

                                        <!-- Unviewed Badge -->
                                        <div v-else-if="story.has_unviewed" class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white"></div>

                                        <!-- Instagram Badge -->
                                        <div v-if="story.source === 'instagram'" class="absolute -bottom-1 -right-1 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full p-1">
                                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-xs text-gray-700 text-center mt-1 max-w-12 truncate">
                                        {{ story.username || story.user?.name || 'Anonymous' }}
                                    </p>
                                </div>

                                <!-- Empty State for Stories -->
                                <div v-if="state.stories.length === 0" class="flex-1 flex flex-col items-center justify-center h-20">
                                    <svg class="w-6 h-6 text-gray-300 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                    </svg>
                                    <p class="text-xs text-gray-500">No stories yet</p>
                                    <p class="text-xs text-gray-400">Be the first to share a story!</p>
                                </div>
                            </div>
                        </div>




                        <!-- Feed Section -->
                        <div class="space-y-6">
                            <!-- Loading State -->
                            <div v-if="state.loading && state.posts.length === 0" class="text-center py-12">
                                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                <p class="text-gray-500">Loading amazing content...</p>
                            </div>

                            <!-- Empty State -->
                            <div v-else-if="state.posts.length === 0" class="text-center py-16">
                                <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <svg class="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-3">No posts yet</h3>
                                <p class="text-gray-500 mb-6 max-w-md mx-auto">Be the first to share health insights with the community!</p>
                                <button
                                    @click="showCreatePost = true"
                                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium rounded-full hover:from-blue-600 hover:to-cyan-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                                >
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                    Create First Post
                                </button>
                            </div>

                            <!-- Posts List -->
                            <div v-else>
                                <article
                                    v-for="post in (searchState.query ? searchState.results : state.posts)"
                                    :key="post.id"
                                    class="bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200 mb-4 sm:mb-6"
                                >
                                    <!-- Post Header -->
                                    <div class="p-4 sm:p-6 pb-3 sm:pb-4">
                                        <div class="flex items-center justify-between mb-3 sm:mb-4">
                                            <div class="flex items-center space-x-3">
                                                <button
                                                    @click="openUserProfile(post.user?.id)"
                                                    class="w-10 h-10 sm:w-12 sm:h-12 rounded-full overflow-hidden border-2 border-gray-200 hover:border-blue-400 transition-colors"
                                                >
                                                    <img
                                                        :src="post.user?.profile_image ?
                                                            (post.user.profile_image.startsWith('http') ?
                                                                post.user.profile_image :
                                                                `/storage/${post.user.profile_image}`) :
                                                            '/images/default-avatar.svg'"
                                                        :alt="post.user?.name || 'Anonymous'"
                                                        class="w-full h-full object-cover"
                                                    />
                                                </button>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center space-x-2">
                                                        <button
                                                            @click="openUserProfile(post.user?.id)"
                                                            class="font-semibold text-gray-900 hover:text-blue-600 transition-colors text-left text-sm sm:text-base"
                                                        >
                                                            {{ post.user?.name || 'Anonymous' }}
                                                        </button>
                                                        <!-- Instagram Badge -->
                                                        <div v-if="post.source === 'instagram'" class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-br from-purple-500 to-pink-500" title="Instagram Post">
                                                            <svg class="w-3.5 h-3.5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                                            </svg>
                                                        </div>
                                                    </div>

                                                    <p class="text-xs sm:text-sm text-gray-500 mt-1">
                                                        {{ formatDate(post.published_at || post.created_at) }}
                                                        <span v-if="post.source === 'instagram' && (post.instagram_username || post.username)" class="ml-1">
                                                            @{{ post.instagram_username || post.username }}
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>

                                            <!-- Post Actions Menu -->
                                            <div class="relative post-menu-container">
                                                <button
                                                    @click="togglePostMenu(post.id)"
                                                    class="p-1.5 sm:p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
                                                >
                                                    <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                                    </svg>
                                                </button>

                                                <!-- Dropdown Menu -->
                                                <div
                                                    v-if="showPostMenu[post.id]"
                                                    class="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10"
                                                >
                                                    <button
                                                        v-if="post.can_delete"
                                                        @click="deletePost(post); showPostMenu[post.id] = false"
                                                        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                                    >
                                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                        Delete Post
                                                    </button>
                                                    <button
                                                        v-if="!post.can_delete"
                                                        @click="reportPost(post); showPostMenu[post.id] = false"
                                                        class="w-full text-left px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 transition-colors"
                                                    >
                                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                                        </svg>
                                                        Report Post
                                                    </button>
                                                </div>
                                            </div>
                                        </div>


                                    </div>

                                    <!-- Media -->
                                    <div v-if="post.media_url" class="px-6 pb-4">
                                        <div class="relative rounded-xl overflow-hidden bg-gray-100">
                                            <!-- Video Content -->
                                            <div v-if="post.content_type === 'video'" class="relative">
                                                <!-- Video Element with Multiple Sources -->
                                                <video
                                                    v-if="!videoStates[post.id]?.unavailable"
                                                    :data-post-id="post.id"
                                                    :poster="post.thumbnail_url || post.media_url"
                                                    class="w-full object-cover cursor-pointer"
                                                    style="aspect-ratio: 9/16; height: 85vh; min-height: 600px;"
                                                    muted
                                                    loop
                                                    playsinline
                                                    preload="metadata"
                                                    @click.stop="toggleVideoPlay(post.id)"
                                                    @loadedmetadata="onVideoLoaded(post.id)"
                                                    @play="videoStates[post.id] && (videoStates[post.id].playing = true)"
                                                    @pause="videoStates[post.id] && (videoStates[post.id].playing = false)"
                                                    @error="handleVideoLoadError(post.id, $event)"
                                                >
                                                    <!-- Primary video source -->
                                                    <source v-if="post.video_url && post.video_url !== post.media_url" :src="post.video_url" type="video/mp4">
                                                    <!-- Fallback to media_url -->
                                                    <source v-if="post.media_url" :src="post.media_url" type="video/mp4">
                                                    <!-- Instagram CDN fallback -->
                                                    <source v-if="post.source === 'instagram' && post.external_url" :src="post.external_url" type="video/mp4">
                                                    Your browser does not support the video tag.
                                                </video>
                                                
                                                <!-- Video Unavailable Placeholder -->
                                                <div v-else class="w-full bg-gray-900 flex items-center justify-center cursor-pointer"
                                                     style="aspect-ratio: 9/16; height: 85vh; min-height: 600px;"
                                                     @click="retryVideoLoad(post.id)">
                                                    <div class="text-center text-white">
                                                        <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                                        </svg>
                                                        <p class="text-lg font-medium">Video Unavailable</p>
                                                        <p class="text-sm text-gray-300 mt-2">
                                                            {{ post.source === 'instagram' ? 'Instagram video expired' : 'Video cannot be loaded' }}
                                                        </p>
                                                        <button class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors">
                                                            Click to Retry
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Play/Pause Button Overlay -->
                                                <div
                                                    v-show="!isVideoPlaying(post.id)"
                                                    class="absolute inset-0 flex items-center justify-center"
                                                    @click.stop="toggleVideoPlay(post.id)"
                                                >
                                                    <div class="bg-black bg-opacity-50 text-white rounded-full p-4 hover:bg-opacity-70 transition-all cursor-pointer">
                                                        <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                                                        </svg>
                                                    </div>
                                                </div>

                                                <!-- Video Badge -->
                                                <div class="absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs font-medium">
                                                    <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                                                    </svg>
                                                    Video
                                                </div>

                                                <!-- Mute/Unmute Button -->
                                                <button
                                                    @click.stop="toggleVideoMute()"
                                                    class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white rounded-full p-2 hover:bg-opacity-80 transition-all z-10"
                                                >
                                                    <svg v-if="isVideoMuted()" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                    </svg>
                                                    <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.414A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z" clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                            </div>
                                            <!-- Image Content -->
                                            <img
                                                v-else
                                                :src="post.media_url"
                                                :alt="post.caption"
                                                class="w-full object-cover"
                                                style="max-height: 600px; min-height: 300px;"
                                            />
                                            <!-- Instagram External Link Overlay -->
                                            <div v-if="post.source === 'instagram' && post.permalink" class="absolute top-3 right-3">
                                                <a
                                                    :href="post.permalink"
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    class="inline-flex items-center px-2 py-1 bg-black bg-opacity-70 text-white text-xs font-medium rounded-lg hover:bg-opacity-80 transition-all"
                                                >
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                    </svg>
                                                    View on Instagram
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Post Actions - Below Media -->
                                    <div class="px-4 sm:px-6 py-3 border-t border-gray-50">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-6">
                                                <!-- Like Button -->
                                                <button
                                                    @click="toggleLike(post)"
                                                    :class="[
                                                        'flex items-center space-x-2 text-sm font-medium transition-all duration-200',
                                                        post.liked
                                                            ? 'text-red-600 hover:text-red-700'
                                                            : 'text-gray-500 hover:text-red-600'
                                                    ]"
                                                >
                                                    <svg class="w-6 h-6" :fill="post.liked ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                    </svg>
                                                    <span>{{ post.engagement_metrics?.likes || 0 }}</span>
                                                </button>

                                                <!-- Comment Button -->
                                                <button
                                                    @click="toggleComments(post.id)"
                                                    class="flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-blue-600 transition-all duration-200"
                                                >
                                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                    </svg>
                                                    <span>{{ post.engagement_metrics?.comments || 0 }}</span>
                                                </button>

                                                <!-- Share Button -->
                                                <button
                                                    @click="openShareModal(post)"
                                                    class="flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-purple-600 transition-all duration-200"
                                                >
                                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                                    </svg>
                                                    <span>Share</span>
                                                </button>

                                                <!-- Save Button -->
                                                <button
                                                    @click="toggleSave(post)"
                                                    :class="[
                                                        'flex items-center space-x-2 text-sm font-medium transition-all duration-200',
                                                        post.saved
                                                            ? 'text-green-600 hover:text-green-700'
                                                            : 'text-gray-500 hover:text-green-600'
                                                    ]"
                                                >
                                                    <svg class="w-6 h-6" :fill="post.saved ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                                    </svg>
                                                    <span>{{ post.engagement_metrics?.saves || 0 }}</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Post Content - Below Actions -->
                                    <div v-if="post.caption" class="px-4 sm:px-6 pb-3 sm:pb-4">
                                        <div class="text-gray-800 leading-relaxed text-sm sm:text-base">
                                            <!-- Truncated text (2-3 lines) -->
                                            <p
                                                v-if="!showFullText[post.id]"
                                                class="line-clamp-3"
                                                style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;"
                                            >
                                                {{ post.caption }}
                                            </p>

                                            <!-- Full text -->
                                            <p v-else class="whitespace-pre-wrap">{{ post.caption }}</p>

                                            <!-- Show more/less button -->
                                            <button
                                                v-if="post.caption && post.caption.length > 150"
                                                @click="toggleFullText(post.id)"
                                                class="text-gray-500 hover:text-gray-700 text-sm font-medium mt-1 transition-colors"
                                            >
                                                {{ showFullText[post.id] ? 'Show less' : 'Show more' }}
                                            </button>
                                        </div>

                                        <!-- Health Topics - Only show for local posts -->
                                        <div v-if="post.source !== 'instagram' && post.health_topics && post.health_topics.length > 0" class="flex flex-wrap gap-2 mt-3">
                                            <!-- Show first 2 topics -->
                                            <span
                                                v-for="topic in (showAllTopics[post.id] ? post.health_topics : post.health_topics.slice(0, 2))"
                                                :key="topic"
                                                class="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-xs font-medium rounded-full"
                                            >
                                                #{{ topic }}
                                            </span>

                                            <!-- Show count and expand button if more than 2 topics -->
                                            <button
                                                v-if="post.health_topics.length > 2 && !showAllTopics[post.id]"
                                                @click="toggleAllTopics(post.id)"
                                                class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"
                                            >
                                                +{{ post.health_topics.length - 2 }} more
                                            </button>

                                            <!-- Show less button when expanded -->
                                            <button
                                                v-if="post.health_topics.length > 2 && showAllTopics[post.id]"
                                                @click="toggleAllTopics(post.id)"
                                                class="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"
                                            >
                                                Show less
                                            </button>
                                        </div>
                                    </div>



                                    <!-- Comments Section -->
                                    <div v-if="showComments[post.id]" class="border-t border-gray-50 bg-gray-50">
                                        <!-- Add Comment -->
                                        <div class="p-4 border-b border-gray-100">
                                            <div class="flex space-x-3">
                                                <div class="w-8 h-8 rounded-full overflow-hidden border border-gray-200 flex-shrink-0">
                                                    <img
                                                        :src="$page.props.auth.user?.profile_image ?
                                                            ($page.props.auth.user.profile_image.startsWith('http') ?
                                                                $page.props.auth.user.profile_image :
                                                                `/storage/${$page.props.auth.user.profile_image}`) :
                                                            '/images/default-avatar.svg'"
                                                        :alt="$page.props.auth.user?.name || 'You'"
                                                        class="w-full h-full object-cover"
                                                    />
                                                </div>
                                                <div class="flex-1">
                                                    <textarea
                                                        v-model="newComment.content"
                                                        @keydown.enter.prevent="addComment(post.id)"
                                                        placeholder="Add a comment..."
                                                        class="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                                        rows="2"
                                                    ></textarea>
                                                    <div class="flex justify-end mt-2">
                                                        <button
                                                            @click="addComment(post.id)"
                                                            :disabled="!newComment.content.trim()"
                                                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                                        >
                                                            Comment
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Comments List -->
                                        <div class="p-4 space-y-4 max-h-96 overflow-y-auto">
                                            <div
                                                v-for="comment in commentsData[post.id]"
                                                :key="comment.id"
                                                class="space-y-3"
                                            >
                                                <!-- Main Comment -->
                                                <div class="flex space-x-3">
                                                    <div class="w-8 h-8 rounded-full overflow-hidden border border-gray-200 flex-shrink-0">
                                                        <img
                                                            :src="comment.user?.profile_image ?
                                                                (comment.user.profile_image.startsWith('http') ?
                                                                    comment.user.profile_image :
                                                                    `/storage/${comment.user.profile_image}`) :
                                                                '/images/default-avatar.svg'"
                                                            :alt="comment.user?.name || 'Anonymous'"
                                                            class="w-full h-full object-cover"
                                                        />
                                                    </div>
                                                    <div class="flex-1">
                                                        <div class="bg-white rounded-lg p-3">
                                                            <div class="flex items-center justify-between mb-1">
                                                                <p class="text-sm font-medium text-gray-900">{{ comment.user?.name || 'Anonymous' }}</p>
                                                                <div class="flex items-center space-x-2">
                                                                    <p class="text-xs text-gray-500">{{ formatDate(comment.created_at) }}</p>
                                                                    <!-- Comment Menu -->
                                                                    <div class="relative comment-menu-container" v-if="comment.user_id === $page.props.auth.user.id">
                                                                        <button
                                                                            @click="toggleCommentMenu(comment.id)"
                                                                            class="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
                                                                        >
                                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                                                            </svg>
                                                                        </button>
                                                                        <!-- Dropdown Menu -->
                                                                        <div
                                                                            v-if="showCommentMenu[comment.id]"
                                                                            class="absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10"
                                                                        >
                                                                            <button
                                                                                @click="deleteComment(post.id, comment.id); showCommentMenu[comment.id] = false"
                                                                                class="w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                                                            >
                                                                                Delete
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <p class="text-sm text-gray-800 mb-2">{{ comment.content }}</p>

                                                            <!-- Comment Actions -->
                                                            <div class="flex items-center space-x-4 text-xs">
                                                                <button
                                                                    @click="toggleCommentLike(post.id, comment.id)"
                                                                    :class="[
                                                                        'flex items-center space-x-1 transition-colors',
                                                                        comment.user_reaction === 'like'
                                                                            ? 'text-red-600 hover:text-red-700'
                                                                            : 'text-gray-500 hover:text-red-600'
                                                                    ]"
                                                                >
                                                                    <svg class="w-4 h-4" :fill="comment.user_reaction === 'like' ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                                    </svg>
                                                                    <span>{{ comment.reaction_counts?.like || 0 }}</span>
                                                                </button>

                                                                <button
                                                                    @click="toggleCommentReply(comment.id)"
                                                                    class="text-gray-500 hover:text-blue-600 transition-colors"
                                                                >
                                                                    Reply
                                                                </button>
                                                            </div>
                                                        </div>

                                                        <!-- Reply Form -->
                                                        <div v-if="showReplyForm[comment.id]" class="mt-3 ml-4">
                                                            <div class="flex space-x-2">
                                                                <textarea
                                                                    v-model="replyContent[comment.id]"
                                                                    @keydown.enter.prevent="addReply(post.id, comment.id)"
                                                                    placeholder="Write a reply..."
                                                                    class="flex-1 p-2 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                                                    rows="2"
                                                                ></textarea>
                                                                <div class="flex flex-col space-y-1">
                                                                    <button
                                                                        @click="addReply(post.id, comment.id)"
                                                                        :disabled="!replyContent[comment.id]?.trim()"
                                                                        class="px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                                                    >
                                                                        Reply
                                                                    </button>
                                                                    <button
                                                                        @click="cancelReply(comment.id)"
                                                                        class="px-3 py-1 bg-gray-200 text-gray-700 text-xs font-medium rounded-lg hover:bg-gray-300 transition-colors"
                                                                    >
                                                                        Cancel
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Replies -->
                                                        <div v-if="comment.replies && comment.replies.length > 0" class="mt-3 ml-4 space-y-2">
                                                            <div
                                                                v-for="reply in comment.replies"
                                                                :key="reply.id"
                                                                class="flex space-x-2"
                                                            >
                                                                <div class="w-6 h-6 rounded-full overflow-hidden border border-gray-200 flex-shrink-0">
                                                                    <img
                                                                        :src="reply.user?.profile_image ?
                                                                            (reply.user.profile_image.startsWith('http') ?
                                                                                reply.user.profile_image :
                                                                                `/storage/${reply.user.profile_image}`) :
                                                                            '/images/default-avatar.svg'"
                                                                        :alt="reply.user?.name || 'Anonymous'"
                                                                        class="w-full h-full object-cover"
                                                                    />
                                                                </div>
                                                                <div class="flex-1">
                                                                    <div class="bg-gray-50 rounded-lg p-2">
                                                                        <div class="flex items-center justify-between mb-1">
                                                                            <p class="text-xs font-medium text-gray-900">{{ reply.user?.name || 'Anonymous' }}</p>
                                                                            <div class="flex items-center space-x-2">
                                                                                <p class="text-xs text-gray-500">{{ formatDate(reply.created_at) }}</p>
                                                                                <div class="relative comment-menu-container" v-if="reply.user_id === $page.props.auth.user.id">
                                                                                    <button
                                                                                        @click="toggleCommentMenu(reply.id)"
                                                                                        class="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
                                                                                    >
                                                                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                                                                        </svg>
                                                                                    </button>
                                                                                    <div
                                                                                        v-if="showCommentMenu[reply.id]"
                                                                                        class="absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10"
                                                                                    >
                                                                                        <button
                                                                                            @click="deleteComment(post.id, reply.id); showCommentMenu[reply.id] = false"
                                                                                            class="w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                                                                        >
                                                                                            Delete
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <p class="text-xs text-gray-800 mb-1">{{ reply.content }}</p>

                                                                        <!-- Reply Actions -->
                                                                        <div class="flex items-center space-x-3 text-xs">
                                                                            <button
                                                                                @click="toggleCommentLike(post.id, reply.id)"
                                                                                :class="[
                                                                                    'flex items-center space-x-1 transition-colors',
                                                                                    reply.user_reaction === 'like'
                                                                                        ? 'text-red-600 hover:text-red-700'
                                                                                        : 'text-gray-500 hover:text-red-600'
                                                                                ]"
                                                                            >
                                                                                <svg class="w-3 h-3" :fill="reply.user_reaction === 'like' ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                                                </svg>
                                                                                <span>{{ reply.reaction_counts?.like || 0 }}</span>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-if="!commentsData[post.id] || commentsData[post.id].length === 0" class="text-center py-6">
                                                <svg class="w-8 h-8 text-gray-300 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                                <p class="text-sm text-gray-500">No comments yet</p>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>

                            <!-- Auto-load indicator -->
                            <div v-if="hasMorePosts" class="text-center py-8" ref="loadMoreTrigger">
                                <div v-if="state.loading" class="flex items-center justify-center">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                    <span class="ml-3 text-gray-500">Loading more posts...</span>
                                </div>
                            </div>

                            <!-- End of posts indicator -->
                            <div v-else-if="state.posts.length > 0" class="text-center py-8">
                                <div class="text-gray-400 text-sm">
                                    🎉 You've reached the end! No more posts to load.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Sidebar - Saved Posts & Instagram -->
                    <div class="w-48 xl:w-56 flex-shrink-0 hidden lg:block">
                        <div class="sticky top-28 space-y-4">
                            <!-- Saved Posts -->
                            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 h-[140px] flex flex-col justify-center hover:shadow-md transition-all duration-200">
                                <button
                                    @click="toggleSavedPosts"
                                    class="w-full p-4 hover:bg-gray-50 rounded-2xl transition-colors flex flex-col items-center text-center"
                                >
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mb-2 shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <span class="text-sm font-semibold text-gray-900 block">Saved Posts</span>
                                        <span class="text-xs text-gray-500">{{ totalSavedPosts }} saved</span>
                                    </div>
                                </button>
                            </div>

                            <!-- Instagram Connect - Only show when NOT connected -->
                            <div v-if="!instagramState.connected" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-4">
                                <div class="flex items-center space-x-2 mb-3">
                                    <div class="w-5 h-5 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">Connect Instagram</span>
                                </div>
                                <p class="text-xs text-gray-600 mb-3">
                                    Connect your Instagram business account to share health-related content with the community.
                                </p>
                                <button
                                    @click="connectInstagram"
                                    :disabled="instagramState.loading || instagramState.connecting"
                                    class="w-full px-3 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-medium rounded-lg hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                                >
                                    <span v-if="instagramState.connecting" class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Connecting...
                                    </span>
                                    <span v-else>Connect Instagram</span>
                                </button>
                            </div>

                            <!-- Instagram Management - Only show when connected -->
                            <div v-if="instagramState.connected && instagramState.account" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-4">
                                <div class="flex items-center space-x-2 mb-3">
                                    <div class="w-5 h-5 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">Instagram</span>
                                </div>
                                <div class="text-xs text-gray-600 mb-3">
                                    @{{ instagramState.account.username }}
                                </div>
                                <div class="space-y-2">
                                    <button
                                        @click="syncInstagramContent"
                                        :disabled="instagramState.syncing"
                                        class="w-full px-3 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        {{ instagramState.syncing ? 'Syncing...' : 'Sync Content' }}
                                    </button>
                                    <button
                                        @click="disconnectInstagram"
                                        :disabled="instagramState.loading"
                                        class="w-full px-3 py-2 bg-gray-200 text-gray-700 text-xs font-medium rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Disconnect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Post Modal -->
        <div v-if="showCreatePost" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-900">Create Post</h3>
                        <button
                            @click="showCreatePost = false"
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <label class="block text-sm font-medium text-gray-700">What's on your mind?</label>
                                <button
                                    @click="openAiWriter"
                                    class="flex items-center space-x-2 px-3 py-1.5 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span>Write with AI</span>
                                </button>
                            </div>
                            <textarea
                                v-model="newPost.caption"
                                @input="generateHashtagSuggestions($event.target.value)"
                                placeholder="Share your health insights, tips, or experiences..."
                                class="w-full p-4 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                                rows="4"
                            ></textarea>
                        </div>

                        <!-- Hashtag Suggestions -->
                        <div v-if="newPost.suggestedHashtags.length > 0">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Suggested Hashtags</label>
                            <div class="flex flex-wrap gap-2 mb-3">
                                <button
                                    v-for="hashtag in newPost.suggestedHashtags"
                                    :key="hashtag"
                                    @click="addHashtag(hashtag)"
                                    :disabled="newPost.hashtags.includes(hashtag)"
                                    class="px-3 py-1 text-sm rounded-full border transition-colors"
                                    :class="newPost.hashtags.includes(hashtag)
                                        ? 'bg-teal-100 text-teal-700 border-teal-300 cursor-not-allowed'
                                        : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-teal-50 hover:border-teal-300 cursor-pointer'"
                                >
                                    {{ hashtag }}
                                </button>
                            </div>
                        </div>

                        <!-- Selected Hashtags -->
                        <div v-if="newPost.hashtags.length > 0">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Selected Hashtags</label>
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span
                                    v-for="hashtag in newPost.hashtags"
                                    :key="hashtag"
                                    class="inline-flex items-center px-3 py-1 text-sm bg-teal-100 text-teal-700 rounded-full"
                                >
                                    {{ hashtag }}
                                    <button
                                        @click="removeHashtag(hashtag)"
                                        class="ml-2 w-4 h-4 text-teal-500 hover:text-teal-700"
                                    >
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Add Image (Optional)</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors">
                                <input
                                    type="file"
                                    @change="handlePostMediaChange"
                                    accept="image/*"
                                    class="hidden"
                                    id="post-media-input"
                                />
                                <label for="post-media-input" class="cursor-pointer">
                                    <div v-if="newPost.mediaPreview" class="mb-4">
                                        <img :src="newPost.mediaPreview" alt="Preview" class="max-h-32 mx-auto rounded-lg" />
                                    </div>
                                    <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <p class="text-sm text-gray-500">Click to upload an image</p>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-100">
                        <button
                            @click="showCreatePost = false"
                            class="px-6 py-2 text-gray-700 font-medium rounded-lg hover:bg-gray-100 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            @click="createPost"
                            :disabled="!newPost.caption.trim()"
                            class="medroid-post-btn px-6 py-2 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                        >
                            Share Post
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Image Cropper Modal -->
        <ImageCropper
            :show="showImageCropper"
            :imageFile="cropperImageFile"
            @cropped="handleCroppedImage"
            @cancel="cancelCropping"
        />

        <!-- User Profile Modal -->
        <UserProfile
            :show="showUserProfile"
            :userId="selectedUserId"
            @close="closeUserProfile"
            @open-post-detail="openPostDetail"
        />

        <!-- Post Detail Modal -->
        <div v-if="showPostDetail && selectedPost" class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div
                    class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
                    @click="closePostDetail"
                ></div>

                <!-- Modal panel -->
                <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Post Details</h3>
                        <button
                            @click="closePostDetail"
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Post Content -->
                    <div class="space-y-4">
                        <!-- Post Header -->
                        <div class="flex items-center space-x-3">
                            <button
                                @click="openUserProfile(selectedPost.user?.id); closePostDetail()"
                                class="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 hover:border-blue-400 transition-colors"
                            >
                                <img
                                    :src="selectedPost.user?.profile_image ?
                                        (selectedPost.user.profile_image.startsWith('http') ?
                                            selectedPost.user.profile_image :
                                            `/storage/${selectedPost.user.profile_image}`) :
                                        '/images/default-avatar.svg'"
                                    :alt="selectedPost.user?.name || 'Anonymous'"
                                    class="w-full h-full object-cover"
                                />
                            </button>
                            <div>
                                <button
                                    @click="openUserProfile(selectedPost.user?.id); closePostDetail()"
                                    class="font-semibold text-gray-900 hover:text-blue-600 transition-colors text-left"
                                >
                                    {{ selectedPost.user?.name || 'Anonymous' }}
                                </button>
                                <p class="text-sm text-gray-500">{{ formatDate(selectedPost.created_at) }}</p>
                            </div>
                        </div>

                        <!-- Post Media -->
                        <div v-if="selectedPost.media_url" class="rounded-xl overflow-hidden bg-gray-100">
                            <img
                                :src="selectedPost.media_url"
                                :alt="selectedPost.caption"
                                class="w-full max-h-96 object-contain"
                            />
                        </div>

                        <!-- Post Caption -->
                        <div class="prose max-w-none">
                            <p class="text-gray-800 leading-relaxed whitespace-pre-wrap">{{ selectedPost.caption }}</p>
                        </div>

                        <!-- Health Topics -->
                        <div v-if="selectedPost.health_topics?.length" class="flex flex-wrap gap-2">
                            <span
                                v-for="topic in selectedPost.health_topics"
                                :key="topic"
                                class="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-xs font-medium rounded-full"
                            >
                                {{ topic }}
                            </span>
                        </div>

                        <!-- Post Actions -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                            <div class="flex items-center space-x-6">
                                <button
                                    @click="toggleLike(selectedPost)"
                                    :class="[
                                        'flex items-center space-x-2 text-sm font-medium transition-all duration-200',
                                        selectedPost.liked
                                            ? 'text-red-600 hover:text-red-700'
                                            : 'text-gray-500 hover:text-red-600'
                                    ]"
                                >
                                    <svg class="w-5 h-5" :fill="selectedPost.liked ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                    <span>{{ selectedPost.engagement_metrics?.likes || 0 }}</span>
                                </button>

                                <button
                                    @click="toggleComments(selectedPost.id)"
                                    class="flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-blue-600 transition-colors"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                    <span>{{ selectedPost.engagement_metrics?.comments || 0 }}</span>
                                </button>

                                <button
                                    @click="toggleSave(selectedPost)"
                                    :class="[
                                        'flex items-center space-x-2 text-sm font-medium transition-colors',
                                        selectedPost.saved
                                            ? 'text-blue-600 hover:text-blue-700'
                                            : 'text-gray-500 hover:text-blue-600'
                                    ]"
                                >
                                    <svg class="w-5 h-5" :fill="selectedPost.saved ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Story Viewer Modal -->
        <div v-if="showStoryViewer && currentStoryGroup && currentStory" class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50">
            <div class="relative w-full max-w-md mx-4 h-[80vh] bg-black rounded-2xl overflow-hidden">
                <!-- Close Button -->
                <button
                    @click="closeStoryViewer"
                    class="absolute top-4 right-4 z-10 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white hover:bg-opacity-70 transition-colors"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <!-- Story Progress Bar -->
                <div class="absolute top-4 left-4 right-16 z-10 flex space-x-1">
                    <div
                        v-for="(story, index) in userStories"
                        :key="story.id"
                        class="flex-1 h-1 bg-white bg-opacity-30 rounded-full overflow-hidden"
                    >
                        <div
                            class="h-full bg-white transition-all duration-100"
                            :style="{
                                width: index < currentStoryIndex ? '100%' :
                                       index === currentStoryIndex ? `${storyProgress}%` : '0%'
                            }"
                        ></div>
                    </div>
                </div>

                <!-- Story Header -->
                <div class="absolute top-12 left-4 right-4 z-10 flex items-center space-x-3 text-white">
                    <div class="w-8 h-8 rounded-full overflow-hidden border-2 border-white">
                        <img
                            :src="currentStoryGroup.user_avatar || '/images/default-avatar.svg'"
                            :alt="currentStoryGroup.username"
                            class="w-full h-full object-cover"
                        />
                    </div>
                    <div>
                        <p class="font-semibold text-sm">{{ currentStoryGroup.username || 'Anonymous' }}</p>
                        <p class="text-xs text-gray-300">{{ formatDate(currentStory.created_at) }}</p>
                    </div>
                </div>

                <!-- Story Content -->
                <div
                    class="w-full h-full flex items-center justify-center bg-gray-900"
                    @mousedown="pauseStory"
                    @mouseup="resumeStory"
                    @touchstart="pauseStory"
                    @touchend="resumeStory"
                >
                    <img
                        :src="currentStory.media_url"
                        :alt="currentStory.caption || 'Story'"
                        class="w-full h-full object-contain"
                        @error="$event.target.src = '/images/default-avatar.svg'"
                    />
                </div>

                <!-- Story Caption -->
                <div v-if="currentStory.caption" class="absolute bottom-4 left-4 right-4 z-10">
                    <div class="bg-black bg-opacity-50 rounded-lg p-3">
                        <p class="text-white text-sm">{{ currentStory.caption }}</p>
                    </div>
                </div>

                <!-- Navigation Areas -->
                <div class="absolute inset-0 flex">
                    <!-- Previous Story Area -->
                    <div
                        @click="previousStory"
                        class="w-1/2 h-full cursor-pointer flex items-center justify-start pl-4"
                    >
                        <div v-if="currentStoryIndex > 0" class="w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </div>
                    </div>
                    <!-- Next Story Area -->
                    <div
                        @click="nextStory"
                        class="w-1/2 h-full cursor-pointer flex items-center justify-end pr-4"
                    >
                        <div v-if="currentStoryIndex < userStories.length - 1" class="w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Story Modal -->
        <div v-if="showCreateStory" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-900">Create Story</h3>
                        <button
                            @click="showCreateStory = false"
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Add Image/Video</label>
                            <div v-if="!newStory.mediaPreview" class="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors">
                                <input
                                    type="file"
                                    accept="image/*,video/*"
                                    class="hidden"
                                    id="story-media-input"
                                    @change="handleStoryMediaChange"
                                />
                                <label for="story-media-input" class="cursor-pointer">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <p class="text-gray-500 mb-2">Click to upload media</p>
                                    <p class="text-xs text-gray-400">Images and videos up to 10MB</p>
                                </label>
                            </div>

                            <!-- Media Preview -->
                            <div v-if="newStory.mediaPreview" class="relative">
                                <img
                                    :src="newStory.mediaPreview"
                                    alt="Story preview"
                                    class="w-full h-64 object-cover rounded-xl"
                                />
                                <button
                                    @click="resetStoryForm"
                                    class="absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Caption Input -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Caption (Optional)</label>
                            <textarea
                                v-model="newStory.caption"
                                placeholder="Add a caption to your story..."
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                rows="3"
                                maxlength="500"
                            ></textarea>
                            <p class="text-xs text-gray-500 mt-1">{{ newStory.caption.length }}/500 characters</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-100">
                        <button
                            @click="showCreateStory = false"
                            class="px-6 py-2 text-gray-700 font-medium rounded-lg hover:bg-gray-100 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            @click="createStory"
                            :disabled="!newStory.media"
                            class="medroid-story-btn px-6 py-2 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                        >
                            Share Story
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Writer Modal -->
        <div v-if="aiWriting.showAiModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                            <svg class="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Write with AI
                        </h3>
                        <button @click="closeAiModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">What would you like to write about?</label>
                            <textarea
                                v-model="aiWriting.prompt"
                                placeholder="e.g., 'Write a motivational post about staying healthy during winter' or 'Share tips for managing stress at work'"
                                class="w-full p-4 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                rows="3"
                            ></textarea>
                        </div>

                        <div class="flex space-x-3">
                            <button
                                @click="generateAiContent"
                                :disabled="!aiWriting.prompt.trim() || aiWriting.isGenerating"
                                class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                            >
                                <svg v-if="aiWriting.isGenerating" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ aiWriting.isGenerating ? 'Generating...' : 'Generate Content' }}
                            </button>
                        </div>

                        <div v-if="aiWriting.generatedContent" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Generated Content</label>
                                <div class="p-4 bg-gray-50 border border-gray-200 rounded-xl">
                                    <p class="text-gray-800 whitespace-pre-wrap">{{ aiWriting.generatedContent }}</p>
                                </div>
                            </div>

                            <div class="flex space-x-3">
                                <button
                                    @click="useAiContent"
                                    class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                                >
                                    Use This Content
                                </button>
                                <button
                                    @click="generateAiContent"
                                    :disabled="aiWriting.isGenerating"
                                    class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"
                                >
                                    Regenerate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Panel -->
        <div v-if="showNotificationPanel" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden">
                <!-- Header -->
                <div class="p-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                    <div class="flex items-center space-x-2">
                        <button
                            v-if="notificationCount > 0"
                            @click="markAllNotificationsAsRead"
                            class="text-sm text-blue-600 hover:text-blue-700 font-medium"
                        >
                            Mark all read
                        </button>
                        <button
                            @click="showNotificationPanel = false"
                            class="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Notifications List -->
                <div class="overflow-y-auto max-h-96">
                    <div v-if="activityNotifications.length === 0" class="p-8 text-center">
                        <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                        </svg>
                        <p class="text-gray-500">No notifications yet</p>
                        <p class="text-sm text-gray-400 mt-1">Your activity will appear here</p>
                    </div>

                    <div v-else class="divide-y divide-gray-100">
                        <div
                            v-for="notification in activityNotifications"
                            :key="notification.id"
                            @click="markNotificationAsRead(notification.id)"
                            class="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                            :class="{ 'bg-blue-50': !notification.read }"
                        >
                            <div class="flex items-start space-x-3">
                                <!-- Notification Icon -->
                                <div class="flex-shrink-0 mt-1">
                                    <!-- Like Icon -->
                                    <div v-if="notification.type === 'like'" class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                        </svg>
                                    </div>
                                    <!-- Comment Icon -->
                                    <div v-else-if="notification.type === 'comment'" class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                    </div>
                                    <!-- Save Icon -->
                                    <div v-else-if="notification.type === 'save'" class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                                        </svg>
                                    </div>
                                    <!-- Share Icon -->
                                    <div v-else-if="notification.type === 'share'" class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </div>
                                </div>

                                <!-- Notification Content -->
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm text-gray-900 font-medium">{{ notification.message }}</p>
                                    <p class="text-sm text-gray-500 truncate">{{ notification.post_title }}</p>
                                    <p class="text-xs text-gray-400 mt-1">{{ formatDate(notification.timestamp) }}</p>
                                </div>

                                <!-- Post Thumbnail -->
                                <div v-if="notification.post_image" class="flex-shrink-0">
                                    <img :src="notification.post_image" alt="Post" class="w-10 h-10 rounded object-cover" />
                                </div>

                                <!-- Unread Indicator -->
                                <div v-if="!notification.read" class="flex-shrink-0">
                                    <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Toast Notification System -->
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <div
                v-for="notification in notifications"
                :key="notification.id"
                class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ease-in-out"
                :class="{
                    'border-l-4 border-green-500': notification.type === 'success',
                    'border-l-4 border-red-500': notification.type === 'error',
                    'border-l-4 border-blue-500': notification.type === 'info',
                    'border-l-4 border-yellow-500': notification.type === 'warning'
                }"
            >
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <!-- Success Icon -->
                            <svg v-if="notification.type === 'success'" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <!-- Error Icon -->
                            <svg v-else-if="notification.type === 'error'" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <!-- Info Icon -->
                            <svg v-else-if="notification.type === 'info'" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <!-- Warning Icon -->
                            <svg v-else class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium text-gray-900">{{ notification.message }}</p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button
                                @click="removeNotification(notification.id)"
                                class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Share Modal -->
        <div v-if="shareModal.show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-900">Share Post</h3>
                    <button
                        @click="closeShareModal"
                        class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Post Preview Card -->
                <div v-if="shareModal.post" class="p-6 border-b border-gray-100">
                    <div class="bg-gray-50 rounded-xl p-4">
                        <!-- Post Header -->
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-medium text-sm">
                                    {{ shareModal.post.source === 'instagram' ? 'IG' : (shareModal.post.user?.name?.charAt(0) || 'U') }}
                                </span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 text-sm">
                                    {{ shareModal.post.source === 'instagram'
                                        ? `@${shareModal.post.instagram_username || shareModal.post.username}`
                                        : (shareModal.post.user?.name || 'Anonymous') }}
                                </p>
                                <p class="text-xs text-gray-500">
                                    {{ shareModal.post.source === 'instagram' ? 'Instagram' : 'Medroid AI Community' }}
                                </p>
                            </div>
                        </div>

                        <!-- Post Content Preview -->
                        <div v-if="shareModal.post.media_url" class="mb-3">
                            <img
                                :src="shareModal.post.media_url"
                                :alt="shareModal.post.caption || 'Post image'"
                                class="w-full h-32 object-cover rounded-lg"
                            />
                        </div>

                        <p v-if="shareModal.post.caption" class="text-sm text-gray-700 line-clamp-3">
                            {{ shareModal.post.caption }}
                        </p>
                    </div>
                </div>

                <!-- Share Options -->
                <div class="p-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-4">Share via</h4>

                    <!-- Primary Share Options -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <!-- WhatsApp -->
                        <button
                            @click="shareToWhatsApp"
                            class="flex items-center justify-center space-x-3 p-4 bg-green-50 hover:bg-green-100 rounded-xl transition-colors group"
                        >
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.700"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700 group-hover:text-green-700">WhatsApp</span>
                        </button>

                        <!-- LinkedIn -->
                        <button
                            @click="shareToLinkedIn"
                            class="flex items-center justify-center space-x-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors group"
                        >
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700 group-hover:text-blue-700">LinkedIn</span>
                        </button>
                    </div>

                    <!-- Secondary Share Options -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <!-- Email -->
                        <button
                            @click="shareToEmail"
                            class="flex items-center justify-center space-x-3 p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors group"
                        >
                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700 group-hover:text-gray-800">Email</span>
                        </button>

                        <!-- SMS -->
                        <button
                            @click="shareToSMS"
                            class="flex items-center justify-center space-x-3 p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors group"
                        >
                            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.255-.949L5 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700 group-hover:text-gray-800">SMS</span>
                        </button>
                    </div>

                    <!-- Copy Link -->
                    <button
                        @click="copyShareLink"
                        class="w-full flex items-center justify-center space-x-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors group mb-4"
                    >
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <span class="text-sm font-medium text-purple-700">Copy Link</span>
                    </button>

                    <!-- More Options -->
                    <div class="text-center">
                        <button class="text-sm text-gray-500 hover:text-gray-700 transition-colors">
                            More sharing options
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </AppLayout>
</template>

<style scoped>
/* Medroid Theme Colors */
.medroid-profile-btn {
    background: linear-gradient(135deg, #17C3B2 0%, #8BE9C8 100%);
}

.medroid-profile-btn:hover {
    background: linear-gradient(135deg, #15B3A3 0%, #7DDBB9 100%);
}

.medroid-create-btn {
    background: linear-gradient(135deg, #FF9F6E 0%, #FFB891 100%);
}

.medroid-create-btn:hover {
    background: linear-gradient(135deg, #FF8F5E 0%, #FFA881 100%);
}

.medroid-topic-active {
    background: linear-gradient(135deg, #17C3B2 0%, #8BE9C8 100%);
}

.medroid-icon-bg {
    background: linear-gradient(135deg, #17C3B2 0%, #8BE9C8 100%);
}

/* Story creation button */
.medroid-story-btn {
    background: linear-gradient(135deg, #FF9F6E 0%, #FFB891 100%);
}

.medroid-story-btn:hover {
    background: linear-gradient(135deg, #FF8F5E 0%, #FFA881 100%);
}

/* Post creation button */
.medroid-post-btn {
    background: linear-gradient(135deg, #17C3B2 0%, #8BE9C8 100%);
}

.medroid-post-btn:hover {
    background: linear-gradient(135deg, #15B3A3 0%, #7DDBB9 100%);
}
</style>
