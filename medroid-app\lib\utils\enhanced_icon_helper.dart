import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Enhanced icon helper with support for SVG, icon fonts, and animated icons
class EnhancedIconHelper {
  /// Load SVG icons with customizable properties
  static Widget svgIcon(
    String assetPath, {
    double? size,
    Color? color,
    BoxFit fit = BoxFit.contain,
    String? semanticLabel,
  }) {
    return SvgPicture.asset(
      assetPath,
      width: size,
      height: size,
      colorFilter:
          color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
      fit: fit,
      semanticsLabel: semanticLabel,
    );
  }

  /// FontAwesome icon with enhanced styling
  static Widget fontAwesomeIcon(
    IconData icon, {
    double? size,
    Color? color,
    String? semanticLabel,
  }) {
    return FaIcon(
      icon,
      size: size ?? 24.0,
      color: color,
      semanticLabel: semanticLabel,
    );
  }

  /// Material Design Icons
  static Widget materialIcon(
    IconData icon, {
    double? size,
    Color? color,
    String? semanticLabel,
  }) {
    return Icon(
      icon,
      size: size ?? 24.0,
      color: color,
      semanticLabel: semanticLabel,
    );
  }

  /// Animated icon with rotation effect
  static Widget animatedIcon(
    Widget icon, {
    Duration duration = const Duration(seconds: 1),
    bool isAnimating = false,
  }) {
    return AnimatedRotation(
      turns: isAnimating ? 1.0 : 0.0,
      duration: duration,
      child: icon,
    );
  }

  /// Icon with gradient background
  static Widget gradientIcon(
    Widget icon, {
    required List<Color> colors,
    double? size,
    BorderRadius? borderRadius,
  }) {
    return Container(
      width: size ?? 40,
      height: size ?? 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: colors),
        borderRadius: borderRadius ?? BorderRadius.circular(8),
      ),
      child: Center(child: icon),
    );
  }
}

/// Common app icons using SVG
class AppIcons {
  static Widget instagram({double? size, Color? color}) =>
      EnhancedIconHelper.svgIcon('assets/icons/instagram.svg',
          size: size, color: color);

  static Widget tiktok({double? size, Color? color}) =>
      EnhancedIconHelper.svgIcon('assets/icons/tiktok.svg',
          size: size, color: color);

  static Widget medroid({double? size, Color? color}) =>
      EnhancedIconHelper.svgIcon('assets/icons/medroid.svg',
          size: size, color: color);

  static Widget social({double? size, Color? color}) =>
      EnhancedIconHelper.svgIcon('assets/icons/social.svg',
          size: size, color: color);
}

/// Medical icons using FontAwesome
class MedicalIcons {
  static Widget stethoscope({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.stethoscope,
          size: size, color: color);

  static Widget heartPulse({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.heartPulse,
          size: size, color: color);

  static Widget pills({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.pills,
          size: size, color: color);

  static Widget syringe({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.syringe,
          size: size, color: color);

  static Widget hospital({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.hospital,
          size: size, color: color);

  static Widget userDoctor({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.userDoctor,
          size: size, color: color);

  static Widget calendarCheck({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.calendarCheck,
          size: size, color: color);

  static Widget comments({double? size, Color? color}) =>
      EnhancedIconHelper.fontAwesomeIcon(FontAwesomeIcons.comments,
          size: size, color: color);
}

/// Interface icons using Material Design Icons
class InterfaceIcons {
  static Widget home({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.home_outlined,
          size: size, color: color);

  static Widget search({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.search_outlined,
          size: size, color: color);

  static Widget notifications({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.notifications_outlined,
          size: size, color: color);

  static Widget profile({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.person_outline,
          size: size, color: color);

  static Widget settings({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.settings_outlined,
          size: size, color: color);

  static Widget menu({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.menu, size: size, color: color);

  static Widget close({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.close, size: size, color: color);

  static Widget arrowBack({double? size, Color? color}) =>
      EnhancedIconHelper.materialIcon(Icons.arrow_back_ios,
          size: size, color: color);
}
