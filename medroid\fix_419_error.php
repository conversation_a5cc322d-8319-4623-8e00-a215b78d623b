<?php

/**
 * Fix 419 PAGE EXPIRED Error <PERSON>
 * 
 * This script fixes common Laravel 419 errors by:
 * 1. Clearing all caches
 * 2. Optimizing for production
 * 3. Checking session configuration
 * 4. Verifying CSRF setup
 */

echo "=== FIXING 419 PAGE EXPIRED ERROR ===\n\n";

// Function to execute artisan commands
function execArtisan($command) {
    $fullCommand = "php artisan $command";
    echo "Running: $fullCommand\n";
    $output = shell_exec($fullCommand . ' 2>&1');
    echo $output . "\n";
    return $output;
}

// Step 1: Clear all caches
echo "1. CLEARING ALL CACHES\n";
echo "======================\n";

$commands = [
    'cache:clear',
    'config:clear', 
    'route:clear',
    'view:clear',
    'optimize:clear',
    'session:table', // Create sessions table if using database driver
];

foreach ($commands as $command) {
    execArtisan($command);
}

// Step 2: Optimize for production
echo "\n2. OPTIMIZING FOR PRODUCTION\n";
echo "=============================\n";

$optimizeCommands = [
    'config:cache',
    'route:cache',
    'view:cache',
    'optimize',
];

foreach ($optimizeCommands as $command) {
    execArtisan($command);
}

// Step 3: Check session configuration
echo "\n3. CHECKING SESSION CONFIGURATION\n";
echo "==================================\n";

$sessionDriver = env('SESSION_DRIVER', 'file');
$sessionDomain = env('SESSION_DOMAIN');
$sessionSecure = env('SESSION_SECURE_COOKIE', false);
$sessionSameSite = env('SESSION_SAME_SITE', 'lax');
$appUrl = env('APP_URL');

echo "Session Driver: $sessionDriver\n";
echo "Session Domain: $sessionDomain\n";
echo "Session Secure: " . ($sessionSecure ? 'true' : 'false') . "\n";
echo "Session SameSite: $sessionSameSite\n";
echo "App URL: $appUrl\n";

// Step 4: Check if sessions directory exists and is writable
if ($sessionDriver === 'file') {
    $sessionPath = storage_path('framework/sessions');
    echo "\nSession Path: $sessionPath\n";
    
    if (!is_dir($sessionPath)) {
        echo "Creating sessions directory...\n";
        mkdir($sessionPath, 0755, true);
    }
    
    if (is_writable($sessionPath)) {
        echo "Sessions directory is writable ✓\n";
    } else {
        echo "Sessions directory is NOT writable ✗\n";
        echo "Run: chmod 755 $sessionPath\n";
    }
}

// Step 5: Check CSRF configuration
echo "\n4. CHECKING CSRF CONFIGURATION\n";
echo "===============================\n";

$csrfExceptions = [
    'api/*',
    'web-api/ai/*',
    'web-api/user/profile-image',
    'webhooks/*',
];

echo "CSRF Exceptions configured:\n";
foreach ($csrfExceptions as $exception) {
    echo "- $exception\n";
}

// Step 6: Final recommendations
echo "\n5. FINAL RECOMMENDATIONS\n";
echo "========================\n";

echo "✓ Session domain set to: .medroid.ai (allows subdomains)\n";
echo "✓ Session same-site set to: lax (recommended for most cases)\n";
echo "✓ App URL updated to: https://app.medroid.ai\n";
echo "✓ All caches cleared and optimized\n\n";

echo "NEXT STEPS:\n";
echo "1. Restart your web server (nginx/apache)\n";
echo "2. Restart PHP-FPM if using it\n";
echo "3. Test the application\n";
echo "4. If still having issues, check browser developer tools for cookie errors\n\n";

echo "TROUBLESHOOTING:\n";
echo "- Clear browser cookies for medroid.ai domain\n";
echo "- Check if HTTPS is properly configured\n";
echo "- Verify SSL certificate is valid\n";
echo "- Check server logs for additional errors\n\n";

echo "=== SCRIPT COMPLETED ===\n";
