<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - {{ $order->order_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #10B981;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .order-summary {
            background-color: #F0FDF4;
            border: 1px solid #BBF7D0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .order-items {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .item {
            border-bottom: 1px solid #e5e5e5;
            padding: 10px 0;
        }
        .item:last-child {
            border-bottom: none;
        }
        .button {
            display: inline-block;
            background-color: #10B981;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e5e5;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ Order Confirmed!</h1>
        <p>Thank you for your purchase</p>
    </div>

    <div class="content">
        <p>Hi {{ $recipient->name }},</p>

        <p>Thank you for your order! We've received your order and are preparing it for shipment.</p>

        <div class="order-summary">
            <h3>📋 Order Details</h3>
            <p><strong>Order Number:</strong> {{ $order->order_number }}</p>
            <p><strong>Order Date:</strong> {{ $order->created_at->format('F j, Y') }}</p>
            <p><strong>Payment Status:</strong> {{ ucfirst($order->payment_status) }}</p>
            <p><strong>Order Status:</strong> {{ ucfirst($order->status) }}</p>
        </div>

        <div class="order-items">
            <h3>🛍️ Items Ordered</h3>
            @foreach($order->items as $item)
                <div class="item">
                    <strong>{{ $item->product_name }}</strong><br>
                    <span style="color: #666;">SKU: {{ $item->product_sku }}</span><br>
                    <span style="color: #666;">Quantity: {{ $item->quantity }} × {{ $item->formatted_unit_price }}</span>
                    <div style="text-align: right; font-weight: bold;">{{ $item->formatted_total_price }}</div>
                </div>
            @endforeach
            
            <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #e5e5e5;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span>Subtotal:</span>
                    <span>{{ $order->formatted_subtotal }}</span>
                </div>
                @if($order->tax_amount > 0)
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>Tax:</span>
                        <span>{{ $order->formatted_tax }}</span>
                    </div>
                @endif
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span>Shipping:</span>
                    <span style="color: #10B981; font-weight: bold;">Free</span>
                </div>
                @if($order->discount_amount > 0)
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px; color: #10B981;">
                        <span>Discount:</span>
                        <span>-{{ $order->formatted_discount }}</span>
                    </div>
                @endif
                <div style="display: flex; justify-content: space-between; font-size: 18px; font-weight: bold; border-top: 1px solid #e5e5e5; padding-top: 10px;">
                    <span>Total:</span>
                    <span>{{ $order->formatted_total }}</span>
                </div>
            </div>
        </div>

        @if($order->shipping_address)
            <div style="background-color: white; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3>🏠 Shipping Address</h3>
                <p>
                    {{ $order->shipping_address['name'] ?? ($order->shipping_address['first_name'] . ' ' . $order->shipping_address['last_name']) }}<br>
                    {{ $order->shipping_address['address_line_1'] }}<br>
                    @if(!empty($order->shipping_address['address_line_2']))
                        {{ $order->shipping_address['address_line_2'] }}<br>
                    @endif
                    {{ $order->shipping_address['city'] }}, {{ $order->shipping_address['state'] }} {{ $order->shipping_address['postal_code'] }}<br>
                    {{ $order->shipping_address['country'] }}
                </p>
            </div>
        @endif

        <div style="text-align: center;">
            <a href="{{ $orderUrl }}" class="button">View Order Details</a>
        </div>

        <p><strong>What's Next?</strong></p>
        <ul>
            <li>We'll process your order within 1-2 business days</li>
            <li>You'll receive a shipping confirmation email with tracking information</li>
            <li>You can track your order status anytime using the link above</li>
        </ul>

        <p>If you have any questions about your order, please don't hesitate to contact our customer support team.</p>

        <p>Thank you for choosing Medroid!</p>

        <p>Best regards,<br>
        The Medroid Team</p>
    </div>

    <div class="footer">
        <p>This email was sent regarding your order #{{ $order->order_number }}</p>
        <p>© {{ date('Y') }} Medroid. All rights reserved.</p>
    </div>
</body>
</html>
