import{_ as v}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{d as l,e as a,f as g,u as m,m as h,g as y,i as t,t as o,F as d,p,P as _,A as c}from"./vendor-BhKTHoN5.js";import{_ as w}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const A={class:"p-6"},k={class:"dashboard-container"},P={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},D={class:"bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500"},j={class:"flex justify-between"},$={class:"text-2xl font-bold text-blue-600"},S={class:"bg-green-50 rounded-lg p-4 border-l-4 border-green-500"},C={class:"flex justify-between"},E={class:"text-2xl font-bold text-green-600"},N={class:"bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500"},U={class:"flex justify-between"},M={class:"text-2xl font-bold text-purple-600"},T={class:"bg-yellow-50 rounded-lg p-4 border-l-4 border-yellow-500"},B={class:"flex justify-between"},L={class:"text-2xl font-bold text-yellow-600"},R={class:"mb-6"},V={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},q={class:"flex items-center mb-3"},F={class:"font-semibold text-gray-900"},O={class:"text-sm text-gray-600"},z={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},I={class:"bg-white rounded-lg shadow p-6"},Q={key:0,class:"text-center py-8"},G={key:1,class:"space-y-3"},H={class:"flex-1"},J={class:"font-medium text-gray-900"},K={class:"text-sm text-gray-600"},W={class:"bg-white rounded-lg shadow p-6"},X={key:0,class:"text-center py-8"},Y={key:1,class:"space-y-3"},Z={class:"flex-1"},tt={class:"font-medium text-gray-900"},st={class:"text-sm text-gray-600"},et={__name:"ProviderDashboard",props:{provider:{type:Object,required:!0},stats:{type:Object,default:()=>({totalPatients:0,upcomingAppointments:0,monthlyEarnings:0,completionRate:0})},todayAppointments:{type:Array,default:()=>[]},upcomingAppointments:{type:Array,default:()=>[]}},setup(i){const b=[{title:"Provider Dashboard",href:"/provider/dashboard"}],u=n=>new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),x=n=>({scheduled:"bg-blue-100 text-blue-800",confirmed:"bg-green-100 text-green-800",in_progress:"bg-yellow-100 text-yellow-800",completed:"bg-gray-100 text-gray-800",cancelled:"bg-red-100 text-red-800"})[n]||"bg-gray-100 text-gray-800",f=[{title:"Manage Availability",href:"/provider/availability",icon:"calendar-alt",description:"Set your working hours and time slots",color:"blue"},{title:"View Appointments",href:"/provider/appointments",icon:"calendar-check",description:"See your upcoming appointments",color:"green"},{title:"Patient List",href:"/provider/patients",icon:"users",description:"Manage your patient records",color:"purple"},{title:"Earnings",href:"/provider/earnings",icon:"dollar-sign",description:"Track your earnings and payments",color:"yellow"}];return(n,s)=>(a(),l(d,null,[g(m(h),{title:"Provider Dashboard"}),g(v,{breadcrumbs:b},{default:y(()=>[t("div",A,[t("div",k,[s[17]||(s[17]=t("div",{class:"dashboard-header mb-6"},[t("div",{class:"header-content"},[t("h1",{class:"text-3xl font-bold text-gray-900 mb-2"},"Provider Dashboard"),t("p",{class:"text-gray-600"},"Manage your patients, availability, and appointments")])],-1)),t("div",P,[t("div",D,[t("div",j,[t("div",null,[s[0]||(s[0]=t("p",{class:"text-sm text-gray-500"},"Total Patients",-1)),t("p",$,o(i.stats.totalPatients||0),1),s[1]||(s[1]=t("p",{class:"text-xs text-gray-400"},"Active patients",-1))]),s[2]||(s[2]=t("div",{class:"text-blue-500"},[t("i",{class:"fas fa-users text-2xl"})],-1))])]),t("div",S,[t("div",C,[t("div",null,[s[3]||(s[3]=t("p",{class:"text-sm text-gray-500"},"Upcoming Appointments",-1)),t("p",E,o(i.stats.upcomingAppointments||0),1),s[4]||(s[4]=t("p",{class:"text-xs text-gray-400"},"Next 7 days",-1))]),s[5]||(s[5]=t("div",{class:"text-green-500"},[t("i",{class:"fas fa-calendar-check text-2xl"})],-1))])]),t("div",N,[t("div",U,[t("div",null,[s[6]||(s[6]=t("p",{class:"text-sm text-gray-500"},"Completion Rate",-1)),t("p",M,o(i.stats.completionRate||0)+"%",1),s[7]||(s[7]=t("p",{class:"text-xs text-gray-400"},"This month",-1))]),s[8]||(s[8]=t("div",{class:"text-purple-500"},[t("i",{class:"fas fa-chart-line text-2xl"})],-1))])]),t("div",T,[t("div",B,[t("div",null,[s[9]||(s[9]=t("p",{class:"text-sm text-gray-500"},"Monthly Earnings",-1)),t("p",L,"$"+o(i.stats.monthlyEarnings||0),1),s[10]||(s[10]=t("p",{class:"text-xs text-gray-400"},"Current month",-1))]),s[11]||(s[11]=t("div",{class:"text-yellow-500"},[t("i",{class:"fas fa-dollar-sign text-2xl"})],-1))])])]),t("div",R,[s[12]||(s[12]=t("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Quick Actions",-1)),t("div",V,[(a(),l(d,null,p(f,e=>g(m(_),{key:e.title,href:e.href,class:"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow duration-200 border border-gray-200"},{default:y(()=>[t("div",q,[t("div",{class:c(`p-2 rounded-lg mr-3 bg-${e.color}-100`)},[t("i",{class:c(`fas fa-${e.icon} text-${e.color}-600`)},null,2)],2),t("h4",F,o(e.title),1)]),t("p",O,o(e.description),1)]),_:2},1032,["href"])),64))])]),t("div",z,[t("div",I,[s[14]||(s[14]=t("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Today's Appointments",-1)),i.todayAppointments.length===0?(a(),l("div",Q,s[13]||(s[13]=[t("i",{class:"fas fa-calendar-times text-4xl text-gray-300 mb-4"},null,-1),t("p",{class:"text-gray-500"},"No appointments scheduled for today",-1)]))):(a(),l("div",G,[(a(!0),l(d,null,p(i.todayAppointments,e=>{var r;return a(),l("div",{key:e.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",H,[t("p",J,o(((r=e.patient)==null?void 0:r.name)||"Unknown Patient"),1),t("p",K,o(u(e.scheduled_at)),1)]),t("span",{class:c(`px-2 py-1 rounded-full text-xs font-medium ${x(e.status)}`)},o(e.status),3)])}),128))]))]),t("div",W,[s[16]||(s[16]=t("h3",{class:"text-lg font-semibold text-gray-700 mb-4"},"Upcoming Appointments",-1)),i.upcomingAppointments.length===0?(a(),l("div",X,s[15]||(s[15]=[t("i",{class:"fas fa-calendar-plus text-4xl text-gray-300 mb-4"},null,-1),t("p",{class:"text-gray-500"},"No upcoming appointments",-1)]))):(a(),l("div",Y,[(a(!0),l(d,null,p(i.upcomingAppointments,e=>{var r;return a(),l("div",{key:e.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",Z,[t("p",tt,o(((r=e.patient)==null?void 0:r.name)||"Unknown Patient"),1),t("p",st,o(u(e.scheduled_at)),1)]),t("span",{class:c(`px-2 py-1 rounded-full text-xs font-medium ${x(e.status)}`)},o(e.status),3)])}),128))]))])])])])]),_:1})],64))}},nt=w(et,[["__scopeId","data-v-7e3a6229"]]);export{nt as default};
