# Medroid Flutter App - Production Deployment Guide

## Overview
This guide covers the production deployment of the Medroid Flutter mobile application with comprehensive e-commerce functionality, video consultations, and provider marketplace features.

## Prerequisites

### Development Environment
- Flutter SDK 3.16.0 or higher
- Dart SDK 3.2.0 or higher
- Android Studio / Xcode for mobile development
- Firebase CLI for push notifications

### Backend Requirements
- Laravel backend with API endpoints
- Stripe payment gateway setup
- Agora.io account for video consultations
- Firebase project for push notifications

## Environment Configuration

### 1. Environment Files
The app supports multiple environments:

#### Development (`.env.development`)
```env
API_BASE_URL=http://medroid-full.test/api/
API_STORAGE_URL=http://medroid-full.test/

# Agora Configuration
AGORA_APP_ID=your_development_agora_app_id
AGORA_APP_CERTIFICATE=your_development_agora_app_certificate
# Leave empty for local development to use built-in token generator
AGORA_TOKEN_SERVICE_URL=

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_key
STRIPE_WEBHOOK_SECRET=http://localhost:8000
STRIPE_WEBHOOK_VERIFY=false

DEBUG_MODE=true
```

#### Staging (`.env`)
```env
API_BASE_URL=https://staging-backend.medroid.ai/api/
API_STORAGE_URL=https://staging-backend.medroid.ai/

# Agora Configuration
AGORA_APP_ID=your_staging_agora_app_id
AGORA_APP_CERTIFICATE=your_staging_agora_app_certificate
AGORA_TOKEN_SERVICE_URL=https://staging-backend.medroid.ai/api/agora/token

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_key
STRIPE_WEBHOOK_SECRET=https://staging-backend.medroid.ai/webhooks/stripe
STRIPE_WEBHOOK_VERIFY=true

DEBUG_MODE=false
```

#### Production (`.env.production`)
```env
API_BASE_URL=https://api.medroid.ai/api/
API_STORAGE_URL=https://api.medroid.ai/

# Agora Configuration
AGORA_APP_ID=your_production_agora_app_id
AGORA_APP_CERTIFICATE=your_production_agora_app_certificate
AGORA_TOKEN_SERVICE_URL=https://api.medroid.ai/api/agora/token

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_live_key
STRIPE_WEBHOOK_SECRET=https://api.medroid.ai/webhooks/stripe
STRIPE_WEBHOOK_VERIFY=true

DEBUG_MODE=false
```

### 2. Configuration Validation
The app includes built-in configuration validation to ensure all required keys are properly set:

```dart
// Check Agora configuration
bool isValid = AgoraTokenService.isAgoraConfigValid();
Map<String, dynamic> config = AgoraTokenService.getAgoraConfig();

// Check Stripe configuration  
String publishableKey = PaymentService().getStripePublishableKey();
bool webhookVerify = PaymentService().isWebhookVerificationEnabled();
```

### 3. Token Service Integration
The app supports both local token generation and dedicated token service:

- **Development**: Uses local token generation (no `AGORA_TOKEN_SERVICE_URL`)
- **Production**: Uses dedicated token service for enhanced security
- **Fallback**: If token service fails, falls back to main API token generation

### 4. Firebase Configuration
1. Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
2. Place in respective platform directories
3. Configure Firebase Cloud Messaging for push notifications

## Core Features

### 1. Shop & E-commerce System
- **Patient Features**:
  - Browse products and services from healthcare providers
  - Shopping cart with persistent storage
  - Secure checkout with Stripe integration
  - Order history and tracking
  - Digital product downloads

- **Provider Features**:
  - Product and service management
  - Inventory tracking
  - Order management
  - Revenue analytics
  - Digital content delivery

### 2. Video Consultations (Agora.io)
- HD video/audio calling
- Screen sharing capabilities
- Recording functionality
- Multi-platform support (iOS, Android, Web)
- Bandwidth optimization

### 3. Healthcare Provider Network
- Provider profiles and specializations
- Appointment booking system
- Service catalog management
- Patient reviews and ratings

### 4. Payment Processing
- Stripe integration for secure payments
- Support for credit/debit cards
- Subscription management
- Refund processing
- Multi-currency support

## API Integration

### Base URLs
- Development: `http://medroid-full.test/api/`
- Staging: `https://staging-backend.medroid.ai/api/`
- Production: `https://api.medroid.ai/api/`

### Key Endpoints

#### Authentication
```
POST /auth/login
POST /auth/register
POST /auth/logout
GET /auth/user
```

#### Shop & E-commerce
```
GET /shop/products
GET /shop/categories
POST /shop/cart/add
GET /shop/cart
POST /shop/checkout
GET /shop/orders
```

#### Provider Management
```
GET /provider/products
POST /provider/products
PUT /provider/products/{id}
DELETE /provider/products/{id}
```

#### Video Consultations
```
POST /video/session/start
GET /video/session/{id}/token
POST /video/session/{id}/end
```

## Build and Deployment

### 1. Android Build
```bash
# Development
flutter build apk --flavor development -t lib/main.dart

# Production
flutter build appbundle --release --flavor production
```

### 2. iOS Build
```bash
# Development
flutter build ios --flavor development

# Production
flutter build ipa --release --flavor production
```

### 3. Code Signing
- Android: Configure signing keys in `android/app/build.gradle`
- iOS: Set up provisioning profiles and certificates in Xcode

## Security Considerations

### 1. API Security
- All API calls use HTTPS in production
- Bearer token authentication
- Request/response encryption
- Rate limiting implementation

### 2. Payment Security
- PCI DSS compliant Stripe integration
- No sensitive payment data stored locally
- 3D Secure authentication support
- Tokenized payment methods

### 3. Data Protection
- Local data encryption using secure storage
- GDPR compliance for EU users
- HIPAA compliance for healthcare data
- Secure video communication

## Performance Optimization

### 1. Image Loading
- Cached network images with lazy loading
- Image compression and optimization
- Progressive image loading

### 2. State Management
- Efficient BLoC pattern implementation
- Minimal rebuilds and optimized widgets
- Memory leak prevention

### 3. Network Optimization
- HTTP/2 support
- Request caching
- Offline capabilities
- Background sync

## Testing Strategy

### 1. Unit Tests
```bash
flutter test
```

### 2. Integration Tests
```bash
flutter test integration_test/
```

### 3. Platform-Specific Testing
- Android: Test on various screen sizes and API levels
- iOS: Test on different device models and iOS versions

## Monitoring and Analytics

### 1. Crash Reporting
- Firebase Crashlytics integration
- Automatic crash reporting
- Performance monitoring

### 2. User Analytics
- Firebase Analytics
- Custom event tracking
- User journey analysis

### 3. Error Logging
- Comprehensive error logging
- Remote logging capabilities
- Real-time error monitoring

## Deployment Checklist

### Pre-Deployment
- [ ] Update version numbers in `pubspec.yaml`
- [ ] Verify all environment variables are set
- [ ] Run full test suite
- [ ] Perform security audit
- [ ] Test payment flows in staging
- [ ] Verify video consultation functionality

### App Store Deployment
- [ ] Prepare app store listings
- [ ] Create promotional materials
- [ ] Set up app store optimization (ASO)
- [ ] Configure in-app purchase products (if applicable)

### Post-Deployment
- [ ] Monitor crash reports
- [ ] Check payment processing
- [ ] Verify video consultation performance
- [ ] Monitor user feedback
- [ ] Track key performance metrics

## Troubleshooting

### Common Issues

#### Build Failures
1. Clean build files: `flutter clean && flutter pub get`
2. Check Flutter and Dart SDK versions
3. Verify platform-specific configurations

#### API Connection Issues
1. Verify base URL configuration
2. Check authentication tokens
3. Test network connectivity

#### Payment Issues
1. Verify Stripe keys are correct
2. Check webhook configurations
3. Test in Stripe dashboard

#### Video Call Problems
1. Verify Agora App ID
2. Check device permissions
3. Test network quality

## Support and Documentation

### Technical Support
- Email: <EMAIL>
- Documentation: [Internal Docs](https://docs.medroid.ai)
- Issue Tracker: GitHub Issues

### API Documentation
- Staging: https://staging-backend.medroid.ai/docs
- Production: https://api.medroid.ai/docs

## License
This project is proprietary and confidential. All rights reserved.