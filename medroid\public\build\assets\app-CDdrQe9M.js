const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Create-CbyuFS2W.js","assets/vendor-BhKTHoN5.js","assets/AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/Primitive-DSQomZit.js","assets/createLucideIcon-YxmScYOV.js","assets/AppLayout-B4Zax8Ug.css","assets/Edit-RroQ6TC5.js","assets/Index-CiYym50_.js","assets/Show-DzY_w5Lq.js","assets/Index-NpjahwoV.js","assets/Show-BWE_OqIT.js","assets/Create-BbdbyqR2.js","assets/FilePickerButton-BUMxh5r7.js","assets/Edit-jrScC92y.js","assets/Index-LnjU7Y15.js","assets/BulkImportModal-DkR4MIpW.js","assets/SocialMediaManager-DRXUOYJo.js","assets/SocialMediaManager-tn0RQdqM.css","assets/AppointmentDetail-DsmQdHw4.js","assets/AppointmentEdit-BopAGnAJ.js","assets/AppointmentPayment-HN7F9STy.js","assets/Appointments-CsNAWFgz.js","assets/Appointments-Dc5S3XBs.css","assets/Chat-DUGiwotC.js","assets/ChatInput-DdW2-31K.js","assets/ChatInput-BmGnubPc.css","assets/useForwardPropsEmits-DFe9BlYF.js","assets/index-CGRqDMLC.js","assets/useForwardExpose-DjhPD9_V.js","assets/Chat-vt7b9SCF.css","assets/ChatHistory-D_zhIIec.js","assets/Chats-Bap8KiS1.js","assets/Clinics-DZG-2Oly.js","assets/Clubs-l4pgWUoc.js","assets/CreditHistory-BvmO2d9p.js","assets/Credits-BeNtNeLm.js","assets/Dashboard-Dk1bJR-u.js","assets/Dashboard_backup-Dch5Xkz1.js","assets/Dashboard_backup-k8swJl3t.css","assets/Discover-BVMJm3S6.js","assets/ComingSoon-Bg3W8jN1.js","assets/Discover-5tsUXK41.css","assets/EmailTemplates-B7s2sQXw.js","assets/FileManager-BKKpNFRf.js","assets/FilePickerTest-gCPc_pIl.js","assets/Notifications-BTiZfsI0.js","assets/Patients-DrHjleXc.js","assets/Payments-Bhre_-U2.js","assets/PendingApprovals-_XDWeN7d.js","assets/Permissions-CPoCtipN.js","assets/Availability-DizBvLIl.js","assets/Earnings-DDohDz_Y.js","assets/Patients-99boGfLT.js","assets/Products-CVu8tHRB.js","assets/Schedule-D2-6c27A.js","assets/Services-B8y842oR.js","assets/ProviderDashboard-FmBcY6ji.js","assets/ProviderDashboard-BsHFbCOR.css","assets/ProviderRegister-dXX45nKS.js","assets/InputError.vue_vue_type_script_setup_true_lang-B3hvUGHW.js","assets/Providers-C8Lt1aQd.js","assets/Referrals-Chthckoq.js","assets/Services-63D1pZiq.js","assets/Shop-Dxp3S9xg.js","assets/Cart-B71ie0FG.js","assets/Checkout-DxfQTTXS.js","assets/OrderDetail-DKiKaMhk.js","assets/Orders-B82r2l0s.js","assets/ProductDetail-DdWia-CK.js","assets/SystemVerification-B59eao1b.js","assets/Users-DDBm2xSM.js","assets/Waitlist-DZKehbJW.js","assets/Welcome-hm7bqEyf.js","assets/Welcome-DrPWS9zi.css","assets/ConfirmPassword-DKo_Ebxu.js","assets/index-CFmBC9d8.js","assets/Label.vue_vue_type_script_setup_true_lang-dYaAjAby.js","assets/AuthLayout.vue_vue_type_script_setup_true_lang-BEoj-CWY.js","assets/ForgotPassword-DIxJmtBS.js","assets/TextLink.vue_vue_type_script_setup_true_lang-B2BUb5pV.js","assets/FounderSignup-Bm-Je2wP.js","assets/Register-BVpoNxlu.js","assets/Register-B2WbpIMy.css","assets/ResetPassword-DtF-gtz6.js","assets/VerifyEmail-BbFI8OfC.js","assets/Appearance-wQY5Uk9l.js","assets/HeadingSmall.vue_vue_type_script_setup_true_lang-DVe5uQrS.js","assets/Layout.vue_vue_type_script_setup_true_lang-CjtMa_dt.js","assets/Appearance-CB0SEYXv.css","assets/AppointmentPreferences-DN8c2tWu.js","assets/useBodyScrollLock-CcBLBbmp.js","assets/Password-DoXZAk16.js","assets/Profile-BMFV_Le4.js"])))=>i.map(i=>d[i]);
import{r as h,o as I,c as E,w as D,a as v,L as S,W as y,b as V,k as w,h as k}from"./vendor-BhKTHoN5.js";const C="modulepreload",x=function(e){return"/build/"+e},R={},t=function(o,r,n){let d=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),_=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));d=Promise.allSettled(r.map(s=>{if(s=x(s),s in R)return;R[s]=!0;const p=s.endsWith(".css"),i=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${i}`))return;const u=document.createElement("link");if(u.rel=p?"stylesheet":C,p||(u.as="script"),u.crossOrigin="",u.href=s,_&&u.setAttribute("nonce",_),document.head.appendChild(u),p)return new Promise((f,O)=>{u.addEventListener("load",f),u.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${s}`)))})}))}function c(a){const _=new Event("vite:preloadError",{cancelable:!0});if(_.payload=a,window.dispatchEvent(_),!_.defaultPrevented)throw a}return d.then(a=>{for(const _ of a||[])_.status==="rejected"&&c(_.reason);return o().catch(c)})};async function F(e,o){for(const r of Array.isArray(e)?e:[e]){const n=o[r];if(!(typeof n>"u"))return typeof n=="function"?n():n}throw new Error(`Page not found: ${e}`)}function P(e){if(!(typeof window>"u"))if(e==="system"){const r=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";document.documentElement.classList.toggle("dark",r==="dark")}else document.documentElement.classList.toggle("dark",e==="dark")}const b=(e,o,r=365)=>{if(typeof document>"u")return;const n=r*24*60*60;document.cookie=`${e}=${o};path=/;max-age=${n};SameSite=Lax`},z=()=>typeof window>"u"?null:window.matchMedia("(prefers-color-scheme: dark)"),L=()=>typeof window>"u"?null:localStorage.getItem("appearance"),$=()=>{const e=L();P(e||"system")};function j(){var o;if(typeof window>"u")return;const e=L();P(e||"system"),(o=z())==null||o.addEventListener("change",$)}function H(){const e=h("system");I(()=>{const r=localStorage.getItem("appearance");r&&(e.value=r)});function o(r){e.value=r,localStorage.setItem("appearance",r),b("appearance",r),P(r)}return{appearance:e,updateAppearance:o}}const m={small:{name:"Small",scale:.875,description:"Smaller text for better screen space"},normal:{name:"Normal",scale:1,description:"Default text size"},large:{name:"Large",scale:1.125,description:"Larger text for better readability"},xlarge:{name:"Extra Large",scale:1.25,description:"Extra large text for accessibility"}},l=h("normal"),N=()=>{const e=localStorage.getItem("medroid-font-size");e&&m[e]&&(l.value=e)},q=e=>{localStorage.setItem("medroid-font-size",e)};N();function W(){const e=E(()=>{var i;return((i=m[l.value])==null?void 0:i.scale)||1}),o=E(()=>{var i;return((i=m[l.value])==null?void 0:i.name)||"Normal"}),r=E(()=>{var i;return((i=m[l.value])==null?void 0:i.description)||""}),n=E(()=>Object.entries(m).map(([i,u])=>({key:i,...u}))),d=E(()=>({"--font-scale":e.value,"--text-xs":`${.75*e.value}rem`,"--text-sm":`${.875*e.value}rem`,"--text-base":`${1*e.value}rem`,"--text-lg":`${1.125*e.value}rem`,"--text-xl":`${1.25*e.value}rem`,"--text-2xl":`${1.5*e.value}rem`,"--text-3xl":`${1.875*e.value}rem`,"--text-4xl":`${2.25*e.value}rem`,"--text-5xl":`${3*e.value}rem`,"--text-6xl":`${3.75*e.value}rem`})),c=i=>{m[i]&&(l.value=i,q(i),p())},a=()=>{const i=Object.keys(m),u=i.indexOf(l.value);u<i.length-1&&c(i[u+1])},_=()=>{const i=Object.keys(m),u=i.indexOf(l.value);u>0&&c(i[u-1])},s=()=>{c("normal")},p=()=>{const i=document.documentElement;Object.entries(d.value).forEach(([u,f])=>{i.style.setProperty(u,f)}),document.body.className=document.body.className.replace(/font-size-\w+/g,""),document.body.classList.add(`font-size-${l.value}`)};return D(l,()=>{p()},{immediate:!0}),{currentFontSize:l,fontSizeScale:e,fontSizeName:o,fontSizeDescription:r,availableFontSizes:n,fontSizeStyles:d,setFontSize:c,increaseFontSize:a,decreaseFontSize:_,resetFontSize:s,applyFontSizeToDocument:p,FONT_SIZES:m}}const M="Medroid";v.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";v.defaults.withCredentials=!0;const g=()=>{const e=document.head.querySelector('meta[name="csrf-token"]');return e?e.content:null},A=async()=>{try{if((await fetch("/sanctum/csrf-cookie",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json"}})).ok){await new Promise(n=>setTimeout(n,100));let r=g();if(r)return r}const o=await fetch("/csrf-token",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json"}});if(o.ok){const r=await o.json();if(r.csrf_token){const n=document.head.querySelector('meta[name="csrf-token"]');return n&&(n.content=r.csrf_token),r.csrf_token}}}catch(e){console.error("Failed to refresh CSRF token:",e)}return null},T=g();T&&(v.defaults.headers.common["X-CSRF-TOKEN"]=T);const X=async()=>{let e=g();return e||(console.log("Initializing CSRF token..."),e=await A(),e?console.log("CSRF token initialized successfully"):console.warn("Failed to initialize CSRF token")),e};v.interceptors.request.use(async e=>{let o=g();return o||(console.warn("No CSRF token found, attempting to refresh..."),o=await A()),o?e.headers["X-CSRF-TOKEN"]=o:console.error("Unable to obtain CSRF token"),e.headers.Accept=e.headers.Accept||"application/json",!e.headers["Content-Type"]&&!(e.data instanceof FormData)&&(e.headers["Content-Type"]="application/json"),e},e=>Promise.reject(e));v.interceptors.response.use(e=>e,async e=>{var r,n,d,c,a,_;const o=e.config;if(((r=e.response)==null?void 0:r.status)===419&&!o._retry){console.warn("CSRF token mismatch detected, attempting to refresh and retry..."),o._retry=!0;try{const s=await A();if(s)return o.headers["X-CSRF-TOKEN"]=s,v.defaults.headers.common["X-CSRF-TOKEN"]=s,await new Promise(p=>setTimeout(p,50)),v(o);console.error("Failed to refresh CSRF token, reloading page..."),setTimeout(()=>{window.confirm("Session expired. Reload page to continue?")&&window.location.reload()},1e3)}catch(s){console.error("Error during token refresh:",s)}}if(((n=e.response)==null?void 0:n.status)===500&&!o._serverRetry&&!((d=o.url)!=null&&d.includes("/logout"))){console.warn("Server error detected, attempting retry..."),o._serverRetry=!0,await new Promise(s=>setTimeout(s,1e3));try{return v(o)}catch(s){console.error("Server error retry failed:",s)}}return(c=o.url)!=null&&c.includes("/logout")?(console.log("Logout request - not intercepting"),Promise.reject(e)):(((a=e.response)==null?void 0:a.status)===401&&((_=o.url)!=null&&_.includes("/logout")||(console.warn("Authentication failed, redirecting to login..."),window.location.href="/login")),Promise.reject(e))});window.axios=v;S({title:e=>`${e} - ${M}`,resolve:e=>F(`./pages/${e}.vue`,Object.assign({"./pages/Admin/Categories/Create.vue":()=>t(()=>import("./Create-CbyuFS2W.js"),__vite__mapDeps([0,1,2,3,4,5,6])),"./pages/Admin/Categories/Edit.vue":()=>t(()=>import("./Edit-RroQ6TC5.js"),__vite__mapDeps([7,1,2,3,4,5,6])),"./pages/Admin/Categories/Index.vue":()=>t(()=>import("./Index-CiYym50_.js"),__vite__mapDeps([8,1,2,3,4,5,6])),"./pages/Admin/Categories/Show.vue":()=>t(()=>import("./Show-DzY_w5Lq.js"),__vite__mapDeps([9,2,1,3,4,5,6])),"./pages/Admin/Orders/Index.vue":()=>t(()=>import("./Index-NpjahwoV.js"),__vite__mapDeps([10,1,2,3,4,5,6])),"./pages/Admin/Orders/Show.vue":()=>t(()=>import("./Show-BWE_OqIT.js"),__vite__mapDeps([11,2,1,3,4,5,6])),"./pages/Admin/Products/Create.vue":()=>t(()=>import("./Create-BbdbyqR2.js"),__vite__mapDeps([12,1,2,3,4,5,6,13])),"./pages/Admin/Products/Edit.vue":()=>t(()=>import("./Edit-jrScC92y.js"),__vite__mapDeps([14,1,2,3,4,5,6])),"./pages/Admin/Products/Index.vue":()=>t(()=>import("./Index-LnjU7Y15.js"),__vite__mapDeps([15,1,2,3,4,5,6,16])),"./pages/Admin/SocialMediaManager.vue":()=>t(()=>import("./SocialMediaManager-DRXUOYJo.js"),__vite__mapDeps([17,1,2,3,4,5,6,18])),"./pages/AppointmentDetail.vue":()=>t(()=>import("./AppointmentDetail-DsmQdHw4.js"),__vite__mapDeps([19,2,1,3,4,5,6])),"./pages/AppointmentEdit.vue":()=>t(()=>import("./AppointmentEdit-BopAGnAJ.js"),__vite__mapDeps([20,1,2,3,4,5,6])),"./pages/AppointmentPayment.vue":()=>t(()=>import("./AppointmentPayment-HN7F9STy.js"),__vite__mapDeps([21,1,2,3,4,5,6])),"./pages/Appointments.vue":()=>t(()=>import("./Appointments-CsNAWFgz.js"),__vite__mapDeps([22,2,1,3,4,5,6,23])),"./pages/Chat.vue":()=>t(()=>import("./Chat-DUGiwotC.js"),__vite__mapDeps([24,1,2,3,4,5,6,25,26,27,28,29,30])),"./pages/ChatHistory.vue":()=>t(()=>import("./ChatHistory-D_zhIIec.js"),__vite__mapDeps([31,2,1,3,4,5,6])),"./pages/Chats.vue":()=>t(()=>import("./Chats-Bap8KiS1.js"),__vite__mapDeps([32,1,2,3,4,5,6])),"./pages/Clinics.vue":()=>t(()=>import("./Clinics-DZG-2Oly.js"),__vite__mapDeps([33,1,2,3,4,5,6])),"./pages/Clubs.vue":()=>t(()=>import("./Clubs-l4pgWUoc.js"),__vite__mapDeps([34,1,2,3,4,5,6])),"./pages/CreditHistory.vue":()=>t(()=>import("./CreditHistory-BvmO2d9p.js"),__vite__mapDeps([35,1,2,3,4,5,6])),"./pages/Credits.vue":()=>t(()=>import("./Credits-BeNtNeLm.js"),__vite__mapDeps([36,1,2,3,4,5,6])),"./pages/Dashboard.vue":()=>t(()=>import("./Dashboard-Dk1bJR-u.js"),__vite__mapDeps([37,1,2,3,4,5,6])),"./pages/Dashboard_backup.vue":()=>t(()=>import("./Dashboard_backup-Dch5Xkz1.js"),__vite__mapDeps([38,1,2,3,4,5,6,39])),"./pages/Discover.vue":()=>t(()=>import("./Discover-BVMJm3S6.js"),__vite__mapDeps([40,1,2,3,4,5,6,41,42])),"./pages/EmailTemplates.vue":()=>t(()=>import("./EmailTemplates-B7s2sQXw.js"),__vite__mapDeps([43,1,2,3,4,5,6])),"./pages/FileManager.vue":()=>t(()=>import("./FileManager-BKKpNFRf.js"),__vite__mapDeps([44,1,2,3,4,5,6])),"./pages/FilePickerTest.vue":()=>t(()=>import("./FilePickerTest-gCPc_pIl.js"),__vite__mapDeps([45,2,1,3,4,5,6,13])),"./pages/Notifications.vue":()=>t(()=>import("./Notifications-BTiZfsI0.js"),__vite__mapDeps([46,2,1,3,4,5,6])),"./pages/Patients.vue":()=>t(()=>import("./Patients-DrHjleXc.js"),__vite__mapDeps([47,1,2,3,4,5,6])),"./pages/Payments.vue":()=>t(()=>import("./Payments-Bhre_-U2.js"),__vite__mapDeps([48,1,2,3,4,5,6])),"./pages/PendingApprovals.vue":()=>t(()=>import("./PendingApprovals-_XDWeN7d.js"),__vite__mapDeps([49,1,2,3,4,5,6])),"./pages/Permissions.vue":()=>t(()=>import("./Permissions-CPoCtipN.js"),__vite__mapDeps([50,1,2,3,4,5,6])),"./pages/Provider/Availability.vue":()=>t(()=>import("./Availability-DizBvLIl.js"),__vite__mapDeps([51,1,2,3,4,5,6])),"./pages/Provider/Earnings.vue":()=>t(()=>import("./Earnings-DDohDz_Y.js"),__vite__mapDeps([52,2,1,3,4,5,6])),"./pages/Provider/Patients.vue":()=>t(()=>import("./Patients-99boGfLT.js"),__vite__mapDeps([53,1,2,3,4,5,6])),"./pages/Provider/Products.vue":()=>t(()=>import("./Products-CVu8tHRB.js"),__vite__mapDeps([54,1,2,3,4,5,6,16])),"./pages/Provider/Schedule.vue":()=>t(()=>import("./Schedule-D2-6c27A.js"),__vite__mapDeps([55,1,2,3,4,5,6])),"./pages/Provider/Services.vue":()=>t(()=>import("./Services-B8y842oR.js"),__vite__mapDeps([56,2,1,3,4,5,6])),"./pages/ProviderDashboard.vue":()=>t(()=>import("./ProviderDashboard-FmBcY6ji.js"),__vite__mapDeps([57,2,1,3,4,5,6,58])),"./pages/ProviderRegister.vue":()=>t(()=>import("./ProviderRegister-dXX45nKS.js"),__vite__mapDeps([59,1,60])),"./pages/Providers.vue":()=>t(()=>import("./Providers-C8Lt1aQd.js"),__vite__mapDeps([61,1,2,3,4,5,6])),"./pages/Referrals.vue":()=>t(()=>import("./Referrals-Chthckoq.js"),__vite__mapDeps([62,2,1,3,4,5,6])),"./pages/Services.vue":()=>t(()=>import("./Services-63D1pZiq.js"),__vite__mapDeps([63,1,2,3,4,5,6])),"./pages/Shop.vue":()=>t(()=>import("./Shop-Dxp3S9xg.js"),__vite__mapDeps([64,1,2,3,4,5,6,41])),"./pages/Shop/Cart.vue":()=>t(()=>import("./Cart-B71ie0FG.js"),__vite__mapDeps([65,2,1,3,4,5,6])),"./pages/Shop/Checkout.vue":()=>t(()=>import("./Checkout-DxfQTTXS.js"),__vite__mapDeps([66,1,2,3,4,5,6])),"./pages/Shop/OrderDetail.vue":()=>t(()=>import("./OrderDetail-DKiKaMhk.js"),__vite__mapDeps([67,2,1,3,4,5,6])),"./pages/Shop/Orders.vue":()=>t(()=>import("./Orders-B82r2l0s.js"),__vite__mapDeps([68,1,2,3,4,5,6])),"./pages/Shop/ProductDetail.vue":()=>t(()=>import("./ProductDetail-DdWia-CK.js"),__vite__mapDeps([69,2,1,3,4,5,6])),"./pages/SystemVerification.vue":()=>t(()=>import("./SystemVerification-B59eao1b.js"),__vite__mapDeps([70,1,2,3,4,5,6])),"./pages/Users.vue":()=>t(()=>import("./Users-DDBm2xSM.js"),__vite__mapDeps([71,1,2,3,4,5,6])),"./pages/Waitlist.vue":()=>t(()=>import("./Waitlist-DZKehbJW.js"),__vite__mapDeps([72,1,2,3,4,5,6])),"./pages/Welcome.vue":()=>t(()=>import("./Welcome-hm7bqEyf.js"),__vite__mapDeps([73,1,25,3,26,74])),"./pages/auth/ConfirmPassword.vue":()=>t(()=>import("./ConfirmPassword-DKo_Ebxu.js"),__vite__mapDeps([75,1,60,76,4,77,29,28,78,5])),"./pages/auth/ForgotPassword.vue":()=>t(()=>import("./ForgotPassword-DIxJmtBS.js"),__vite__mapDeps([79,1,60,80,76,4,77,29,28,78,5])),"./pages/auth/FounderSignup.vue":()=>t(()=>import("./FounderSignup-Bm-Je2wP.js"),__vite__mapDeps([81,1,60])),"./pages/auth/Register.vue":()=>t(()=>import("./Register-BVpoNxlu.js"),__vite__mapDeps([82,1,60,3,83])),"./pages/auth/ResetPassword.vue":()=>t(()=>import("./ResetPassword-DtF-gtz6.js"),__vite__mapDeps([84,1,60,76,4,77,29,28,78,5])),"./pages/auth/VerifyEmail.vue":()=>t(()=>import("./VerifyEmail-BbFI8OfC.js"),__vite__mapDeps([85,1,80,76,4,78,5])),"./pages/settings/Appearance.vue":()=>t(()=>import("./Appearance-wQY5Uk9l.js"),__vite__mapDeps([86,1,3,5,87,2,4,6,88,76,28,89])),"./pages/settings/AppointmentPreferences.vue":()=>t(()=>import("./AppointmentPreferences-DN8c2tWu.js"),__vite__mapDeps([90,1,88,76,4,28,77,29,27,91])),"./pages/settings/Password.vue":()=>t(()=>import("./Password-DoXZAk16.js"),__vite__mapDeps([92,1,60,2,3,4,5,6,88,76,28,87,77,29])),"./pages/settings/Profile.vue":()=>t(()=>import("./Profile-BMFV_Le4.js"),__vite__mapDeps([93,1,87,60,76,4,27,28,29,91,5,77,2,3,6,88]))})),setup({el:e,App:o,props:r,plugin:n}){V({render:()=>k(o,r)}).use(n).use(w).mount(e)},progress:{color:"#4B5563"}}).then(()=>{y.on("error",e=>{var o,r,n,d,c,a;if(console.log("Inertia request error:",e),(r=(o=e.response)==null?void 0:o.url)!=null&&r.includes("/logout")||(c=(d=(n=e.response)==null?void 0:n.config)==null?void 0:d.url)!=null&&c.includes("/logout")){console.log("Logout error handled gracefully, redirecting to home"),window.location.href="/";return}if(((a=e.response)==null?void 0:a.status)===419){console.warn("CSRF error on Inertia request, reloading page"),window.location.reload();return}})}).catch(e=>{console.error("Error initializing Inertia app:",e)});j();const{applyFontSizeToDocument:U}=W();U();"serviceWorker"in navigator&&navigator.serviceWorker.getRegistrations().then(function(e){for(const o of e)(o.scope.includes("datadog")||o.scope.includes("sw.js"))&&o.unregister()}).catch(function(e){});X().catch(e=>{console.warn("Failed to initialize CSRF token on startup:",e)});export{H as a,W as u};
