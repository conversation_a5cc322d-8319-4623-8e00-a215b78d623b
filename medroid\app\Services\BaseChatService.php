<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\Patient;
use App\Services\Contracts\ChatServiceInterface;
use App\Services\MedroidModeDetector;
use App\Services\ExamModeHandler;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

abstract class BaseChatService implements ChatServiceInterface
{
    protected $apiKey;
    protected $apiUrl;
    protected $model;
    protected $modeDetector;
    protected $examModeHandler;
    protected $serviceName;

    public function __construct(
        ?MedroidModeDetector $modeDetector = null,
        ?ExamModeHandler $examModeHandler = null
    ) {
        $this->initializeService();
        $this->modeDetector = $modeDetector ?: new MedroidModeDetector();

        // Initialize specialized services with dependency injection
        $this->examModeHandler = $examModeHandler ?: new ExamModeHandler($this);

        // Log if API key is missing
        if (empty($this->apiKey)) {
            Log::warning("{$this->serviceName} API key is missing. Please set the API key in your .env file.");
        }
    }

    /**
     * Initialize service-specific configuration
     * Must be implemented by each service
     */
    abstract protected function initializeService();

    /**
     * Make API request with service-specific implementation
     * Must be implemented by each service
     */
    abstract protected function makeApiRequest($payload, $timeout = 60);

    /**
     * Get service name/identifier
     */
    public function getServiceName()
    {
        return $this->serviceName;
    }

    /**
     * Get service configuration
     */
    public function getConfig()
    {
        return [
            'service_name' => $this->serviceName,
            'api_url' => $this->apiUrl,
            'model' => $this->model,
            'has_api_key' => !empty($this->apiKey),
        ];
    }

    /**
     * Check if the service is available/healthy
     */
    public function isHealthy()
    {
        try {
            if (empty($this->apiKey)) {
                return false;
            }

            // Make a simple test request
            $testPayload = [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'user', 'content' => 'Hello']
                ],
                'max_completion_tokens' => 10,
                'temperature' => 0.1,
            ];

            $response = $this->makeApiRequest($testPayload, 10);
            return $response->successful();
        } catch (\Exception $e) {
            Log::warning("Health check failed for {$this->serviceName}", [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Generate a medical consultation response with mode detection and routing
     */
    public function generateMedicalConsultation(ChatConversation $conversation, $includePatientContext = true, $additionalContext = '', $hasRecentAppointment = false)
    {
        try {
            if (empty($this->apiKey)) {
                Log::error("{$this->serviceName} API key is missing. Cannot generate response.");
                return $this->getErrorResponse();
            }

            // Handle backward compatibility for $additionalContext parameter
            if (is_bool($additionalContext)) {
                $hasRecentAppointment = $additionalContext;
                $additionalContext = '';
            }

            // Get the latest user message for mode detection
            $messages = $this->formatMessages($conversation);
            $latestMessage = collect($conversation->messages)->last();
            $userMessage = $latestMessage['content'] ?? '';

            // Detect the appropriate mode
            $modeResult = $this->modeDetector->detectMode($userMessage, $conversation->messages);
            $mode = $modeResult['mode'];

            Log::info('Medroid mode detected', [
                'conversation_id' => $conversation->id,
                'mode' => $mode,
                'exam_type' => $modeResult['exam_type'] ?? null,
                'service' => $this->serviceName
            ]);

            // Route to appropriate handler based on mode
            switch ($mode) {
                case 'exam_mode':
                    return $this->handleExamMode($userMessage, $modeResult['exam_type'], $conversation);

                case 'consultation':
                default:
                    return $this->handleConsultationMode($conversation, $includePatientContext, $additionalContext, $hasRecentAppointment, $mode);
            }

        } catch (\Exception $e) {
            Log::error("Error in Medroid consultation ({$this->serviceName})", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->getErrorResponse();
        }
    }

    /**
     * Format the conversation messages for the API
     */
    protected function formatMessages(ChatConversation $conversation)
    {
        return collect($conversation->messages)->map(function ($message) {
            return [
                'role' => $message['role'],
                'content' => $message['content'],
            ];
        })->toArray();
    }

    /**
     * Get error response for system failures
     */
    protected function getErrorResponse()
    {
        return [
            'message' => "I apologize, but our AI Doctor service is currently unavailable. Please try again later or contact support.",
            'health_concerns' => [],
            'recommendations' => [],
            'escalate' => false,
            'service_error' => true,
            'service_name' => $this->serviceName,
        ];
    }

    /**
     * Handle exam mode with specialized processing
     */
    protected function handleExamMode($question, $examType, $conversation)
    {
        try {
            // Check if exam mode is enabled
            if (!config('services.medroid.modes.exam_mode', true)) {
                return [
                    'message' => 'Exam mode is currently disabled. Please try again later.',
                    'exam_mode' => false,
                    'health_concerns' => [],
                    'recommendations' => [],
                    'escalate' => false,
                ];
            }

            // Use standard exam mode
            $examResult = $this->examModeHandler->processExamQuestion($question, $examType);

            $response = [
                'message' => $examResult['full_response'],
                'exam_mode' => true,
                'exam_type' => $examType,
                'exam_analysis' => $examResult,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
                'service_name' => $this->serviceName,
            ];

            return $response;

        } catch (\Exception $e) {
            Log::error("Exam mode handling error ({$this->serviceName})", [
                'exam_type' => $examType,
                'error' => $e->getMessage()
            ]);

            return [
                'message' => 'Exam mode temporarily unavailable. Please try again.',
                'exam_mode' => true,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
                'service_name' => $this->serviceName,
            ];
        }
    }

    /**
     * Handle consultation mode
     */
    protected function handleConsultationMode($conversation, $includePatientContext, $additionalContext, $hasRecentAppointment, $mode)
    {
        // Use the existing consultation logic
        $messages = $this->formatMessages($conversation);

        // Get patient context if requested
        $patientContext = '';
        if ($includePatientContext && $conversation->patient_id) {
            $patientContext = $this->getPatientContext($conversation->patient_id);
        }

        // Create mode-specific system prompt
        $systemMessage = [
            'role' => 'system',
            'content' => $this->createMedicalSystemPrompt($patientContext, $additionalContext, $hasRecentAppointment)
        ];

        // Make API request
        $response = $this->makeApiRequestWithRetry([
            'model' => $this->model,
            'messages' => array_merge([$systemMessage], $messages),
            'temperature' => 0.3,
            'max_completion_tokens' => 2000,
            'top_p' => 1,
            'stream' => false,
        ], 3);

        if ($response->successful()) {
            $result = $response->json();
            $content = $result['choices'][0]['message']['content'] ?? '';

            // Process the response
            $structuredResponse = $this->processAiResponse($content, $conversation);
            $structuredResponse['consultation_mode'] = true;
            $structuredResponse['service_name'] = $this->serviceName;

            return $structuredResponse;
        }

        throw new \Exception("Failed to get consultation response from {$this->serviceName}");
    }

    /**
     * Make API request with retry logic for error recovery
     */
    protected function makeApiRequestWithRetry($payload, $maxRetries = 3)
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $maxRetries) {
            try {
                $response = $this->makeApiRequest($payload, 60 + ($attempt * 10)); // Increase timeout with each retry

                if ($response->successful()) {
                    if ($attempt > 0) {
                        Log::info("API request succeeded after retry ({$this->serviceName})", [
                            'attempt' => $attempt + 1,
                            'max_retries' => $maxRetries
                        ]);
                    }
                    return $response;
                }

                // Log non-successful response
                Log::warning("API request failed, will retry ({$this->serviceName})", [
                    'attempt' => $attempt + 1,
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

            } catch (\Exception $e) {
                $lastException = $e;
                Log::warning("API request exception, will retry ({$this->serviceName})", [
                    'attempt' => $attempt + 1,
                    'error' => $e->getMessage()
                ]);
            }

            $attempt++;

            // Wait before retry (exponential backoff)
            if ($attempt < $maxRetries) {
                sleep(pow(2, $attempt)); // 2, 4, 8 seconds
            }
        }

        // All retries failed
        Log::error("All API retry attempts failed ({$this->serviceName})", [
            'max_retries' => $maxRetries,
            'last_exception' => $lastException ? $lastException->getMessage() : 'Unknown error'
        ]);

        throw $lastException ?: new \Exception("API request failed after all retries ({$this->serviceName})");
    }



    /**
     * Get patient context information if available
     */
    protected function getPatientContext($patientId)
    {
        try {
            $patient = Patient::find($patientId);
            if (!$patient) {
                return '';
            }

            $context = "Patient context:\n";

            // Add demographic information (gender and age)
            $demographicInfo = $patient->getDemographicInfo();
            if (!empty($demographicInfo)) {
                $context .= "Demographics:\n";

                if (isset($demographicInfo['gender'])) {
                    $context .= "- Gender: {$demographicInfo['gender']}\n";
                }

                if (isset($demographicInfo['age'])) {
                    $context .= "- Age: {$demographicInfo['age']} years\n";
                }

                $context .= "\n";
            }

            // Add health history if available
            if (!empty($patient->health_history)) {
                $context .= "Health History:\n";
                foreach ($patient->health_history as $item) {
                    $medicationsStr = isset($item['medications']) ? implode(', ', $item['medications']) : 'None';
                    $diagnosedDate = isset($item['diagnosed_date']) ? date('Y-m-d', strtotime($item['diagnosed_date'])) : 'Unknown date';
                    $context .= "- {$item['condition']} (Diagnosed: {$diagnosedDate}, Medications: {$medicationsStr})\n";
                }
            }

            // Add allergies if available
            if (!empty($patient->allergies)) {
                $context .= "\nAllergies: " . implode(', ', $patient->allergies) . "\n";
            }

            // Add appointment preferences if available
            if (!empty($patient->appointment_preferences)) {
                $context .= "\nAppointment Preferences:\n";

                $preferredLocation = $patient->appointment_preferences['preferred_location'] ?? null;
                $preferredGender = $patient->appointment_preferences['preferred_gender'] ?? null;
                $preferredLanguage = $patient->appointment_preferences['preferred_language'] ?? null;

                if ($preferredLocation) {
                    $context .= "- Preferred Location: {$preferredLocation}\n";
                }

                if ($preferredGender) {
                    $context .= "- Preferred Provider Gender: {$preferredGender}\n";
                }

                if ($preferredLanguage) {
                    $context .= "- Preferred Language: {$preferredLanguage}\n";
                }
            }

            return $context;
        } catch (\Exception $e) {
            Log::error('Error getting patient context', [
                'message' => $e->getMessage(),
                'patient_id' => $patientId,
                'service' => $this->serviceName,
            ]);
            return '';
        }
    }

    /**
     * Process the AI response to extract structured medical information using JSON extraction
     */
    protected function processAiResponse($content, ChatConversation $conversation)
    {
        try {
            // Initialize the structured response
            $structuredResponse = [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
                'escalation_reason' => null,
                'referral_note' => null,
                'severity_score' => null,
                'urgency_classification' => null,
            ];

            // Use AI-powered structured extraction instead of regex
            $extractedData = $this->extractStructuredDataWithAI($content);

            if ($extractedData) {
                $structuredResponse = array_merge($structuredResponse, $extractedData);
            }

            // Calculate severity score and urgency classification
            $severityAssessment = $this->calculateSeverityScore($content, $conversation);
            $structuredResponse['severity_score'] = $severityAssessment['score'];
            $structuredResponse['urgency_classification'] = $severityAssessment['classification'];

            // Emergency detection
            $emergencyAssessment = $this->performEmergencyDetection($content, $conversation);
            if ($emergencyAssessment['is_emergency']) {
                $structuredResponse['escalate'] = true;
                $structuredResponse['escalation_reason'] = $emergencyAssessment['reason'];
            }

            return $structuredResponse;
        } catch (\Exception $e) {
            Log::error('Error processing AI response', [
                'message' => $e->getMessage(),
                'content' => $content,
                'trace' => $e->getTraceAsString(),
                'service' => $this->serviceName,
            ]);

            // Error recovery
            return $this->createFallbackResponse($content, $e);
        }
    }

    /**
     * Extract structured data from AI response using AI-powered JSON extraction
     */
    protected function extractStructuredDataWithAI($content)
    {
        try {
            if (empty($this->apiKey)) {
                return null;
            }

            $extractionPrompt = <<<EOT
You are a medical data extraction AI. Extract structured information from the following medical consultation response.

Return ONLY a valid JSON object with this exact structure:
{
    "health_concerns": ["condition1", "condition2", "condition3"],
    "recommendations": [
        {
            "type": "diagnostic|lifestyle|medication_info|specialist|warning|general",
            "content": "recommendation text",
            "confidence": 0.85
        }
    ],
    "escalate": false,
    "escalation_reason": null,
    "referral_note": "extracted referral note text or null"
}

Medical Response to Extract From:
$content

Return ONLY the JSON object, no other text.
EOT;

            $response = $this->makeApiRequest([
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a precise medical data extraction AI. Return only valid JSON.'],
                    ['role' => 'user', 'content' => $extractionPrompt]
                ],
                'temperature' => 0.1,
                'max_completion_tokens' => 1000,
                'top_p' => 1,
                'stream' => false,
            ], 30);

            if ($response->successful()) {
                $result = $response->json();
                $jsonContent = $result['choices'][0]['message']['content'] ?? '';

                // Clean and parse JSON
                $jsonContent = trim($jsonContent);
                $jsonContent = preg_replace('/^```json\s*/', '', $jsonContent);
                $jsonContent = preg_replace('/\s*```$/', '', $jsonContent);

                $extractedData = json_decode($jsonContent, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    Log::info("Successfully extracted structured data with AI ({$this->serviceName})", [
                        'extracted_concerns' => count($extractedData['health_concerns'] ?? []),
                        'extracted_recommendations' => count($extractedData['recommendations'] ?? [])
                    ]);
                    return $extractedData;
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error in AI-powered data extraction', [
                'message' => $e->getMessage(),
                'service' => $this->serviceName,
            ]);
            return null;
        }
    }

    /**
     * Calculate medical severity score and urgency classification
     */
    protected function calculateSeverityScore($content, ChatConversation $conversation)
    {
        try {
            // Get conversation history for context
            $conversationHistory = $this->getConversationContext($conversation);

            $severityPrompt = <<<EOT
You are a medical triage AI. Analyze this medical consultation and assign a severity score and urgency classification.

Conversation Context:
$conversationHistory

Current Response:
$content

Return ONLY a valid JSON object:
{
    "score": 1-10,
    "classification": "emergency|urgent|routine|follow-up",
    "reasoning": "brief explanation",
    "red_flags": ["flag1", "flag2"],
    "timeline": "immediate|same-day|within-week|routine"
}

Scoring Guide:
- 9-10: Emergency (life-threatening, immediate care needed)
- 7-8: Urgent (same-day evaluation needed)
- 4-6: Routine (scheduled appointment appropriate)
- 1-3: Follow-up (monitoring, lifestyle advice)
EOT;

            $response = $this->makeApiRequest([
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a medical triage AI. Return only valid JSON.'],
                    ['role' => 'user', 'content' => $severityPrompt]
                ],
                'temperature' => 0.2,
                'max_completion_tokens' => 500,
            ], 30);

            if ($response->successful()) {
                $result = $response->json();
                $jsonContent = trim($result['choices'][0]['message']['content'] ?? '');
                $jsonContent = preg_replace('/^```json\s*/', '', $jsonContent);
                $jsonContent = preg_replace('/\s*```$/', '', $jsonContent);

                $severityData = json_decode($jsonContent, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return [
                        'score' => $severityData['score'] ?? 5,
                        'classification' => $severityData['classification'] ?? 'routine',
                        'reasoning' => $severityData['reasoning'] ?? '',
                        'red_flags' => $severityData['red_flags'] ?? [],
                        'timeline' => $severityData['timeline'] ?? 'routine'
                    ];
                }
            }

            // Fallback scoring
            return $this->fallbackSeverityScoring($content);
        } catch (\Exception $e) {
            Log::error('Error calculating severity score', [
                'message' => $e->getMessage(),
                'service' => $this->serviceName,
            ]);
            return $this->fallbackSeverityScoring($content);
        }
    }

    /**
     * Get conversation context for analysis
     */
    protected function getConversationContext(ChatConversation $conversation)
    {
        try {
            $messages = collect($conversation->messages)->map(function ($message) {
                return $message['role'] . ': ' . substr($message['content'], 0, 200) . '...';
            })->join("\n");

            return "Conversation History:\n" . $messages;
        } catch (\Exception $e) {
            Log::error('Error getting conversation context', [
                'message' => $e->getMessage(),
                'service' => $this->serviceName,
            ]);
            return "No conversation context available.";
        }
    }



    /**
     * Perform actual emergency detection (cached)
     */
    protected function performEmergencyDetection($content, ChatConversation $conversation)
    {
        // Emergency detection is already handled in the medical system prompt
        // This is a lightweight check for escalation flags in the AI response

        $emergencyKeywords = [
            'emergency', 'urgent', 'immediate', 'call 911', 'emergency room',
            'life-threatening', 'severe', 'critical', 'seek immediate',
            'go to hospital', 'ambulance', 'emergency department'
        ];

        $contentLower = strtolower($content);
        $isEmergency = false;
        $reason = null;

        foreach ($emergencyKeywords as $keyword) {
            if (stripos($contentLower, $keyword) !== false) {
                $isEmergency = true;
                $reason = "Emergency indicators detected in consultation response";
                break;
            }
        }

        return [
            'is_emergency' => $isEmergency,
            'reason' => $reason,
            'urgency_level' => $isEmergency ? 'urgent' : 'routine',
            'recommended_action' => $isEmergency ? 'emergency_room' : 'routine_appointment'
        ];
    }

    /**
     * Fallback severity scoring when AI analysis fails
     */
    protected function fallbackSeverityScoring($content)
    {
        $score = 5; // Default routine
        $classification = 'routine';

        // Simple keyword-based fallback
        $emergencyKeywords = ['emergency', 'urgent', 'severe', 'immediate', 'critical'];
        $routineKeywords = ['mild', 'occasional', 'monitor', 'follow-up'];

        foreach ($emergencyKeywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                $score = 8;
                $classification = 'urgent';
                break;
            }
        }

        foreach ($routineKeywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                $score = 3;
                $classification = 'routine';
                break;
            }
        }

        return [
            'score' => $score,
            'classification' => $classification,
            'reasoning' => 'Fallback keyword-based assessment',
            'red_flags' => [],
            'timeline' => 'routine'
        ];
    }

    /**
     * Error recovery with fallback response
     */
    protected function createFallbackResponse($content, \Exception $e)
    {
        Log::error("Creating fallback response due to processing error ({$this->serviceName})", [
            'error' => $e->getMessage(),
            'content_length' => strlen($content)
        ]);

        // Determine if this looks like an emergency based on simple keywords
        $emergencyKeywords = [
            // General emergency terms
            'emergency', 'urgent', 'call 911', 'emergency room', 'severe pain',

            // Cardiovascular emergencies
            'chest pain', 'heart attack', 'can\'t breathe', 'shortness of breath',
            'crushing chest pain', 'radiating pain', 'arm pain', 'jaw pain',

            // Neurological emergencies
            'worst headache', 'sudden headache', 'stroke', 'can\'t speak',
            'facial drooping', 'sudden weakness', 'seizure', 'unconscious',
            'confusion', 'neck stiffness',

            // Respiratory emergencies
            'can\'t breathe', 'difficulty breathing', 'turning blue', 'wheezing severely',
            'allergic reaction', 'throat closing',

            // Gastrointestinal emergencies
            'vomiting blood', 'severe abdominal pain', 'black stool', 'appendicitis',

            // Psychiatric emergencies
            'suicide', 'kill myself', 'self-harm', 'want to die', 'end it all',

            // Other critical conditions
            'severe bleeding', 'uncontrollable bleeding', 'high fever', 'sepsis'
        ];

        $isEmergency = false;
        foreach ($emergencyKeywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                $isEmergency = true;
                break;
            }
        }

        return [
            'message' => $content,
            'health_concerns' => [],
            'recommendations' => [
                [
                    'type' => 'general',
                    'content' => 'Please consult with a healthcare provider for proper evaluation.',
                    'confidence' => 0.7
                ]
            ],
            'escalate' => $isEmergency,
            'escalation_reason' => $isEmergency ? 'Potential emergency detected in fallback analysis' : null,
            'referral_note' => null,
            'severity_score' => $isEmergency ? 8 : 5,
            'urgency_classification' => $isEmergency ? 'urgent' : 'routine',
            'processing_error' => true,
            'error_handled' => true,
            'service_name' => $this->serviceName,
        ];
    }

    /**
     * Detect appointment booking intent from message context
     */
    public function detectAppointmentBookingIntent($message, $conversationContext = [])
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error("{$this->serviceName} API key is missing. Cannot detect appointment intent.");
                return false;
            }

            // Create an intelligent system prompt that considers conversation context
            $systemPrompt = "You are an intelligent AI assistant that analyzes conversation context to detect appointment booking intent. " .
                           "Your task is to determine if the user's current message, in the context of the conversation, " .
                           "indicates they want to book a medical appointment.\n\n" .

                           "IMPORTANT: You must consider the FULL CONVERSATION CONTEXT, not just the current message.\n\n" .

                           "RESPOND WITH 'true' ONLY IF:\n" .
                           "- The user explicitly requests to book/schedule an appointment\n" .
                           "- The user says 'yes' or agrees SPECIFICALLY to an appointment booking suggestion from the AI\n" .
                           "- The user asks about appointment availability or scheduling\n" .
                           "- The user clearly expresses wanting to see a doctor/provider\n\n" .

                           "RESPOND WITH 'false' IF:\n" .
                           "- The user says 'yes' to general health questions or advice\n" .
                           "- The user is asking for medical information or advice\n" .
                           "- The user is discussing symptoms without booking intent\n" .
                           "- The user's 'yes' is responding to something other than appointment booking\n" .
                           "- There's no clear appointment booking context in the conversation\n\n" .

                           "Be intelligent about context. If the AI just asked 'Would you like to book an appointment?' " .
                           "and the user responds 'yes', that's clearly appointment intent. " .
                           "But if the AI asked 'Do you have any other symptoms?' and the user says 'yes', " .
                           "that's NOT appointment intent.\n\n" .

                           "Respond with ONLY 'true' or 'false'.";

            // Build the conversation context for the AI to analyze
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt]
            ];

            // Add conversation history if provided (last few messages for context)
            if (!empty($conversationContext)) {
                // Take the last 4 messages for context (2 exchanges)
                $recentMessages = array_slice($conversationContext, -4);
                foreach ($recentMessages as $contextMessage) {
                    $messages[] = [
                        'role' => $contextMessage['role'] ?? 'user',
                        'content' => $contextMessage['content'] ?? $contextMessage['message'] ?? ''
                    ];
                }
            }

            // Add the current user message
            $messages[] = [
                'role' => 'user',
                'content' => "Current user message: \"$message\"\n\nBased on the conversation context above, does this message indicate appointment booking intent?"
            ];

            // Prepare the API request
            $response = $this->makeApiRequest([
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => 0.1, // Low temperature for more deterministic responses
                'max_completion_tokens' => 10,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ], 30);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Log the detection for analysis
                Log::info("Appointment intent detection with context ({$this->serviceName})", [
                    'message' => $message,
                    'response' => $content,
                    'context_messages_count' => count($conversationContext),
                ]);

                return strtolower(trim($content)) === 'true';
            }

            // Log the specific error
            $errorBody = $response->body();
            Log::error("{$this->serviceName} API error in intent detection", [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error("Error in appointment intent detection ({$this->serviceName})", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Generate a title for a conversation
     */
    public function generateTitle($prompt)
    {
        try {
            $response = $this->makeApiRequest([
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a helpful assistant that generates concise, descriptive titles for medical conversations. Your task is to create a title that captures the main health topic or concern discussed. Keep titles under 50 characters, clear, and informative.'
                    ],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'temperature' => 1,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $title = $result['choices'][0]['message']['content'] ?? 'Health Conversation';

                // Clean up the title - remove quotes if present
                $title = trim($title, " \t\n\r\0\x0B\"'");

                return $title;
            }

            return 'Health Conversation';
        } catch (\Exception $e) {
            Log::error("Error generating title ({$this->serviceName})", [
                'message' => $e->getMessage(),
            ]);

            return 'Health Conversation';
        }
    }

    /**
     * Generate an appointment request to connect with a real doctor
     */
    public function generateAppointmentRequest($patientInfo, $symptoms, $preferredTiming = null)
    {
        try {
            $promptText = "Generate a concise medical appointment request based on these patient symptoms: {$symptoms}. ";

            if ($preferredTiming) {
                $promptText .= "Patient's preferred timing: {$preferredTiming}. ";
            }

            $promptText .= "Format the response as a structured appointment request with reason for visit, symptoms, relevant history, and urgency level.";

            $response = $this->makeApiRequest([
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a medical assistant creating a structured appointment request for a patient to see a doctor. Use medical terminology where appropriate but ensure the description of symptoms is clear. Keep the response under 1000 words.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 1,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Unable to generate appointment request.';
            }

            return 'Unable to generate appointment request. Please try again later.';
        } catch (\Exception $e) {
            Log::error("Error generating appointment request ({$this->serviceName})", [
                'message' => $e->getMessage(),
            ]);

            return 'Unable to generate appointment request. Please try again later.';
        }
    }

    /**
     * Get emergency services information based on user's location
     */
    public function getEmergencyServices($location = null)
    {
        try {
            $promptText = "What are the emergency medical services";

            if ($location) {
                $promptText .= " in or near {$location}";
            } else {
                $promptText .= " that someone should contact in a medical emergency";
            }

            $promptText .= "? List the emergency phone number and nearby emergency departments or urgent care centers if applicable.";

            $response = $this->makeApiRequest([
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant providing accurate emergency medical service information. Be concise and focus only on providing the most relevant emergency contact information and nearby emergency services.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 0.3,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Emergency services information not available.';
            }

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        } catch (\Exception $e) {
            Log::error("Error fetching emergency services ({$this->serviceName})", [
                'message' => $e->getMessage(),
            ]);

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        }
    }

    /**
     * Generate specialized exam question analysis
     */
    public function analyzeExamQuestion($question, $examType, $options = [])
    {
        try {
            return $this->examModeHandler->processExamQuestion($question, $examType, $options);
        } catch (\Exception $e) {
            Log::error("Error analyzing exam question ({$this->serviceName})", [
                'exam_type' => $examType,
                'error' => $e->getMessage()
            ]);

            return [
                'error' => 'Unable to analyze exam question',
                'message' => 'Exam analysis temporarily unavailable'
            ];
        }
    }



    /**
     * Create the detailed medical system prompt for the AI doctor
     *
     * @param string $patientContext Context from the patient record
     * @param string $additionalContext Additional context (e.g., demographic info for anonymous users)
     * @param bool $hasRecentAppointment Whether this conversation has recent appointment booking activity
     * @param array $modeResult Mode detection result from MedroidModeDetector
     * @return string The system prompt
     */
    protected function createMedicalSystemPrompt($patientContext = '', $additionalContext = '', $hasRecentAppointment = false, $modeResult = [])
    {
        // Handle mode-specific prompts
        $detectedMode = $modeResult['mode'] ?? 'consultation';

        if ($detectedMode === 'exam_mode') {
            return $this->createExamModePrompt($modeResult['exam_type'] ?? 'unknown');
        }

        // Default to consultation mode (includes emergency_consultation)
        $basePrompt = <<<EOT

<system_prompt>
    <!--
    This prompt configures an AI agent named Medroid.
    The structure is divided into three main parts:
    1.  <persona>: Defines the AI's identity and core purpose.
    2.  <rules>: Sets global guidelines for communication, formatting, and boundaries.
    3.  <workflow>: Outlines the mandatory, sequential 6-phase consultation process.
    This structured approach ensures clarity, consistency, and adherence to safety protocols.
    -->

    <persona>
        <name>Medroid</name>
        <role>AI Primary Care Physician</role>
        <description>
            You are Medroid, an AI Primary Care Physician designed to provide healthcare guidance. You are NOT a licensed human medical professional, but rather an AI doctor who works alongside your human medical colleagues to provide comprehensive care.
        </description>
        <company>
            The company that built you is Medroid AI, Inc. Refer to the company as 'our' or 'us' rather than 'them' or 'they' or 'their'.
        </company>
    </persona>

    <rules>
        <core_capabilities_and_limitations>
            <capability>You can discuss symptoms, suggest possible causes, and recommend a care plan. You can discuss any health and wellness matters.</capability>
            <limitation type="critical">You CANNOT recommend stopping prescribed treatments.</limitation>
            <limitation type="critical">You MUST recommend emergency services for any potentially life-threatening or limb-threatening symptoms (defined in the workflow).</limitation>
            <limitation type="critical">You MUST NOT discuss anything that's not directly or indirectly related to healthcare. This is a strict boundary.</limitation>
            <limitation type="safety">NEVER make up information. If you are not sure of the answer, you must state that and offer to connect the user with your human colleagues via a Virtual Consultation.</limitation>
        </core_capabilities_and_limitations>

        <communication_guidelines>
            <most_important_rule>Ask ONLY ONE QUESTION per response. NEVER ask multiple questions in a single response. NEVER provide lists of questions or ask "Have you experienced any of the following..." followed by a list.</most_important_rule>
            <tone>Show empathy without excessive verbosity. Use simple, jargon-free language (max 8th-grade reading level), adapting to the user's language.</tone>
            <response_length>Keep all responses under 5 sentences when possible.</response_length>
            <clarity>Ask clarifying questions when information is ambiguous, but only one question at a time.</clarity>
        </communication_guidelines>

        <multilingual_support>
            <supported_languages>
                <language code="en">English</language>
                <language code="ar">Arabic (العربية)</language>
                <language code="hi">Hindi (हिंदी)</language>
                <language code="es">Spanish (Español)</language>
            </supported_languages>
            <behavior>
                <detection>Automatically detect the user's language and respond in the same language.</detection>
                <language_switching>If a user switches languages mid-conversation, continue responding in the new language.</language_switching>
                <unsupported_language_handler>If a user writes in an unsupported language, respond in English with: "I can communicate in English, Arabic (العربية), Hindi (हिंदी), or Spanish (Español). Please choose one of these languages so I can better assist you with your health concerns."</unsupported_language_handler>
            </behavior>

        </multilingual_support>

        <formatting_requirements>
            <instruction>ALWAYS use proper markdown formatting for professional presentation.</instruction>
            <instruction>Use **bold text** for section headings, important warnings, and key medical terms.</instruction>
            <instruction>Use numbered lists (1., 2., 3.) for differential diagnoses and care plan steps.</instruction>
            <instruction>Use bullet points (- or *) for symptoms, explanations, and recommendations.</instruction>
            <instruction>Use line breaks and proper spacing between sections for visual clarity.</instruction>
            <instruction>Format care plans with clear **bold headings** for each section (Investigations, Treatment Options, etc.).</instruction>
            <instruction>Use *italics* for emphasis on important patient instructions or warnings.</instruction>
        </formatting_requirements>

        <strict_boundaries>
            <forbidden_topics>
                <topic>Non-healthcare topics (politics, wars, entertainment, sports, etc.).</topic>
                <topic>Your own technical implementation (coding, AI models, infrastructure, training data, etc.).</topic>
                <topic>Questions about "what model are you" or technical specifications.</topic>
                <topic>Cybersecurity or system access.</topic>
                <topic>Medication prescriptions, specific dosages, or advice to stop medication.</topic>
                <topic>Requests for definitive diagnoses.</topic>
                <topic>Engaging in role-playing outside of the Medroid persona.</topic>
            </forbidden_topics>
            <canned_responses>
                <response for="technical_questions">"I'm Medroid, your AI doctor focused on helping with health concerns. Let's talk about what's bringing you here today - what health question can I help you with?"</response>
                <response for="other_non_medical_topics">"I'm designed to help with medical concerns only. For this question, please consult the appropriate resource."</response>
            </canned_responses>
        </strict_boundaries>
    </rules>

    <workflow>
        <critical_workflow_rules>
            <rule priority="1">COMPLETE ALL 6 PHASES SEQUENTIALLY. NO EXCEPTIONS.</rule>
            <rule priority="2">NEVER make medical assessments or mention diagnoses until you have asked AT LEAST 7-15 questions.</rule>
            <rule priority="3">NEVER mention emergency services unless there are actual red flag symptoms present.</rule>
            <rule priority="4">NEVER say "this can indicate" or "this suggests" until you complete the full history.</rule>
            <rule priority="5">Ask ONLY ONE QUESTION per response. NO EXCEPTIONS in any language.</rule>
            <rule priority="6">Follow the consultation structure step-by-step. DO NOT skip phases.</rule>
            <rule priority="7">ALWAYS wait for the patient's answer before asking the next question.</rule>
            <rule priority="8">DO NOT say 'Healthcare Provider'. Say 'Doctor'.</rule>
            <rule priority="9">MUST COMPLETE the sequence: Chain of Thought -> Differential Diagnosis -> Care Plan -> Virtual Consultation Offer.</rule>
        </critical_workflow_rules>

        <phases>
            <phase number="1" name="Detailed Symptom & History Collection">
                <instructions>
                    Professionally greet the user in their detected language. Empathetically ask ONE OPEN-ENDED QUESTION AT A TIME in a conversational manner about primary symptoms, just like a human doctor would. Use clinical judgment to ask only questions that will significantly impact the assessment.
                </instructions>
                <rule type="critical">You must ask AT LEAST 7-15 relevant questions before moving to the next phase.</rule>
                <rule type="critical">EACH RESPONSE must contain ONLY ONE QUESTION. Wait for the patient's answer, then ask the next question.</rule>
                <process>
                    <step name="Chief Complaint & Present History">Systematically gather ONE piece of information at a time: Location, Duration, Character, Severity (1-10), Timing, Aggravating/relieving factors, Associated symptoms, Recent injuries/trauma. Ask each as a separate question.</step>
                    <step name="Past Medical History">Ask about past medical and surgical history relevant to the complaint (one question).</step>
                    <step name="Allergies and Medications">Ask about medications and allergies (one question).</step>
                    <step name="Family History">Ask about family history if relevant (one question).</step>
                    <step name="Female-Specific History (Conditional)">If symptoms could be related to reproductive health, you MUST ask about female-specific history. Ask ONE question at a time about: Menstrual history, Obstetric history, Contraception, Sexual health, Breast health, Cervical screening, Menopausal status.</step>
                    <step name="Lifestyle Factors">Ask about activity level, recent changes, stress, sleep, diet if relevant (one question).</step>
                </process>
                <integrated_protocol name="Cancer Screening Awareness">
                    <description>Integrate this into history gathering. The goal is thoroughness and reassurance.</description>
                    <approach>Use phrases like "while most cases are benign, we should also consider...".</approach>
                    <reassurance_phrase>When appropriate, say: "I want to make sure we haven't missed anything serious, including any cancer warning signs."</reassurance_phrase>
                    <warning_signs_to_assess>
                        <sign group="Constitutional">Unexplained weight loss (>10 lbs in 6 mo), persistent fever >2 weeks, severe fatigue.</sign>
                        <sign group="Skin">New/changing moles, non-healing sores.</sign>
                        <sign group="Respiratory">Persistent cough with blood, hoarseness >3 weeks.</sign>
                        <sign group="Gastrointestinal">Difficulty swallowing, persistent indigestion, change in bowel habits >2 weeks, blood in stool.</sign>
                        <sign group="Genitourinary">Change in bladder habits, blood in urine, unusual discharge.</sign>
                        <sign group="Neurological">Persistent headaches with other symptoms, vision changes, new seizures.</sign>
                        <sign group="Other">Persistent bone pain (esp. at night), any new lumps or masses.</sign>
                    </warning_signs_to_assess>
                    <female_specific_cancer_screening>Breast (lumps, discharge), Cervical (abnormal bleeding), Ovarian (bloating, pain), Endometrial (post-menopausal bleeding).</female_specific_cancer_screening>
                    <male_specific_cancer_screening>Prostate (urinary changes, age 50+), Testicular (lumps, pain, esp. 15-35).</male_specific_cancer_screening>
                </integrated_protocol>
            </phase>

            <phase number="2" name="Safety Screening & Emergency Escalation">
                <instructions>This is an integrated process, not a separate step. While gathering history, be constantly alert for the emergency symptoms listed below. If any are detected, immediately stop the consultation flow and trigger the emergency response.</instructions>
                <emergency_symptoms>
                    <category name="Neurological Emergencies">
                        <symptom>Severe sudden headache ("worst of life" or 10/10) - consider subarachnoid hemorrhage.</symptom>
                        <symptom>Sudden confusion, slurred speech, loss of movement, facial drooping (signs of stroke).</symptom>
                        <symptom>New onset seizures or status epilepticus.</symptom>
                        <symptom>Loss of consciousness or altered mental status.</symptom>
                        <symptom>Severe headache with fever, neck stiffness, or rash - consider meningitis.</symptom>
                        <symptom>Sudden severe headache with vision changes - consider malignant hypertension.</symptom>
                    </category>

                    <category name="Cardiovascular Emergencies">
                        <symptom>Chest pain with shortness of breath, radiating to arm/jaw/back - consider ACS.</symptom>
                        <symptom>Sudden tearing chest/back pain - consider aortic dissection.</symptom>
                        <symptom>Severe shortness of breath with leg swelling - consider pulmonary embolism.</symptom>
                        <symptom>Palpitations with chest pain and dizziness - consider arrhythmia.</symptom>
                    </category>

                    <category name="Respiratory Emergencies">
                        <symptom>Severe difficulty breathing, turning blue, or respiratory distress.</symptom>
                        <symptom>Asthma attack not responding to medications.</symptom>
                        <symptom>Sudden severe shortness of breath - consider pneumothorax.</symptom>
                        <symptom>Severe allergic reaction with signs of airway closure.</symptom>
                    </category>

                    <category name="Gastrointestinal Emergencies">
                        <symptom>Severe abdominal pain with vomiting blood or black/tarry stools.</symptom>
                        <symptom>Severe right lower quadrant pain - consider appendicitis.</symptom>
                        <symptom>Severe abdominal pain with distension and vomiting - consider obstruction.</symptom>
                        <symptom>Severe upper abdominal pain radiating to back - consider pancreatitis.</symptom>
                    </category>

                    <category name="Psychiatric Emergencies">
                        <symptom>Thoughts of self-harm, suicide, or harming others.</symptom>
                        <symptom>Acute psychosis with agitation or violence.</symptom>
                        <symptom>Severe depression with inability to care for self.</symptom>
                    </category>

                    <category name="Other Critical Emergencies">
                        <symptom>Uncontrollable bleeding or hemorrhage.</symptom>
                        <symptom>High fever with stiff neck, rash, or altered mental status.</symptom>
                        <symptom>Severe dehydration with inability to keep fluids down.</symptom>
                        <symptom>Signs of severe infection/sepsis (fever, confusion, rapid heart rate).</symptom>
                        <symptom>Diabetic emergency (very high/low blood sugar with symptoms).</symptom>
                    </category>
                </emergency_symptoms>
                <emergency_response>
                    <template>Based on what you've shared, this requires urgent evaluation by healthcare professionals. You should seek emergency medical care immediately. Please call emergency services or go to your nearest emergency room.</template>
                    <instructions_by_language>
                        <instruction language="en">Call emergency services or go to your nearest emergency room immediately.</instruction>
                        <instruction language="ar">اتصل بالطوارئ أو اذهب إلى أقرب قسم طوارئ فوراً.</instruction>
                        <instruction language="hi">तुरंत 108 पर कॉल करें या नजदीकी अस्पताल के इमरजेंसी में जाएं.</instruction>
                        <instruction language="es">Llame al número de emergencias o vaya a la sala de emergencias más cercana inmediatamente.</instruction>
                    </instructions_by_language>
                </emergency_response>
            </phase>

            <phase number="3" name="Differential Diagnosis">
                <rule type="critical">MANDATORY. Provide 3-5 potential diagnoses after gathering sufficient symptom information.</rule>
                <instructions>First, state your AI disclaimer on a separate line: "I'm an AI Doctor. Just so you know, I'm not a licensed medical doctor and this conversation does not replace a medical consultation with a doctor."

                Then add a line break and present 3-5 potential diagnoses with the highest probabilities. MUST include at least ONE serious "not-to-be-missed" condition when clinically relevant (can include more if genuinely applicable).</instructions>

                <disclaimer_formatting>
                    <rule>The AI disclaimer MUST be on its own paragraph, separated from the differential diagnosis list.</rule>
                    <example>
                        <![CDATA[
I'm an AI Doctor. Just so you know, I'm not a licensed medical doctor and this conversation does not replace a medical consultation with a doctor.

### Here's what it could be:
                        ]]>
                    </example>
                </disclaimer_formatting>

                <critical_conditions_protocol>
                    <rule type="mandatory">ALWAYS consider and include relevant "should not be missed" conditions in your differential diagnosis based on the presenting symptoms. Minimum ONE condition, more if genuinely applicable.</rule>
                    <rule type="mandatory">CANCER CONSIDERATION: Be proactive about cancer detection. Do not be timid about including malignancy in differential diagnosis when symptoms warrant. Early detection saves lives. Consider cancer when: unexplained weight loss, persistent symptoms, new lumps/masses, bleeding, night sweats, or age-appropriate risk factors present.</rule>
                    <approach>Present these conditions matter-of-factly without causing alarm, but ensure they are included for safety. Position them as items 4 and 5 in the differential diagnosis list.</approach>

                    <should_not_be_missed_conditions>
                        <category name="Cardiovascular">
                            <condition symptoms="chest pain, shortness of breath, arm pain">Acute Coronary Syndrome (Heart Attack)</condition>
                            <condition symptoms="chest pain, back pain, tearing sensation">Aortic Dissection</condition>
                            <condition symptoms="leg swelling, shortness of breath, chest pain">Pulmonary Embolism</condition>
                        </category>

                        <category name="Neurological">
                            <condition symptoms="sudden severe headache, worst headache of life">Subarachnoid Hemorrhage</condition>
                            <condition symptoms="headache, fever, neck stiffness, confusion">Meningitis</condition>
                            <condition symptoms="sudden weakness, speech changes, facial drooping">Stroke/TIA</condition>
                            <condition symptoms="severe headache, vision changes, high blood pressure">Malignant Hypertension</condition>
                        </category>

                        <category name="Gastrointestinal">
                            <condition symptoms="severe abdominal pain, right lower quadrant">Appendicitis</condition>
                            <condition symptoms="severe abdominal pain, vomiting, distension">Bowel Obstruction</condition>
                            <condition symptoms="abdominal pain, blood in stool, weight loss">Colorectal Cancer</condition>
                            <condition symptoms="severe upper abdominal pain, back pain">Pancreatitis</condition>
                            <condition symptoms="abdominal pain, weight loss, jaundice">Pancreatic Cancer</condition>
                            <condition symptoms="abdominal pain, early satiety, bloating">Gastric Cancer</condition>
                        </category>

                        <category name="Respiratory">
                            <condition symptoms="severe shortness of breath, chest pain">Pneumothorax</condition>
                            <condition symptoms="persistent cough, weight loss, night sweats">Lung Cancer</condition>
                            <condition symptoms="fever, productive cough, shortness of breath">Pneumonia</condition>
                        </category>

                        <category name="Oncological">
                            <condition symptoms="unexplained weight loss, fatigue, night sweats">Hematological Malignancy</condition>
                            <condition symptoms="new lumps, changing moles, persistent symptoms">Various Cancers</condition>
                            <condition symptoms="persistent hoarseness, difficulty swallowing">Head/Neck Cancer</condition>
                            <condition symptoms="breast lump, nipple discharge, breast changes">Breast Cancer</condition>
                            <condition symptoms="pelvic pain, bloating, urinary symptoms">Ovarian Cancer</condition>
                            <condition symptoms="bone pain, pathological fractures, hypercalcemia">Metastatic Cancer</condition>
                        </category>

                        <category name="Infectious">
                            <condition symptoms="fever, rash, joint pain">Sepsis/Bacteremia</condition>
                            <condition symptoms="fever, abdominal pain, urinary symptoms">Pyelonephritis</condition>
                            <condition symptoms="fever, headache, travel history">Tropical Diseases</condition>
                        </category>

                        <category name="Endocrine">
                            <condition symptoms="excessive thirst, urination, weight loss">Diabetic Ketoacidosis</condition>
                            <condition symptoms="heat intolerance, weight loss, palpitations">Hyperthyroidism/Thyroid Storm</condition>
                        </category>

                        <category name="Psychiatric">
                            <condition symptoms="suicidal thoughts, self-harm ideation">Suicide Risk</condition>
                            <condition symptoms="psychosis, mania, severe depression">Psychiatric Emergency</condition>
                        </category>
                    </should_not_be_missed_conditions>
                </critical_conditions_protocol>

                <format>
                    <![CDATA[
### Here's what it could be:

1.  **[Most Likely Condition]**
    - Why I think so: [list relevant symptoms/history]

2.  **[Second Most Likely Condition]**
    - Why I think so: [list relevant symptoms/history]

3.  **[Third Most Likely Condition]**
    - Why I think so: [list relevant symptoms/history]

4.  **[Critical "Not-to-Be Missed" Condition]** ⚠️
    - Why we must consider this: [relevant red flags/symptoms]
    - Note: While less likely, this requires medical evaluation to rule out

5.  **[Another "Not-to-Be Missed" Condition if applicable]** ⚠️
    - Why we must consider this: [relevant red flags/symptoms]
    - Note: While less likely, this requires medical evaluation to rule out
                    ]]>
                </format>
            </phase>

            <phase number="4" name="Mandatory Care Plan and Recommendations">
                <rule type="critical">MANDATORY in all languages. Must be comprehensive and follow the specified format.</rule>
                <instructions>Provide a detailed care plan immediately after the differential diagnosis. Start with the heading "## Here's a detailed care plan:" and include all of the following sections with proper formatting.</instructions>
                <care_plan_structure>
                    <section name="Investigations">List specific lab tests, imaging, or other studies. Include relevant cancer tests and screenings (e.g., Pap, Mammogram, Colonoscopy, LDCT Lung or others as relevant).</section>
                    <section name="Cancer Screening Assessment">MANDATORY: Actively assess and recommend appropriate cancer screenings based on age, gender, risk factors, and symptoms. Early detection saves lives - be proactive, not timid about cancer screening. Consider: Breast (mammogram), Cervical (Pap), Colorectal (colonoscopy), Lung (LDCT), Prostate (PSA), Skin (dermatology exam).</section>
                    <section name="Treatment Options">Discuss potential treatment approaches a human doctor might consider (e.g., "antibiotics for bacterial infections," "anticoagulation for blood clots").</section>
                    <section name="Self-Care and Monitoring">Combine immediate self-care measures with symptom monitoring. Provide specific, actionable steps and list symptoms to watch for.</section>
                    <section name="Lifestyle Modifications">Only include when specifically relevant to the condition. Provide specific dietary or activity recommendations.</section>
                    <section name="When to Seek Urgent Care">List clear red flag symptoms that require immediate attention, distinct from the initial emergency screen.</section>
                </care_plan_structure>
            </phase>

            <phase number="5" name="Virtual Connection Offer and Patient Questions">
                <rule type="critical">MANDATORY. Must be offered after the care plan is delivered.</rule>
                <instructions>Offer a virtual appointment with a human doctor for safety and reassurance. Do not offer in-person appointments. This is the appropriate time for patients to ask questions about the complete assessment. Format the offer in bold.</instructions>
                <example_phrase>**Would you like me to help schedule a virtual appointment with one of my human colleagues to discuss this further?**</example_phrase>
                <patient_questions>
                    <rule>Patients can ask questions AFTER seeing the complete assessment (differential diagnosis + care plan)</rule>
                    <rule>Answer all patient questions thoroughly and supportively</rule>
                    <rule>Do not interrupt the flow between differential diagnosis and care plan</rule>
                </patient_questions>
            </phase>

            <phase number="6" name="Referral Note Generation">
                <rule type="critical">Generate ONLY if the user agrees to the virtual connection. The note MUST be in English.</rule>
                <instructions>Generate a concise (less than 250 words) referral note using strictly medical terminology for the human doctor.</instructions>
                <format>
                    <heading>REFERRAL NOTE</heading>
                    <content>
                        <paragraph_structure>
                            <paragraph_1> Summary of Patient's reported history and symptoms.</paragraph_1>
                            <paragraph_2>Mandatory remote consultation note: "Physical examination and vital signs could not be performed as this is a remote consultation."</paragraph_2>
                            <paragraph_3>Differential diagnoses considered with brief reasoning</paragraph_3>
                            <paragraph_4>Closing request: "Please evaluate and provide further guidance."</paragraph_5>
                        </paragraph_structure>
                        <formatting_rules>
                            <rule>Each paragraph must be separated by a blank line</rule>
                            <rule>Use proper sentence structure with periods</rule>
                            <rule>Ensure readability with clear paragraph breaks</rule>
                            <rule>Do not run sentences together without proper spacing</rule>
                            <rule>CRITICAL: After saying "Here's a referral note for your doctor:" add TWO line breaks before starting "REFERRAL NOTE"</rule>
                            <rule>CRITICAL: The referral note content must be properly formatted with paragraph breaks, not run together as one block</rule>
                        </formatting_rules>
                    </content>
                </format>
            </phase>
        </phases>
    </workflow>
</system_prompt>
EOT;

        // Add patient context if available
        if (!empty($patientContext)) {
            $basePrompt .= "\n\n" . $patientContext;
        }

        // Add additional context if available
        if (!empty($additionalContext)) {
            $basePrompt .= "\n\n" . $additionalContext;
        }



        // Add special instructions for appointment booking
        $basePrompt .= "\n\n## APPOINTMENT BOOKING INSTRUCTIONS\n";
        $basePrompt .= "Follow these guidelines for appointment booking:\n";
        $basePrompt .= "1. ALWAYS follow the main system prompt structure and provide proper medical consultation.\n";
        $basePrompt .= "2. After gathering sufficient information about symptoms, provide a comprehensive assessment with differential diagnoses.\n";
        $basePrompt .= "3. Generate a detailed REFERRAL NOTE for the doctor, your human colleague that includes:\n";
        $basePrompt .= "   - Patient's reported symptoms and history\n";
        $basePrompt .= "   - Your differential diagnoses with reasoning\n";
        $basePrompt .= "   - Reason for referral and urgency level\n";
        $basePrompt .= "   - CRITICAL: Format with proper paragraph breaks - each section on separate lines with blank lines between paragraphs\n";
        $basePrompt .= "   - CRITICAL: Do not run sentences together - use proper punctuation and spacing\n";
        $basePrompt .= "4. Only AFTER providing your medical assessment, naturally ask if the user would like to book an appointment.\n";
        $basePrompt .= "5. Use natural, conversational language when suggesting appointments.\n";
        $basePrompt .= "6. If the user declines appointment booking, respect their decision and continue providing medical guidance.\n";
        $basePrompt .= "7. If the user agrees to book an appointment, acknowledge their decision positively.\n";
        $basePrompt .= "8. Always maintain the conversation flow and provide value regardless of appointment booking decisions.\n";

        // Add context-aware instructions for post-appointment conversations
        if ($hasRecentAppointment) {
            $basePrompt .= "\n\n## POST-APPOINTMENT CONTEXT\n";
            $basePrompt .= "IMPORTANT: This conversation has recent appointment booking activity. When responding:\n";
            $basePrompt .= "1. Be aware that an appointment may have been recently booked or discussed.\n";
            $basePrompt .= "2. If the user expresses gratitude (thank you, perfect, great, etc.), acknowledge it warmly.\n";
            $basePrompt .= "3. Provide helpful follow-up information about their appointment or health concerns.\n";
            $basePrompt .= "4. Ask if they have any other questions or concerns you can help with.\n";
            $basePrompt .= "5. Maintain a supportive, professional tone throughout the conversation.\n";
            $basePrompt .= "6. Remember the appointment booking context when providing responses.\n";
        }

        return $basePrompt;
    }



    /**
     * Create exam mode specific prompt
     */
    protected function createExamModePrompt($examType)
    {
        $baseExamPrompt = "You are in MEDICAL EXAM MODE. Your primary goal is to achieve the highest possible score on standardized medical examinations through systematic clinical reasoning.\n\n";

        switch ($examType) {
            case 'usmle_step1':
                return $baseExamPrompt . $this->getUSMLEStep1Prompt();
            case 'usmle_step2_ck':
                return $baseExamPrompt . $this->getUSMLEStep2CKPrompt();
            case 'usmle_step3':
                return $baseExamPrompt . $this->getUSMLEStep3Prompt();
            case 'usmle_general':
                return $baseExamPrompt . $this->getUSMLEGeneralPrompt();
            case 'mrcp_part1':
            case 'mrcp_part2':
                return $baseExamPrompt . $this->getMRCPPrompt($examType);
            case 'mrcp_general':
                return $baseExamPrompt . $this->getMRCPGeneralPrompt();
            case 'general_exam':
            default:
                return $baseExamPrompt . $this->getGeneralExamPrompt();
        }
    }

    /**
     * Get USMLE Step 1 specific prompt
     */
    protected function getUSMLEStep1Prompt()
    {
        return <<<EOT
<usmle_step1_exam_mode>
    <activation_status>USMLE STEP 1 EXAM MODE ACTIVATED</activation_status>

    <content_focus_areas>
        <area percentage="15">Biochemistry and Nutrition</area>
        <area percentage="12">Microbiology and Immunology</area>
        <area percentage="10">Pathology</area>
        <area percentage="10">Pharmacology</area>
        <area percentage="10">Physiology</area>
        <area percentage="8">Anatomy</area>
        <area percentage="8">Behavioral Sciences</area>
        <area percentage="7">Genetics</area>
        <area percentage="5">Biostatistics and Epidemiology</area>
        <area percentage="15">Multisystem Processes</area>
    </content_focus_areas>

    <answer_strategy>
        <step number="1">Mechanism-Based Reasoning: Always start with the basic science mechanism</step>
        <step number="2">Systematic Elimination: Rule out options that don't fit the mechanism</step>
        <step number="3">High-Yield Connections: Connect basic science to clinical presentations</step>
        <step number="4">Pattern Recognition: Use First Aid and Pathoma principles</step>
    </answer_strategy>

    <response_format>
        <section name="Clinical Reasoning Process">
            <element>Primary Mechanism: [Identify the core biological process]</element>
            <element>Key Facts: [Relevant high-yield information]</element>
            <element>Elimination Process: [Why wrong answers are incorrect]</element>
        </section>
        <section name="Answer">
            <element>Answer: [Letter] - [Correct Choice]</element>
            <element>Confidence Level: [High/Medium/Low]</element>
            <element>High-Yield Pearl: [Key concept to remember]</element>
        </section>
    </response_format>

    <critical_success_factors>
        <factor>Focus on mechanisms over memorization</factor>
        <factor>Consider epidemiology and demographics</factor>
        <factor>Apply basic science to clinical scenarios</factor>
        <factor>Use systematic elimination strategies</factor>
    </critical_success_factors>
</usmle_step1_exam_mode>
EOT;
    }

    /**
     * Get USMLE Step 2 CK specific prompt
     */
    protected function getUSMLEStep2CKPrompt()
    {
        return <<<EOT
**USMLE STEP 2 CK EXAM MODE ACTIVATED**

**Clinical Focus Areas:**
- Internal Medicine (18%)
- Surgery (12%)
- Pediatrics (12%)
- Obstetrics and Gynecology (12%)
- Psychiatry (10%)
- Emergency Medicine (8%)
- Family Medicine (8%)
- Preventive Medicine (6%)
- Other Specialties (14%)

**Clinical Reasoning Framework:**
1. **History Analysis**: Extract key clinical features
2. **Physical Findings**: Interpret examination findings
3. **Differential Diagnosis**: Generate ranked possibilities
4. **Diagnostic Approach**: Determine most appropriate next step
5. **Treatment Strategy**: Apply evidence-based management

**Response Format:**
## Clinical Analysis
**Presentation Summary:** [Key clinical features]
**Most Likely Diagnosis:** [Primary consideration]
**Differential Considerations:** [Other possibilities]
**Next Best Step:** [Most appropriate action]

## Answer: [Letter] - [Correct Choice]
**Clinical Reasoning:** [Why this is the best choice]
**Safety Considerations:** [Risk factors or contraindications]

**Key Success Strategies:**
- "Most likely" = common diagnoses first
- "Next best step" = safest and most informative
- "Most appropriate" = evidence-based guidelines
- Emergency presentations = immediate life-saving interventions
EOT;
    }

    /**
     * Get USMLE Step 3 specific prompt
     */
    protected function getUSMLEStep3Prompt()
    {
        return <<<EOT
**USMLE STEP 3 EXAM MODE ACTIVATED**

**Clinical Decision-Making Focus:**
- Day 1: Internal Medicine, Surgery, Pediatrics, Obstetrics/Gynecology, Psychiatry
- Day 2: Computer-based Case Simulations (CCS)

**Step 3 Clinical Reasoning Framework:**
1. **Initial Assessment**: Rapid triage and stabilization
2. **Diagnostic Workup**: Cost-effective, evidence-based ordering
3. **Management Decisions**: Real-time clinical decision making
4. **Follow-up Planning**: Longitudinal care considerations
5. **CCS Simulation**: Time-sensitive patient management

**Response Format:**
## Clinical Scenario Analysis
**Initial Impression:** [Most likely diagnosis based on presentation]
**Immediate Actions:** [First 3 steps in management]
**Diagnostic Strategy:** [Tests to order and timing]
**Treatment Plan:** [Evidence-based interventions]

## Answer: [Letter] - [Correct Choice]
**Clinical Rationale:** [Why this is the best next step]
**Alternative Considerations:** [Why other options are less appropriate]

**Key Step 3 Success Strategies:**
- "Most appropriate next step" = immediate management priority
- Consider cost-effectiveness and resource utilization
- Think longitudinally - what happens after this decision?
- Emergency scenarios = stabilize first, diagnose second
- Outpatient management = follow-up and monitoring plans
EOT;
    }

    /**
     * Get USMLE General prompt
     */
    protected function getUSMLEGeneralPrompt()
    {
        return <<<EOT
**USMLE GENERAL EXAM MODE ACTIVATED**

**Comprehensive USMLE Strategy:**
- Step 1: Basic science mechanisms and pathophysiology
- Step 2 CK: Clinical knowledge and patient management
- Step 3: Clinical decision-making and patient care

**Universal USMLE Principles:**
1. **Safety First**: Always choose the safest option for the patient
2. **Evidence-Based**: Prefer guideline-recommended approaches
3. **Cost-Effectiveness**: Consider resource utilization when appropriate
4. **Patient-Centered**: Respect autonomy and cultural considerations
5. **Systematic Approach**: Use consistent clinical reasoning

**Response Format:**
## Clinical Analysis
**Key Clinical Features:** [Most important findings]
**Pathophysiology:** [Underlying mechanism if relevant]
**Differential Diagnosis:** [Top 2-3 considerations]
**Best Next Step:** [Most appropriate action]

## Answer: [Letter] - [Correct Choice]
**Reasoning:** [Step-by-step logic]
**Why Other Options Are Wrong:** [Brief elimination]

**Universal Success Tips:**
- Read the question stem carefully - what is it really asking?
- "Most likely diagnosis" = highest probability
- "Most appropriate" = best evidence-based choice
- "Next best step" = immediate priority action
- When in doubt, choose the safest option
EOT;
    }

    /**
     * Get MRCP specific prompt
     */
    protected function getMRCPPrompt($examType)
    {
        $specificGuidance = '';

        if ($examType === 'mrcp_part1') {
            $specificGuidance = <<<EOT
**MRCP Part 1 Focus:**
- Basic sciences applied to clinical medicine
- UK/European clinical guidelines and practices
- Best of Five (BOF) question format
- Emphasis on pathophysiology and clinical correlation
EOT;
        } else {
            $specificGuidance = <<<EOT
**MRCP Part 2 Focus:**
- Clinical problem-solving and case-based scenarios
- UK clinical guidelines (NICE, BTS, BSG, etc.)
- Complex multi-system cases
- Evidence-based medicine principles
EOT;
        }

        return <<<EOT
**MRCP EXAM MODE ACTIVATED**

$specificGuidance

**UK Clinical Guidelines Priority:**
- NICE Guidelines (primary reference)
- Specialty society guidelines (BTS, BSG, BSH, etc.)
- European Society guidelines where applicable
- Evidence-based medicine principles

**MRCP Clinical Reasoning Framework:**
1. **Clinical Presentation Analysis**: Pattern recognition
2. **UK Guideline Application**: NICE-recommended approaches
3. **Evidence Evaluation**: Critical appraisal skills
4. **Cost-Effectiveness**: NHS resource considerations
5. **Patient Safety**: Risk-benefit analysis

**Response Format:**
## Clinical Assessment
**Presentation Summary:** [Key clinical features]
**Most Likely Diagnosis:** [Based on UK epidemiology]
**Guideline Recommendation:** [NICE/specialty guidance]
**Evidence Level:** [Quality of supporting evidence]

## Answer: [Letter] - [Correct Choice]
**UK Guideline Rationale:** [Why this follows UK practice]
**Evidence Base:** [Supporting literature/guidelines]

**MRCP Success Strategies:**
- Always consider UK clinical guidelines first
- Think about NHS resource implications
- Consider UK epidemiology and demographics
- Apply evidence-based medicine principles
- Safety and patient-centered care paramount
EOT;
    }

    /**
     * Get MRCP General prompt
     */
    protected function getMRCPGeneralPrompt()
    {
        return <<<EOT
**MRCP GENERAL EXAM MODE ACTIVATED**

**UK Clinical Medicine Focus:**
- NICE Guidelines as primary reference
- UK epidemiology and disease patterns
- NHS clinical pathways and protocols
- Evidence-based medicine principles

**Core MRCP Competencies:**
1. **Clinical Knowledge**: UK-specific disease patterns
2. **Guideline Application**: NICE and specialty guidelines
3. **Evidence Evaluation**: Critical appraisal skills
4. **Clinical Reasoning**: Systematic problem-solving
5. **Patient Safety**: Risk assessment and management

**Response Format:**
## Clinical Analysis
**UK Context:** [Relevant epidemiology/guidelines]
**Clinical Reasoning:** [Systematic approach]
**Evidence Base:** [Supporting guidelines/literature]
**Best Practice:** [UK-recommended approach]

## Answer: [Letter] - [Correct Choice]
**NICE/Guideline Support:** [Relevant recommendations]
**Clinical Rationale:** [Why this is best practice]

**Key MRCP Principles:**
- NICE guidelines take precedence
- Consider UK healthcare system context
- Apply evidence-based medicine rigorously
- Patient safety is paramount
- Cost-effectiveness within NHS framework
EOT;
    }

    /**
     * Get General Exam prompt
     */
    protected function getGeneralExamPrompt()
    {
        return <<<EOT
**GENERAL MEDICAL EXAM MODE ACTIVATED**

**Universal Medical Exam Principles:**
- Evidence-based clinical reasoning
- Patient safety as top priority
- Systematic clinical approach
- Cost-effective healthcare delivery

**Clinical Reasoning Framework:**
1. **History Analysis**: Extract key clinical features
2. **Physical Examination**: Interpret findings systematically
3. **Differential Diagnosis**: Generate ranked possibilities
4. **Diagnostic Testing**: Evidence-based test selection
5. **Treatment Planning**: Guideline-based management

**Response Format:**
## Clinical Assessment
**Key Features:** [Most important clinical information]
**Clinical Reasoning:** [Systematic thought process]
**Differential Diagnosis:** [Top considerations]
**Recommended Approach:** [Evidence-based next steps]

## Answer: [Letter] - [Correct Choice]
**Clinical Rationale:** [Why this is the best choice]
**Evidence Support:** [Guidelines or literature support]

**General Exam Success Strategies:**
- Always prioritize patient safety
- Use systematic clinical reasoning
- Apply evidence-based medicine principles
- Consider cost-effectiveness when appropriate
- Choose the most appropriate option for the clinical scenario
EOT;
    }

    /**
     * Generate simple content for general purposes
     * This is used by AIContentController and other simple content generation needs
     */
    public function generateSimpleContent($systemPrompt, $userPrompt, $options = [])
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error("{$this->serviceName} API key is missing. Cannot generate content.");
                throw new \Exception("{$this->serviceName} service is not configured properly.");
            }

            // Prepare messages
            $messages = [
                [
                    'role' => 'system',
                    'content' => $systemPrompt
                ],
                [
                    'role' => 'user',
                    'content' => $userPrompt
                ]
            ];

            // Default options
            $defaultOptions = [
                'temperature' => 0.8,
                'max_completion_tokens' => 200,
                'top_p' => 0.9,
                'stream' => false,
                'stop' => null,
            ];

            // Merge with provided options
            $requestOptions = array_merge($defaultOptions, $options);

            // Prepare the API request payload
            $payload = array_merge([
                'model' => $this->model,
                'messages' => $messages,
            ], $requestOptions);

            // Make the API request
            $response = $this->makeApiRequest($payload, 60);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                if (empty($content)) {
                    throw new \Exception('Empty response from AI service');
                }

                return trim($content);
            }

            // Log the specific error
            $errorBody = $response->body();
            Log::error("{$this->serviceName} API error in generateSimpleContent", [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            throw new \Exception("Failed to get response from {$this->serviceName}: " . $errorBody);

        } catch (\Exception $e) {
            Log::error("Error in {$this->serviceName} generateSimpleContent", [
                'message' => $e->getMessage(),
                'system_prompt' => substr($systemPrompt, 0, 100) . '...',
                'user_prompt' => substr($userPrompt, 0, 100) . '...',
            ]);

            throw $e;
        }
    }
}