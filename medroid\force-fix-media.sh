#!/bin/bash

# FORCE FIX MEDIA LOADING - Comprehensive Solution

echo "🔥 FORCE FIXING MEDIA LOADING ISSUE..."

# Step 1: Kill everything and start fresh
echo "🧹 Killing all existing processes..."
pkill -f cloudflared
pkill -f "php.*serve"
pkill -f "artisan serve"
sleep 3

# Step 2: Clear all caches aggressively
echo "🔄 Clearing all caches..."
php artisan route:clear
php artisan config:clear
php artisan cache:clear
php artisan view:clear

# Step 3: Verify routes exist in our code
echo "📋 Verifying routes are properly registered..."
php artisan route:list --path=saved-posts
php artisan route:list --path=refresh-media

# Step 4: Start Laravel server
echo "🌐 Starting Laravel server..."
php artisan serve --host=0.0.0.0 --port=8000 &
LARAVEL_PID=$!
sleep 5

# Step 5: Test local routes work
echo "🧪 Testing local routes..."
LOCAL_SAVED=$(curl -o /dev/null -s -w "%{http_code}" http://localhost:8000/api/saved-posts)
LOCAL_REFRESH=$(curl -o /dev/null -s -w "%{http_code}" -X POST http://localhost:8000/api/web-api/instagram/refresh-media -H "Content-Type: application/json" -d '{"post_id":"test"}')

if [ "$LOCAL_SAVED" != "302" ] || [ "$LOCAL_REFRESH" != "302" ]; then
    echo "❌ LOCAL ROUTES BROKEN!"
    echo "   saved-posts: $LOCAL_SAVED (should be 302)"
    echo "   refresh-media: $LOCAL_REFRESH (should be 302)"
    exit 1
fi

echo "✅ Local routes working: saved-posts=$LOCAL_SAVED, refresh-media=$LOCAL_REFRESH"

# Step 6: Force new tunnel connection
echo "🔗 Starting fresh tunnel connection..."
cloudflared tunnel --hostname api.medroid.ai --url http://localhost:8000 &
TUNNEL_PID=$!
sleep 10

# Step 7: Test tunnel routes multiple times with retry
echo "🔍 Testing tunnel routes (with retries)..."
for i in {1..5}; do
    echo "   Attempt $i/5..."
    
    TUNNEL_SAVED=$(curl -o /dev/null -s -w "%{http_code}" https://api.medroid.ai/api/saved-posts)
    TUNNEL_REFRESH=$(curl -o /dev/null -s -w "%{http_code}" -X POST https://api.medroid.ai/api/web-api/instagram/refresh-media -H "Content-Type: application/json" -d '{"post_id":"test"}')
    
    echo "     saved-posts: $TUNNEL_SAVED | refresh-media: $TUNNEL_REFRESH"
    
    if [ "$TUNNEL_SAVED" == "302" ] && [ "$TUNNEL_REFRESH" == "302" ]; then
        echo "🎉 SUCCESS! Both routes working through tunnel!"
        break
    fi
    
    if [ $i -lt 5 ]; then
        echo "     Waiting 10 seconds before retry..."
        sleep 10
    fi
done

# Final status
echo ""
echo "🏁 FINAL STATUS:"
echo "   Local saved-posts: $LOCAL_SAVED"
echo "   Local refresh-media: $LOCAL_REFRESH"
echo "   Tunnel saved-posts: $TUNNEL_SAVED"
echo "   Tunnel refresh-media: $TUNNEL_REFRESH"

if [ "$TUNNEL_SAVED" == "302" ] && [ "$TUNNEL_REFRESH" == "302" ]; then
    echo ""
    echo "🎉 MEDIA LOADING ISSUE FIXED!"
    echo "   Both routes are now accessible via https://api.medroid.ai"
    echo "   You can now restart your Flutter app and test"
    echo ""
    echo "📊 Services running:"
    echo "   Laravel PID: $LARAVEL_PID"
    echo "   Tunnel PID: $TUNNEL_PID"
    echo ""
    echo "Press Ctrl+C to stop both services"
    
    # Keep running
    trap 'kill $LARAVEL_PID $TUNNEL_PID 2>/dev/null; exit 0' SIGINT SIGTERM
    wait
else
    echo ""
    echo "❌ TUNNEL STILL NOT WORKING PROPERLY"
    echo "💡 Recommend using local development instead:"
    echo "   ./start-local-dev.sh"
    kill $LARAVEL_PID $TUNNEL_PID 2>/dev/null
    exit 1
fi