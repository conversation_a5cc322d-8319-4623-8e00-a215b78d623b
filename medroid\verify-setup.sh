#!/bin/bash

# 🏥 Medroid.ai - Setup Verification Script
# This script verifies that your development environment is properly configured

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🏥 Medroid.ai - Development Environment Verification${NC}"
echo -e "${BLUE}🔍 Checking your setup...${NC}\n"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check service status
check_service() {
    local service_name=$1
    local port=$2
    local process_name=$3
    
    if lsof -i :$port >/dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name is running on port $port${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name is not running on port $port${NC}"
        return 1
    fi
}

# Check prerequisites
echo -e "${BLUE}📦 Checking Prerequisites...${NC}"

if command_exists brew; then
    echo -e "${GREEN}✅ Homebrew installed${NC}"
else
    echo -e "${RED}❌ Homebrew not found${NC}"
fi

if command_exists node; then
    echo -e "${GREEN}✅ Node.js installed ($(node --version))${NC}"
else
    echo -e "${RED}❌ Node.js not found${NC}"
fi

if command_exists php; then
    echo -e "${GREEN}✅ PHP installed ($(php --version | head -n1 | cut -d' ' -f2))${NC}"
else
    echo -e "${RED}❌ PHP not found${NC}"
fi

if command_exists composer; then
    echo -e "${GREEN}✅ Composer installed${NC}"
else
    echo -e "${RED}❌ Composer not found${NC}"
fi

if command_exists git; then
    echo -e "${GREEN}✅ Git installed${NC}"
else
    echo -e "${RED}❌ Git not found${NC}"
fi

if command_exists cloudflared; then
    echo -e "${GREEN}✅ Cloudflared installed ($(cloudflared --version))${NC}"
else
    echo -e "${RED}❌ Cloudflared not found${NC}"
fi

# Check project files
echo -e "\n${BLUE}📁 Checking Project Files...${NC}"

if [ -f "package.json" ]; then
    echo -e "${GREEN}✅ package.json found${NC}"
else
    echo -e "${RED}❌ package.json not found${NC}"
fi

if [ -f "composer.json" ]; then
    echo -e "${GREEN}✅ composer.json found${NC}"
else
    echo -e "${RED}❌ composer.json not found${NC}"
fi

if [ -f ".env" ]; then
    echo -e "${GREEN}✅ .env file found${NC}"
else
    echo -e "${RED}❌ .env file not found${NC}"
fi

if [ -d "node_modules" ]; then
    echo -e "${GREEN}✅ Node modules installed${NC}"
else
    echo -e "${RED}❌ Node modules not found - run: npm install${NC}"
fi

if [ -d "vendor" ]; then
    echo -e "${GREEN}✅ Composer dependencies installed${NC}"
else
    echo -e "${RED}❌ Composer dependencies not found - run: composer install${NC}"
fi

# Check Cloudflare configuration
echo -e "\n${BLUE}🌐 Checking Cloudflare Configuration...${NC}"

if [ -f "$HOME/.cloudflared/cert.pem" ]; then
    echo -e "${GREEN}✅ Cloudflare authentication found${NC}"
else
    echo -e "${RED}❌ Cloudflare authentication not found - run: cloudflared tunnel login${NC}"
fi

if [ -f "$HOME/.cloudflared/config.yml" ]; then
    echo -e "${GREEN}✅ Cloudflare tunnel config found${NC}"
else
    echo -e "${RED}❌ Cloudflare tunnel config not found${NC}"
fi

# Check if tunnel exists
if command_exists cloudflared && cloudflared tunnel list | grep -q "medroid-backend"; then
    echo -e "${GREEN}✅ Medroid tunnel exists${NC}"
else
    echo -e "${RED}❌ Medroid tunnel not found - run: cloudflared tunnel create medroid-backend${NC}"
fi

# Check running services
echo -e "\n${BLUE}🚀 Checking Running Services...${NC}"

check_service "Laravel Server" 8000 "php artisan serve"
check_service "Vite Dev Server" 5174 "vite"

# Check tunnel status
if pgrep -f "cloudflared tunnel run" >/dev/null; then
    echo -e "${GREEN}✅ Cloudflare tunnel is running${NC}"
else
    echo -e "${YELLOW}⚠️  Cloudflare tunnel is not running${NC}"
fi

# Test connectivity
echo -e "\n${BLUE}🧪 Testing Connectivity...${NC}"

if curl -s -I https://api.medroid.ai | grep -q "200 OK"; then
    echo -e "${GREEN}✅ api.medroid.ai is accessible${NC}"
else
    echo -e "${RED}❌ api.medroid.ai is not accessible${NC}"
fi

if curl -s -I https://api.medroid.ai/sanctum/csrf-cookie | grep -q "204"; then
    echo -e "${GREEN}✅ CSRF endpoint working${NC}"
else
    echo -e "${YELLOW}⚠️  CSRF endpoint not responding correctly${NC}"
fi

# Check environment configuration
echo -e "\n${BLUE}⚙️  Checking Environment Configuration...${NC}"

if [ -f ".env" ]; then
    if grep -q "APP_URL=https://api.medroid.ai" .env; then
        echo -e "${GREEN}✅ APP_URL configured correctly${NC}"
    else
        echo -e "${RED}❌ APP_URL not configured for api.medroid.ai${NC}"
    fi
    
    if grep -q "SESSION_DOMAIN=api.medroid.ai" .env; then
        echo -e "${GREEN}✅ SESSION_DOMAIN configured correctly${NC}"
    else
        echo -e "${RED}❌ SESSION_DOMAIN not configured for api.medroid.ai${NC}"
    fi
fi

# Summary
echo -e "\n${PURPLE}📋 Setup Verification Summary${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Count successful checks
total_checks=0
passed_checks=0

# This is a simplified summary - in a real implementation, 
# you'd track each check result and provide accurate counts

echo -e "\n${GREEN}🎯 Next Steps:${NC}"
echo -e "1. If any items are marked ❌, follow the suggested fixes"
echo -e "2. Start your development environment:"
echo -e "   ${CYAN}Terminal 1:${NC} cloudflared tunnel run medroid-backend"
echo -e "   ${CYAN}Terminal 2:${NC} php artisan serve --port=8000"
echo -e "   ${CYAN}Terminal 3:${NC} npm run dev"
echo -e "3. Visit: ${CYAN}https://api.medroid.ai${NC}"

echo -e "\n${YELLOW}💡 For help, check: NEW_MAC_SETUP_GUIDE.md${NC}"
echo -e "${YELLOW}📋 Quick commands: QUICK_REFERENCE.md${NC}"

echo -e "\n${GREEN}✨ Happy coding! ✨${NC}"
