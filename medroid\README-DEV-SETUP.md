# Medroid Development Setup

This document explains how to start the Laravel backend with Cloudflare tunnel for development.

## Quick Start

### Option 1: Use the startup script (Recommended)
```bash
# From the backend directory
./start-with-tunnel.sh
```

### Option 2: Manual startup
```bash
# Clear caches
php artisan route:clear
php artisan config:clear

# Start Laravel server
php artisan serve --host=0.0.0.0 --port=8000 &

# Start Cloudflare tunnel
cloudflared tunnel --config /Users/<USER>/.cloudflared/config.yml run &
```

## URLs

- **Local Development:** http://localhost:8000
- **Public (via tunnel):** https://api.medroid.ai

## Available Routes

### Instagram Routes
- `POST /api/web-api/instagram/refresh-media` - Refresh expired Instagram media URLs
- `GET /api/web-api/instagram/auth-url` - Get Instagram authorization URL
- `GET /api/web-api/instagram/account-status` - Get Instagram account status
- `POST /api/web-api/instagram/sync` - Sync Instagram content

### Social Feed Routes
- `GET /api/feed` - Get social feed
- `GET /api/saved-posts` - Get saved posts
- `POST /api/feed/create` - Create a new post
- `POST /api/feed/save/{contentId}` - Save a post
- `POST /api/feed/like/{contentId}` - Like a post

## Troubleshooting

### Tunnel Issues
If the tunnel is not working:
1. Kill existing processes: `pkill cloudflared`
2. Restart the script: `./start-with-tunnel.sh`
3. Check tunnel logs in the terminal output

### Route Not Found (404)
If you get 404 errors:
1. Clear Laravel caches: `php artisan route:clear && php artisan config:clear`
2. Restart both services
3. Test locally first: `curl http://localhost:8000/api/[route]`

### Development vs Production
- The tunnel connects your local Laravel server to the public domain
- Changes made locally are immediately available via https://api.medroid.ai
- No deployment needed for development testing

## Scripts

- `start-with-tunnel.sh` - Start Laravel + tunnel together
- `start-tunnel.sh` - Start tunnel only (in project root)
- `start-dev-server.sh` - Comprehensive startup script (in project root)

## Process Management

To stop all services:
```bash
pkill -f "php.*serve"
pkill -f cloudflared
```

Or use Ctrl+C if running the startup script in foreground.