import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/notification_service.dart';
import 'package:medroid_app/services/call_navigation_service.dart';
import 'package:medroid_app/screens/splash_screen.dart';

/// Wrapper widget to initialize notifications and setup callbacks
class NotificationWrapper extends StatefulWidget {
  final Widget child;

  const NotificationWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<NotificationWrapper> createState() => _NotificationWrapperState();
}

class _NotificationWrapperState extends State<NotificationWrapper> {
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
  }

  Future<void> _initializeNotifications() async {
    try {
      final notificationService = RepositoryProvider.of<NotificationService>(context);
      
      // Initialize notification service
      await notificationService.initialize();

      // Setup callbacks for video call notifications
      notificationService.onVideoCallReceived = (callData) {
        debugPrint('📞 Video call received in wrapper: $callData');
        // Show incoming call screen
        CallNavigationService.showIncomingCall(callData);
      };

      // Setup callbacks for notification taps
      notificationService.onNotificationTapped = (data) {
        debugPrint('🎯 Notification tapped in wrapper: $data');
        // Handle navigation based on notification data
        CallNavigationService.handleNotificationNavigation(data);
      };

      setState(() {
        _initialized = true;
      });

      debugPrint('✅ Notification wrapper initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing notification wrapper: $e');
      // Continue even if notifications fail
      setState(() {
        _initialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_initialized) {
      // Show splash screen while initializing
      return const SplashScreen();
    }

    return widget.child;
  }
}