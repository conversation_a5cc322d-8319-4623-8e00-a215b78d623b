import{r as p,B as ae,o as I,d,e as n,i as e,j as H,l as w,v as V,q as S,F as M,p as z,t as u,s as Q,n as C,x as k,a as _,c as E,N as G,A as U,Q as j,y as L,g as de,w as ue}from"./vendor-BhKTHoN5.js";import{_ as ce}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const ge={name:"FileEditModal",props:{file:{type:Object,required:!0},categories:{type:Array,required:!0}},emits:["close","updated"],setup(c,{emit:t}){const g=p(!1),s=ae({name:"",category:"",description:"",is_public:!1}),v=async()=>{var f,b;g.value=!0;try{const o=await _.put(`/web-api/files/${c.file.id}`,s);t("updated",o.data.file)}catch(o){console.error("Error updating file:",o),(b=(f=o.response)==null?void 0:f.data)!=null&&b.message&&alert(o.response.data.message)}finally{g.value=!1}},h=f=>{if(f===0)return"0 Bytes";const b=1024,o=["Bytes","KB","MB","GB","TB"],x=Math.floor(Math.log(f)/Math.log(b));return parseFloat((f/Math.pow(b,x)).toFixed(2))+" "+o[x]},r=f=>new Date(f).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return I(()=>{s.name=c.file.name,s.category=c.file.category,s.description=c.file.description||"",s.is_public=c.file.is_public}),{form:s,updating:g,updateFile:v,formatFileSize:h,formatDate:r}}},me={class:"mt-3"},fe={class:"flex items-center justify-between mb-4"},pe={class:"mb-4 text-center"},ye=["src","alt"],xe={key:1,class:"w-full h-32 bg-gray-100 rounded flex items-center justify-center"},ve={class:"mb-4"},be={class:"mb-4"},he=["value"],we={class:"mb-4"},_e={class:"mb-6"},ke={class:"flex items-center"},Fe={class:"mb-6 p-3 bg-gray-50 rounded"},Ce={class:"text-sm text-gray-600 space-y-1"},Me={key:0},ze={class:"flex justify-end space-x-3"},Be=["disabled"];function Se(c,t,g,s,v,h){return n(),d("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[8]||(t[8]=r=>c.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",onClick:t[7]||(t[7]=H(()=>{},["stop"]))},[e("div",me,[e("div",fe,[t[10]||(t[10]=e("h3",{class:"text-lg font-medium text-gray-900"},"Edit File",-1)),e("button",{onClick:t[0]||(t[0]=r=>c.$emit("close")),class:"text-gray-400 hover:text-gray-600"},t[9]||(t[9]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",pe,[g.file.is_image?(n(),d("img",{key:0,src:g.file.url,alt:g.file.name,class:"max-w-full h-32 object-cover rounded mx-auto"},null,8,ye)):(n(),d("div",xe,t[11]||(t[11]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z","clip-rule":"evenodd"})],-1)])))]),e("form",{onSubmit:t[6]||(t[6]=H((...r)=>s.updateFile&&s.updateFile(...r),["prevent"]))},[e("div",ve,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"File Name",-1)),w(e("input",{"onUpdate:modelValue":t[1]||(t[1]=r=>s.form.name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[V,s.form.name]])]),e("div",be,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Category",-1)),w(e("select",{"onUpdate:modelValue":t[2]||(t[2]=r=>s.form.category=r),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},[(n(!0),d(M,null,z(g.categories,r=>(n(),d("option",{key:r.value,value:r.value},u(r.label),9,he))),128))],512),[[S,s.form.category]])]),e("div",we,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Description",-1)),w(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=r=>s.form.description=r),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Optional description..."},null,512),[[V,s.form.description]])]),e("div",_e,[e("label",ke,[w(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>s.form.is_public=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[Q,s.form.is_public]]),t[15]||(t[15]=e("span",{class:"ml-2 text-sm text-gray-700"},"Make this file publicly accessible",-1))])]),e("div",Fe,[e("div",Ce,[e("div",null,[t[16]||(t[16]=e("strong",null,"Original Name:",-1)),k(" "+u(g.file.original_name),1)]),e("div",null,[t[17]||(t[17]=e("strong",null,"Size:",-1)),k(" "+u(s.formatFileSize(g.file.size)),1)]),e("div",null,[t[18]||(t[18]=e("strong",null,"Type:",-1)),k(" "+u(g.file.mime_type),1)]),e("div",null,[t[19]||(t[19]=e("strong",null,"Created:",-1)),k(" "+u(s.formatDate(g.file.created_at)),1)]),g.file.download_count>0?(n(),d("div",Me,[t[20]||(t[20]=e("strong",null,"Downloads:",-1)),k(" "+u(g.file.download_count),1)])):C("",!0)])]),e("div",ze,[e("button",{type:"button",onClick:t[5]||(t[5]=r=>c.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"}," Cancel "),e("button",{type:"submit",disabled:s.updating,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},u(s.updating?"Updating...":"Update File"),9,Be)])],32)])])])}const Ue=P(ge,[["render",Se]]),Ae={name:"UploadProgressModal",props:{progress:{type:Number,required:!0},files:{type:Array,required:!0}},emits:["close"],setup(c){return{allCompleted:E(()=>c.files.every(s=>s.status==="completed")),formatFileSize:s=>{if(s===0)return"0 Bytes";const v=1024,h=["Bytes","KB","MB","GB","TB"],r=Math.floor(Math.log(s)/Math.log(v));return parseFloat((s/Math.pow(v,r)).toFixed(2))+" "+h[r]}}}},De={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},je={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},Le={class:"mt-3"},Ve={class:"flex items-center justify-between mb-4"},Ee={class:"text-sm text-gray-500"},Pe={class:"mb-6"},Te={class:"flex justify-between text-sm text-gray-600 mb-1"},Oe={class:"w-full bg-gray-200 rounded-full h-2"},Ne={class:"space-y-3 max-h-64 overflow-y-auto"},qe={class:"flex items-center justify-between mb-2"},He={class:"flex-1 min-w-0"},Ge={class:"text-sm font-medium text-gray-900 truncate"},Ie={class:"text-xs text-gray-500"},Qe={class:"ml-2 flex items-center"},Re={key:0,class:"text-xs text-blue-600"},Ke={key:1,class:"text-xs text-green-600 flex items-center"},Je={key:2,class:"text-xs text-red-600"},We={class:"w-full bg-gray-200 rounded-full h-1"},Xe={class:"mt-6 flex justify-end"},Ye={key:1,class:"flex items-center text-sm text-gray-600"};function Ze(c,t,g,s,v,h){return n(),d("div",De,[e("div",je,[e("div",Le,[e("div",Ve,[t[1]||(t[1]=e("h3",{class:"text-lg font-medium text-gray-900"},"Uploading Files",-1)),e("div",Ee,u(g.progress)+"%",1)]),e("div",Pe,[e("div",Te,[t[2]||(t[2]=e("span",null,"Overall Progress",-1)),e("span",null,u(g.progress)+"%",1)]),e("div",Oe,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:G({width:g.progress+"%"})},null,4)])]),e("div",Ne,[(n(!0),d(M,null,z(g.files,(r,f)=>(n(),d("div",{key:f,class:"border border-gray-200 rounded p-3"},[e("div",qe,[e("div",He,[e("div",Ge,u(r.name),1),e("div",Ie,u(s.formatFileSize(r.size)),1)]),e("div",Qe,[r.status==="uploading"?(n(),d("div",Re,u(r.progress)+"%",1)):r.status==="completed"?(n(),d("div",Ke,t[3]||(t[3]=[e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1),k(" Complete ")]))):(n(),d("div",Je,"Error"))])]),e("div",We,[e("div",{class:U(["h-1 rounded-full transition-all duration-300",{"bg-blue-600":r.status==="uploading","bg-green-600":r.status==="completed","bg-red-600":r.status==="error"}]),style:G({width:r.progress+"%"})},null,6)])]))),128))]),e("div",Xe,[s.allCompleted?(n(),d("button",{key:0,onClick:t[0]||(t[0]=r=>c.$emit("close")),class:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"}," Done ")):(n(),d("div",Ye,t[4]||(t[4]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"},null,-1),k(" Uploading... ")])))])])])])}const $e=P(Ae,[["render",Ze]]),et={name:"FileManager",components:{AppLayout:ce,FileEditModal:Ue,UploadProgressModal:$e},setup(){const c=p({data:[],total:0,current_page:1,last_page:1}),t=p([]),g=p(null),s=p(!1),v=p(!1),h=p(0),r=p([]),f=p(""),b=p("all"),o=p("all"),x=p("created_at"),A=p("desc"),R=p("grid"),y=p([]),D=p(null),T=E(()=>c.value.data.length>0&&y.value.length===c.value.data.length),K=E(()=>{const l=[],i=c.value.current_page,a=c.value.last_page;i>3&&l.push(1),i>4&&l.push("...");for(let m=Math.max(1,i-2);m<=Math.min(a,i+2);m++)l.push(m);return i<a-3&&l.push("..."),i<a-2&&l.push(a),l}),F=async(l=1)=>{s.value=!0;try{const i={page:l,search:f.value,category:b.value,type:o.value,sort_by:x.value,sort_order:A.value,per_page:20},a=await _.get("/web-api/files",{params:i});c.value=a.data}catch(i){console.error("Error loading files:",i)}finally{s.value=!1}},J=async()=>{try{const l=await _.get("/web-api/files/categories");t.value=l.data}catch(l){console.error("Error loading categories:",l)}},B=async()=>{try{const l=await _.get("/web-api/files/stats");g.value=l.data}catch(l){console.error("Error loading stats:",l)}},W=async l=>{const i=Array.from(l.target.files);if(i.length!==0){v.value=!0,r.value=i.map(a=>({name:a.name,size:a.size,progress:0,status:"uploading"}));try{for(let a=0;a<i.length;a++){const m=i[a],O=new FormData;O.append("file",m),await _.post("/web-api/files",O,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:N=>{const q=Math.round(N.loaded*100/N.total);r.value[a].progress=q,h.value=Math.round((a*100+q)/i.length)}}),r.value[a].status="completed"}await Promise.all([F(),B()]),l.target.value=""}catch(a){console.error("Error uploading files:",a)}finally{setTimeout(()=>{v.value=!1,h.value=0,r.value=[]},1e3)}}},X=l=>{const i=y.value.indexOf(l.id);i>-1?y.value.splice(i,1):y.value.push(l.id)},Y=()=>{T.value?y.value=[]:y.value=c.value.data.map(l=>l.id)},Z=()=>{y.value=[]},$=async l=>{try{const i=await _.get(`/web-api/files/${l.id}/download`,{responseType:"blob"}),a=window.URL.createObjectURL(new Blob([i.data])),m=document.createElement("a");m.href=a,m.setAttribute("download",l.original_name),document.body.appendChild(m),m.click(),m.remove(),window.URL.revokeObjectURL(a)}catch(i){console.error("Error downloading file:",i)}},ee=l=>{D.value=l},te=async l=>{var i,a;if(confirm(`Are you sure you want to delete "${l.name}"?`))try{await _.delete(`/web-api/files/${l.id}`),await Promise.all([F(),B()]);const m=y.value.indexOf(l.id);m>-1&&y.value.splice(m,1)}catch(m){console.error("Error deleting file:",m),(a=(i=m.response)==null?void 0:i.data)!=null&&a.message&&alert(m.response.data.message)}},se=async()=>{var l,i;if(y.value.length!==0&&confirm(`Are you sure you want to delete ${y.value.length} file(s)?`))try{await _.post("/web-api/files/bulk-delete",{file_ids:y.value}),await Promise.all([F(),B()]),y.value=[]}catch(a){console.error("Error deleting files:",a),(i=(l=a.response)==null?void 0:l.data)!=null&&i.message&&alert(a.response.data.message)}},oe=l=>{l!=="..."&&l!==c.value.current_page&&F(l)},le=async l=>{const i=c.value.data.findIndex(a=>a.id===l.id);i>-1&&(c.value.data[i]=l),D.value=null,await B()},re=l=>{if(l===0)return"0 Bytes";const i=1024,a=["Bytes","KB","MB","GB","TB"],m=Math.floor(Math.log(l)/Math.log(i));return parseFloat((l/Math.pow(i,m)).toFixed(2))+" "+a[m]},ie=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),ne=l=>{const i=t.value.find(a=>a.value===l);return i?i.label:l};return ue([f,b,o,x,A],()=>{F(1)},{debounce:300}),I(async()=>{await Promise.all([F(),J(),B()])}),{files:c,categories:t,stats:g,loading:s,uploading:v,uploadProgress:h,uploadingFiles:r,searchQuery:f,selectedCategory:b,selectedType:o,sortBy:x,sortOrder:A,viewMode:R,selectedFiles:y,editingFile:D,allSelected:T,paginationPages:K,handleFileUpload:W,selectFile:X,toggleSelectAll:Y,clearSelection:Z,downloadFile:$,editFile:ee,deleteFile:te,bulkDelete:se,changePage:oe,handleFileUpdated:le,formatFileSize:re,formatDate:ie,getCategoryLabel:ne}}},tt={class:"py-12"},st={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},ot={class:"bg-white overflow-hidden shadow-xl sm:rounded-lg"},lt={class:"p-6 border-b border-gray-200"},rt={class:"flex justify-between items-center"},it={class:"flex space-x-4"},nt={class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center"},at={class:"flex bg-gray-100 rounded-lg p-1"},dt={class:"p-6 border-b border-gray-200 bg-gray-50"},ut={class:"flex flex-wrap gap-4 items-center"},ct={class:"flex-1 min-w-64"},gt=["value"],mt={key:0,class:"p-6 border-b border-gray-200"},ft={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},pt={class:"bg-blue-50 p-4 rounded-lg"},yt={class:"text-2xl font-bold text-blue-600"},xt={class:"bg-green-50 p-4 rounded-lg"},vt={class:"text-2xl font-bold text-green-600"},bt={class:"bg-purple-50 p-4 rounded-lg"},ht={class:"text-2xl font-bold text-purple-600"},wt={class:"bg-orange-50 p-4 rounded-lg"},_t={class:"text-2xl font-bold text-orange-600"},kt={key:1,class:"p-12 text-center"},Ft={key:2,class:"p-6"},Ct={key:0,class:"mb-4 p-4 bg-blue-50 rounded-lg flex items-center justify-between"},Mt={class:"text-blue-800"},zt={class:"space-x-2"},Bt={key:1,class:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4"},St=["onClick"],Ut={class:"text-center"},At={class:"mb-2"},Dt=["src","alt"],jt={key:1,class:"w-full h-20 bg-gray-100 rounded flex items-center justify-center"},Lt={class:"text-xs font-medium text-gray-900 truncate"},Vt={class:"text-xs text-gray-500"},Et={key:2,class:"overflow-x-auto"},Pt={class:"min-w-full divide-y divide-gray-200"},Tt={class:"bg-gray-50"},Ot={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},Nt=["checked"],qt={class:"bg-white divide-y divide-gray-200"},Ht={class:"px-6 py-4 whitespace-nowrap"},Gt=["value"],It={class:"px-6 py-4 whitespace-nowrap"},Qt={class:"flex items-center"},Rt=["src","alt"],Kt={key:1,class:"w-10 h-10 bg-gray-100 rounded mr-3 flex items-center justify-center"},Jt={class:"text-sm font-medium text-gray-900"},Wt={class:"text-sm text-gray-500"},Xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Yt={class:"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"},Zt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},es={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},ts={class:"flex space-x-2"},ss=["onClick"],os=["onClick"],ls=["onClick"],rs={key:3,class:"mt-6 flex justify-between items-center"},is={class:"text-sm text-gray-700"},ns={class:"flex space-x-2"},as=["onClick"],ds={key:3,class:"p-12 text-center"};function us(c,t,g,s,v,h){const r=j("FileEditModal"),f=j("UploadProgressModal"),b=j("AppLayout");return n(),L(b,{title:"File Manager"},{default:de(()=>[e("div",tt,[e("div",st,[e("div",ot,[e("div",lt,[e("div",rt,[t[16]||(t[16]=e("h2",{class:"text-2xl font-bold text-gray-900"},"File Manager",-1)),e("div",it,[e("label",nt,[t[14]||(t[14]=e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1)),t[15]||(t[15]=k(" Upload Files ")),e("input",{type:"file",multiple:"",onChange:t[0]||(t[0]=(...o)=>s.handleFileUpload&&s.handleFileUpload(...o)),class:"hidden",ref:"fileInput"},null,544)]),e("div",at,[e("button",{onClick:t[1]||(t[1]=o=>s.viewMode="grid"),class:U([s.viewMode==="grid"?"bg-white shadow":"","px-3 py-1 rounded text-sm"])},"Grid",2),e("button",{onClick:t[2]||(t[2]=o=>s.viewMode="list"),class:U([s.viewMode==="list"?"bg-white shadow":"","px-3 py-1 rounded text-sm"])},"List",2)])])])]),e("div",dt,[e("div",ut,[e("div",ct,[w(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>s.searchQuery=o),type:"text",placeholder:"Search files...",class:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[V,s.searchQuery]])]),w(e("select",{"onUpdate:modelValue":t[4]||(t[4]=o=>s.selectedCategory=o),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"},[t[17]||(t[17]=e("option",{value:"all"},"All Categories",-1)),(n(!0),d(M,null,z(s.categories,o=>(n(),d("option",{key:o.value,value:o.value},u(o.label),9,gt))),128))],512),[[S,s.selectedCategory]]),w(e("select",{"onUpdate:modelValue":t[5]||(t[5]=o=>s.selectedType=o),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"},t[18]||(t[18]=[e("option",{value:"all"},"All Types",-1),e("option",{value:"images"},"Images",-1),e("option",{value:"documents"},"Documents",-1),e("option",{value:"videos"},"Videos",-1)]),512),[[S,s.selectedType]]),w(e("select",{"onUpdate:modelValue":t[6]||(t[6]=o=>s.sortBy=o),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"},t[19]||(t[19]=[e("option",{value:"created_at"},"Date Created",-1),e("option",{value:"name"},"Name",-1),e("option",{value:"size"},"Size",-1)]),512),[[S,s.sortBy]]),w(e("select",{"onUpdate:modelValue":t[7]||(t[7]=o=>s.sortOrder=o),class:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"},t[20]||(t[20]=[e("option",{value:"desc"},"Newest First",-1),e("option",{value:"asc"},"Oldest First",-1)]),512),[[S,s.sortOrder]])])]),s.stats?(n(),d("div",mt,[e("div",ft,[e("div",pt,[e("div",yt,u(s.stats.total_files),1),t[21]||(t[21]=e("div",{class:"text-sm text-gray-600"},"Total Files",-1))]),e("div",xt,[e("div",vt,u(s.formatFileSize(s.stats.total_size)),1),t[22]||(t[22]=e("div",{class:"text-sm text-gray-600"},"Total Size",-1))]),e("div",bt,[e("div",ht,u(s.stats.by_type.images),1),t[23]||(t[23]=e("div",{class:"text-sm text-gray-600"},"Images",-1))]),e("div",wt,[e("div",_t,u(s.stats.by_type.documents),1),t[24]||(t[24]=e("div",{class:"text-sm text-gray-600"},"Documents",-1))])])])):C("",!0),s.loading?(n(),d("div",kt,t[25]||(t[25]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),e("p",{class:"mt-2 text-gray-600"},"Loading files...",-1)]))):s.files.data&&s.files.data.length>0?(n(),d("div",Ft,[s.selectedFiles.length>0?(n(),d("div",Ct,[e("span",Mt,u(s.selectedFiles.length)+" file(s) selected",1),e("div",zt,[e("button",{onClick:t[8]||(t[8]=(...o)=>s.bulkDelete&&s.bulkDelete(...o)),class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm"}," Delete Selected "),e("button",{onClick:t[9]||(t[9]=(...o)=>s.clearSelection&&s.clearSelection(...o)),class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm"}," Clear Selection ")])])):C("",!0),s.viewMode==="grid"?(n(),d("div",Bt,[(n(!0),d(M,null,z(s.files.data,o=>(n(),d("div",{key:o.id,class:U(["border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer",{"ring-2 ring-blue-500":s.selectedFiles.includes(o.id)}]),onClick:x=>s.selectFile(o)},[e("div",Ut,[e("div",At,[o.is_image?(n(),d("img",{key:0,src:o.url,alt:o.name,class:"w-full h-20 object-cover rounded"},null,8,Dt)):(n(),d("div",jt,t[26]||(t[26]=[e("svg",{class:"w-8 h-8 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)])))]),e("div",Lt,u(o.name),1),e("div",Vt,u(s.formatFileSize(o.size)),1)])],10,St))),128))])):(n(),d("div",Et,[e("table",Pt,[e("thead",Tt,[e("tr",null,[e("th",Ot,[e("input",{type:"checkbox",onChange:t[10]||(t[10]=(...o)=>s.toggleSelectAll&&s.toggleSelectAll(...o)),checked:s.allSelected},null,40,Nt)]),t[27]||(t[27]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Name",-1)),t[28]||(t[28]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Category",-1)),t[29]||(t[29]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Size",-1)),t[30]||(t[30]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Modified",-1)),t[31]||(t[31]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions",-1))])]),e("tbody",qt,[(n(!0),d(M,null,z(s.files.data,o=>(n(),d("tr",{key:o.id,class:"hover:bg-gray-50"},[e("td",Ht,[w(e("input",{type:"checkbox",value:o.id,"onUpdate:modelValue":t[11]||(t[11]=x=>s.selectedFiles=x)},null,8,Gt),[[Q,s.selectedFiles]])]),e("td",It,[e("div",Qt,[o.is_image?(n(),d("img",{key:0,src:o.url,alt:o.name,class:"w-10 h-10 object-cover rounded mr-3"},null,8,Rt)):(n(),d("div",Kt,t[32]||(t[32]=[e("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z","clip-rule":"evenodd"})],-1)]))),e("div",null,[e("div",Jt,u(o.name),1),e("div",Wt,u(o.mime_type),1)])])]),e("td",Xt,[e("span",Yt,u(s.getCategoryLabel(o.category)),1)]),e("td",Zt,u(s.formatFileSize(o.size)),1),e("td",$t,u(s.formatDate(o.updated_at)),1),e("td",es,[e("div",ts,[e("button",{onClick:x=>s.downloadFile(o),class:"text-blue-600 hover:text-blue-900"},"Download",8,ss),e("button",{onClick:x=>s.editFile(o),class:"text-green-600 hover:text-green-900"},"Edit",8,os),e("button",{onClick:x=>s.deleteFile(o),class:"text-red-600 hover:text-red-900"},"Delete",8,ls)])])]))),128))])])])),s.files.last_page>1?(n(),d("div",rs,[e("div",is," Showing "+u(s.files.from)+" to "+u(s.files.to)+" of "+u(s.files.total)+" results ",1),e("div",ns,[(n(!0),d(M,null,z(s.paginationPages,o=>(n(),d("button",{key:o,onClick:x=>s.changePage(o),class:U([o===s.files.current_page?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50","px-3 py-2 border border-gray-300 rounded text-sm"])},u(o),11,as))),128))])])):C("",!0)])):(n(),d("div",ds,t[33]||(t[33]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 2 0 00-2 2v14a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No files",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"Get started by uploading your first file.",-1)])))])])]),s.editingFile?(n(),L(r,{key:0,file:s.editingFile,categories:s.categories,onClose:t[12]||(t[12]=o=>s.editingFile=null),onUpdated:s.handleFileUpdated},null,8,["file","categories","onUpdated"])):C("",!0),s.uploading?(n(),L(f,{key:1,progress:s.uploadProgress,files:s.uploadingFiles,onClose:t[13]||(t[13]=o=>s.uploading=!1)},null,8,["progress","files"])):C("",!0)]),_:1})}const ys=P(et,[["render",us]]);export{ys as default};
