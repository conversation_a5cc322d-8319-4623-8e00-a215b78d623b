import{r as g,c as O,w as L,o as ee,Q as N,d as n,n as b,e as i,i as t,f as E,t as u,A as _,l as T,v as te,q as H,F as C,p as S,x as I,j as B,N as le,a as P}from"./vendor-BhKTHoN5.js";import{_ as U}from"./_plugin-vue_export-helper-DlAUqK2U.js";const oe={name:"FilePicker",props:{isOpen:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},acceptedTypes:{type:String,default:"*/*"},acceptedTypesText:{type:String,default:"All file types supported"},category:{type:String,default:"all"}},emits:["close","select"],setup(d,{emit:l}){const c=g("browse"),e=g({data:[],total:0,current_page:1,last_page:1}),w=g([]),x=g(!1),f=g(!1),o=g(""),m=g(d.category),k=g("all"),r=g([]),p=g([]),F=g(null),M=O(()=>{const s=[],a=e.value.current_page,y=e.value.last_page;a>3&&s.push(1),a>4&&s.push("...");for(let v=Math.max(1,a-2);v<=Math.min(y,a+2);v++)s.push(v);return a<y-3&&s.push("..."),a<y-2&&s.push(y),s}),h=async(s=1)=>{x.value=!0;try{const a={page:s,search:o.value,category:m.value,type:k.value,per_page:24},y=await P.get("/web-api/files",{params:a});e.value=y.data}catch(a){console.error("Error loading files:",a)}finally{x.value=!1}},D=async()=>{try{const s=await P.get("/web-api/files/categories");w.value=s.data}catch(s){console.error("Error loading categories:",s)}},Q=s=>{if(d.multiple){const a=r.value.findIndex(y=>y.id===s.id);a>-1?r.value.splice(a,1):r.value.push(s)}else r.value=[s]},G=s=>r.value.some(a=>a.id===s),K=()=>{r.value=[]},q=s=>{s!=="..."&&s!==e.value.current_page&&h(s)},J=s=>{const a=Array.from(s.target.files);V(a)},R=s=>{s.preventDefault(),f.value=!1;const a=Array.from(s.dataTransfer.files);V(a)},V=async s=>{if(s.length!==0){p.value=s.map(a=>({name:a.name,progress:0,status:"uploading"}));try{for(let a=0;a<s.length;a++){const y=s[a],v=new FormData;v.append("file",y),v.append("category",m.value!=="all"?m.value:"general");const A=await P.post("/web-api/files",v,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:j=>{const $=Math.round(j.loaded*100/j.total);p.value[a].progress=$}});p.value[a].status="completed",d.multiple?r.value.push(A.data.file):r.value=[A.data.file]}await h(),c.value="browse",setTimeout(()=>{p.value=[]},2e3)}catch(a){console.error("Error uploading files:",a)}}},W=()=>{l("select",r.value),z()},z=()=>{r.value=[],o.value="",m.value=d.category,k.value="all",c.value="browse",p.value=[],l("close")},X=s=>{if(s===0)return"0 Bytes";const a=1024,y=["Bytes","KB","MB","GB","TB"],v=Math.floor(Math.log(s)/Math.log(a));return parseFloat((s/Math.pow(a,v)).toFixed(2))+" "+y[v]};g(!1),g(null),L([o,m,k],()=>{h(1)},{debounce:300}),L(()=>d.isOpen,s=>{s&&(h(),D())});const Y=()=>{f.value=!0},Z=()=>{f.value=!1};return ee(()=>{d.isOpen&&(h(),D())}),{activeTab:c,files:e,categories:w,loading:x,isDragging:f,searchQuery:o,selectedCategory:m,selectedType:k,selectedFiles:r,uploadingFiles:p,fileInput:F,paginationPages:M,loadFiles:h,toggleFileSelection:Q,isFileSelected:G,clearSelection:K,changePage:q,handleFileSelect:J,handleDrop:R,handleDragEnter:Y,handleDragLeave:Z,confirmSelection:W,closeModal:z,formatFileSize:X}}},se={class:"mt-3"},ae={class:"flex items-center justify-between mb-4"},re={class:"text-lg font-medium text-gray-900"},ne={class:"border-b border-gray-200 mb-4"},ie={class:"-mb-px flex space-x-8"},de={key:0,class:"space-y-4"},ce={class:"flex flex-wrap gap-4 items-center bg-gray-50 p-4 rounded-lg"},ue={class:"flex-1 min-w-64"},ge=["value"],me={key:0,class:"text-center py-8"},pe={key:1,class:"space-y-4"},ye={key:0,class:"bg-blue-50 p-3 rounded-lg"},ve={class:"text-blue-800 text-sm"},fe={class:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto"},be=["onClick"],xe={class:"text-center"},he={class:"mb-2"},we=["src","alt"],ke={key:1,class:"w-full h-16 bg-gray-100 rounded flex items-center justify-center"},Fe=["title"],_e={class:"text-xs text-gray-500"},Ce={key:1,class:"flex justify-center space-x-2"},Se=["onClick","disabled"],Me={key:2,class:"text-center py-8"},Te={key:1,class:"space-y-4"},Be={class:"mt-4"},Pe={class:"cursor-pointer"},De=["multiple","accept"],Ve={class:"mt-2 text-xs text-gray-500"},ze={key:0,class:"space-y-2"},Ae={class:"flex items-center justify-between mb-1"},je={class:"text-sm text-gray-700 truncate"},Le={class:"text-xs text-gray-500"},He={class:"w-full bg-gray-200 rounded-full h-2"},Oe={class:"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200"},Ne=["disabled"];function Ee(d,l,c,e,w,x){const f=N("FilePreviewModal");return c.isOpen?(i(),n("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:l[14]||(l[14]=(...o)=>e.closeModal&&e.closeModal(...o))},[t("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white",onClick:l[13]||(l[13]=B(()=>{},["stop"]))},[t("div",se,[t("div",ae,[t("h3",re,u(c.multiple?"Select Files":"Select File"),1),t("button",{onClick:l[0]||(l[0]=(...o)=>e.closeModal&&e.closeModal(...o)),class:"text-gray-400 hover:text-gray-600"},l[15]||(l[15]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",ne,[t("nav",ie,[t("button",{onClick:l[1]||(l[1]=o=>e.activeTab="browse"),class:_([e.activeTab==="browse"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"])}," Browse Files ",2),t("button",{onClick:l[2]||(l[2]=o=>e.activeTab="upload"),class:_([e.activeTab==="upload"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"])}," Upload New ",2)])]),e.activeTab==="browse"?(i(),n("div",de,[t("div",ce,[t("div",ue,[T(t("input",{"onUpdate:modelValue":l[3]||(l[3]=o=>e.searchQuery=o),type:"text",placeholder:"Search files...",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"},null,512),[[te,e.searchQuery]])]),T(t("select",{"onUpdate:modelValue":l[4]||(l[4]=o=>e.selectedCategory=o),class:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 text-sm"},[l[16]||(l[16]=t("option",{value:"all"},"All Categories",-1)),(i(!0),n(C,null,S(e.categories,o=>(i(),n("option",{key:o.value,value:o.value},u(o.label),9,ge))),128))],512),[[H,e.selectedCategory]]),T(t("select",{"onUpdate:modelValue":l[5]||(l[5]=o=>e.selectedType=o),class:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 text-sm"},l[17]||(l[17]=[t("option",{value:"all"},"All Types",-1),t("option",{value:"images"},"Images",-1),t("option",{value:"documents"},"Documents",-1),t("option",{value:"videos"},"Videos",-1)]),512),[[H,e.selectedType]])]),e.loading?(i(),n("div",me,l[18]||(l[18]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),t("p",{class:"mt-2 text-gray-600"},"Loading files...",-1)]))):e.files.data&&e.files.data.length>0?(i(),n("div",pe,[e.selectedFiles.length>0?(i(),n("div",ye,[t("p",ve,[I(u(e.selectedFiles.length)+" file(s) selected ",1),t("button",{onClick:l[6]||(l[6]=(...o)=>e.clearSelection&&e.clearSelection(...o)),class:"ml-2 text-blue-600 hover:text-blue-800 underline"}," Clear ")])])):b("",!0),t("div",fe,[(i(!0),n(C,null,S(e.files.data,o=>(i(),n("div",{key:o.id,class:_(["border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer",{"ring-2 ring-blue-500 bg-blue-50":e.isFileSelected(o.id)}]),onClick:m=>e.toggleFileSelection(o)},[t("div",xe,[t("div",he,[o.is_image?(i(),n("img",{key:0,src:o.url,alt:o.name,class:"w-full h-16 object-cover rounded"},null,8,we)):(i(),n("div",ke,l[19]||(l[19]=[t("svg",{class:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)])))]),t("div",{class:"text-xs font-medium text-gray-900 truncate",title:o.name},u(o.name),9,Fe),t("div",_e,u(e.formatFileSize(o.size)),1)])],10,be))),128))]),e.files.last_page>1?(i(),n("div",Ce,[(i(!0),n(C,null,S(e.paginationPages,o=>(i(),n("button",{key:o,onClick:m=>e.changePage(o),class:_([o===e.files.current_page?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50","px-3 py-1 border border-gray-300 rounded text-sm"]),disabled:o==="..."},u(o),11,Se))),128))])):b("",!0)])):(i(),n("div",Me,l[20]||(l[20]=[t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})],-1),t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No files found",-1),t("p",{class:"mt-1 text-sm text-gray-500"},"Try adjusting your search or upload some files.",-1)])))])):b("",!0),e.activeTab==="upload"?(i(),n("div",Te,[t("div",{onDrop:l[8]||(l[8]=(...o)=>e.handleDrop&&e.handleDrop(...o)),onDragover:l[9]||(l[9]=B(()=>{},["prevent"])),onDragenter:l[10]||(l[10]=B(()=>{},["prevent"])),class:_(["border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors",{"border-blue-500 bg-blue-50":e.isDragging}])},[l[22]||(l[22]=t("svg",{class:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48"},[t("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),t("div",Be,[t("label",Pe,[l[21]||(l[21]=t("span",{class:"mt-2 block text-sm font-medium text-gray-900"}," Drop files here or click to browse ",-1)),t("input",{type:"file",multiple:c.multiple,accept:c.acceptedTypes,onChange:l[7]||(l[7]=(...o)=>e.handleFileSelect&&e.handleFileSelect(...o)),class:"hidden",ref:"fileInput"},null,40,De)]),t("p",Ve,u(c.acceptedTypesText),1)])],34),e.uploadingFiles.length>0?(i(),n("div",ze,[l[23]||(l[23]=t("h4",{class:"text-sm font-medium text-gray-900"},"Uploading Files",-1)),(i(!0),n(C,null,S(e.uploadingFiles,(o,m)=>(i(),n("div",{key:m,class:"bg-gray-50 p-3 rounded"},[t("div",Ae,[t("span",je,u(o.name),1),t("span",Le,u(o.progress)+"%",1)]),t("div",He,[t("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:le({width:o.progress+"%"})},null,4)])]))),128))])):b("",!0)])):b("",!0),t("div",Oe,[t("button",{onClick:l[11]||(l[11]=(...o)=>e.closeModal&&e.closeModal(...o)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"}," Cancel "),t("button",{onClick:l[12]||(l[12]=(...o)=>e.confirmSelection&&e.confirmSelection(...o)),disabled:e.selectedFiles.length===0,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"}," Select "+u(e.selectedFiles.length>0?`(${e.selectedFiles.length})`:""),9,Ne)])])]),E(f,{"is-open":d.previewModalOpen,file:d.previewFile,onClose:d.closePreviewModal,onEdit:d.editFile,onDelete:d.deleteFile,onDownload:d.downloadFile},null,8,["is-open","file","onClose","onEdit","onDelete","onDownload"])])):b("",!0)}const Ie=U(oe,[["render",Ee]]),Ue={name:"FilePickerButton",components:{FilePicker:Ie},props:{modelValue:{type:[Array,Object],default:()=>[]},multiple:{type:Boolean,default:!1},acceptedTypes:{type:String,default:"*/*"},acceptedTypesText:{type:String,default:"All file types supported"},category:{type:String,default:"all"},buttonText:{type:String,default:"Choose Files"}},emits:["update:modelValue"],setup(d,{emit:l}){const c=g(!1),e=O({get(){return d.multiple?Array.isArray(d.modelValue)?d.modelValue:[]:d.modelValue?[d.modelValue]:[]},set(r){d.multiple?l("update:modelValue",r):l("update:modelValue",r.length>0?r[0]:null)}});return{isPickerOpen:c,selectedFiles:e,openFilePicker:()=>{c.value=!0},closeFilePicker:()=>{c.value=!1},handleFileSelection:r=>{e.value=r},removeFile:r=>{const p=e.value.filter(F=>F.id!==r);e.value=p},formatFileSize:r=>{if(r===0)return"0 Bytes";const p=1024,F=["Bytes","KB","MB","GB","TB"],M=Math.floor(Math.log(r)/Math.log(p));return parseFloat((r/Math.pow(p,M)).toFixed(2))+" "+F[M]},getCategoryLabel:r=>({product_images:"Product Images",profile_images:"Profile Images",documents:"Documents",videos:"Videos",audio:"Audio",stories:"Stories",chat_attachments:"Chat Attachments",general:"General"})[r]||r}}},Qe={key:0,class:"mt-3 space-y-2"},Ge={class:"text-sm font-medium text-gray-700"},Ke={class:"space-y-2"},qe={class:"flex items-center space-x-3"},Je={class:"flex-shrink-0"},Re=["src","alt"],We={key:1,class:"w-10 h-10 bg-gray-200 rounded flex items-center justify-center"},Xe={class:"flex-1 min-w-0"},Ye={class:"text-sm font-medium text-gray-900 truncate"},Ze={class:"text-xs text-gray-500"},$e=["onClick"];function et(d,l,c,e,w,x){const f=N("FilePicker");return i(),n("div",null,[t("button",{type:"button",onClick:l[0]||(l[0]=(...o)=>e.openFilePicker&&e.openFilePicker(...o)),class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[l[1]||(l[1]=t("svg",{class:"w-5 h-5 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})],-1)),I(" "+u(c.buttonText),1)]),e.selectedFiles.length>0?(i(),n("div",Qe,[t("div",Ge," Selected "+u(c.multiple?"Files":"File")+": ",1),t("div",Ke,[(i(!0),n(C,null,S(e.selectedFiles,o=>(i(),n("div",{key:o.id,class:"flex items-center justify-between p-2 bg-gray-50 rounded border"},[t("div",qe,[t("div",Je,[o.is_image?(i(),n("img",{key:0,src:o.url,alt:o.name,class:"w-10 h-10 object-cover rounded"},null,8,Re)):(i(),n("div",We,l[2]||(l[2]=[t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z","clip-rule":"evenodd"})],-1)])))]),t("div",Xe,[t("div",Ye,u(o.name),1),t("div",Ze,u(e.formatFileSize(o.size))+" • "+u(e.getCategoryLabel(o.category)),1)])]),t("button",{onClick:m=>e.removeFile(o.id),class:"flex-shrink-0 text-red-400 hover:text-red-600"},l[3]||(l[3]=[t("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,$e)]))),128))])])):b("",!0),E(f,{"is-open":e.isPickerOpen,multiple:c.multiple,"accepted-types":c.acceptedTypes,"accepted-types-text":c.acceptedTypesText,category:c.category,onClose:e.closeFilePicker,onSelect:e.handleFileSelection},null,8,["is-open","multiple","accepted-types","accepted-types-text","category","onClose","onSelect"])])}const ot=U(Ue,[["render",et]]);export{ot as F};
