import{J as C,r as d,c as B,o as M,d as o,e as r,f as b,u as h,m as P,g as p,i as e,l as k,v as V,q as L,F as y,p as _,t as l,A as D,n as u,x as w,y as E,P as R}from"./vendor-BhKTHoN5.js";import{_ as S}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const F={class:"flex items-center justify-between"},$={class:"flex mt-2","aria-label":"Breadcrumb"},j={class:"inline-flex items-center space-x-1 md:space-x-3"},z={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},T={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},q={class:"py-12"},I={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},J={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Q={class:"p-6"},G={class:"flex flex-col sm:flex-row gap-4"},H={class:"flex-1"},K={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},O={class:"p-6 text-gray-900 dark:text-gray-100"},W={key:0,class:"text-center py-8"},X={key:1,class:"text-center py-8"},Y={key:2,class:"overflow-x-auto"},Z={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ee={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},te={class:"px-6 py-4 whitespace-nowrap"},ae={class:"flex items-center"},se={class:"ml-4"},re={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},oe={class:"text-sm text-gray-500 dark:text-gray-400"},le={class:"px-6 py-4 whitespace-nowrap"},de={class:"px-6 py-4 whitespace-nowrap"},ne={class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"},ie={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ge={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},xe=["onClick","disabled"],ce={key:1,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},be=C({__name:"Users",setup(pe){const g=[{title:"Dashboard",href:"/dashboard"},{title:"Users",href:"/users"}],x=d(!1),n=d([]),m=d(""),f=d("all"),c=d(!1),v=B(()=>n.value.filter(s=>s&&s.id)),A=async()=>{x.value=!0;try{const s=await window.axios.get("/users-list");console.log("Users API response:",s.data),n.value=s.data.data||[],console.log("Processed users:",n.value)}catch(s){console.error("Error fetching users:",s),n.value=[]}finally{x.value=!1}},U=s=>s&&{admin:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",provider:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",patient:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",manager:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"}[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",N=async s=>{if(confirm("Are you sure you want to login as this user?")){c.value=!0;try{const a=await window.axios.post(`/impersonate/${s}`);a.data.redirect_url&&(window.location.href=a.data.redirect_url)}catch(a){console.error("Error impersonating user:",a),alert("Failed to impersonate user. Please try again.")}finally{c.value=!1}}};return M(()=>{A()}),(s,a)=>(r(),o(y,null,[b(h(P),{title:"User Management"}),b(S,null,{header:p(()=>[e("div",F,[e("div",null,[a[3]||(a[3]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," User Management ",-1)),e("nav",$,[e("ol",j,[(r(),o(y,null,_(g,(t,i)=>e("li",{key:i,class:"inline-flex items-center"},[i<g.length-1?(r(),E(h(R),{key:0,href:t.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:p(()=>[w(l(t.title),1)]),_:2},1032,["href"])):(r(),o("span",z,l(t.title),1)),i<g.length-1?(r(),o("svg",T,a[2]||(a[2]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):u("",!0)])),64))])])]),a[4]||(a[4]=e("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add User ",-1))])]),default:p(()=>[e("div",q,[e("div",I,[e("div",J,[e("div",Q,[e("div",G,[e("div",H,[k(e("input",{"onUpdate:modelValue":a[0]||(a[0]=t=>m.value=t),type:"text",placeholder:"Search users...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[V,m.value]])]),e("div",null,[k(e("select",{"onUpdate:modelValue":a[1]||(a[1]=t=>f.value=t),class:"px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},a[5]||(a[5]=[e("option",{value:"all"},"All Roles",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"provider"},"Provider",-1),e("option",{value:"patient"},"Patient",-1),e("option",{value:"manager"},"Manager",-1)]),512),[[L,f.value]])])])])]),e("div",K,[e("div",O,[x.value?(r(),o("div",W,a[6]||(a[6]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):v.value.length===0?(r(),o("div",X,a[7]||(a[7]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"No users found.",-1)]))):(r(),o("div",Y,[e("table",Z,[a[11]||(a[11]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," User "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Role "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Last Login "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",ee,[(r(!0),o(y,null,_(v.value,t=>(r(),o("tr",{key:t.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",te,[e("div",ae,[a[8]||(a[8]=e("div",{class:"flex-shrink-0 h-10 w-10"},[e("div",{class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},[e("i",{class:"fas fa-user text-gray-500 dark:text-gray-400"})])],-1)),e("div",se,[e("div",re,l((t==null?void 0:t.name)||"N/A"),1),e("div",oe,l((t==null?void 0:t.email)||"N/A"),1)])])]),e("td",le,[e("span",{class:D([U(t==null?void 0:t.role),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l((t==null?void 0:t.role)||"N/A"),3)]),e("td",de,[e("span",ne,l((t==null?void 0:t.status)||"N/A"),1)]),e("td",ie,l((t==null?void 0:t.last_login)||"N/A"),1),e("td",ge,[a[10]||(a[10]=e("button",{class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"}," Edit ",-1)),(t==null?void 0:t.role)!=="admin"?(r(),o("button",{key:0,onClick:i=>N(t.id),disabled:c.value,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3 disabled:opacity-50"},a[9]||(a[9]=[e("i",{class:"fas fa-user-secret mr-1"},null,-1),w(" Login As ")]),8,xe)):u("",!0),(t==null?void 0:t.role)!=="admin"?(r(),o("button",ce," Delete ")):u("",!0)])]))),128))])])]))])])])])]),_:1})],64))}});export{be as default};
