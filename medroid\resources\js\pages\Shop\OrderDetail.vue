<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import { computed, ref, reactive, onMounted } from 'vue'
import axios from 'axios'

const props = defineProps({
    order: {
        type: Object,
        required: true
    }
})

const breadcrumbs = [
    { title: 'Shop', href: '/shop' },
    { title: 'Orders', href: '/shop/orders' },
    { title: `Order #${props.order.order_number}`, href: `/shop/orders/${props.order.order_number}` },
]

// Review functionality
const reviewForms = ref({})
const reviewableItems = ref([])
const loading = ref(false)

// Initialize review forms for each item
const initializeReviewForms = () => {
    props.order.items.forEach(item => {
        reviewForms.value[item.id] = {
            rating: 0,
            title: '',
            comment: '',
            isEditing: false
        }
    })
}

// Check if order can be reviewed (7 days after shipping)
const canReviewOrder = computed(() => {
    if (!props.order.shipped_at) return false
    const shippedDate = new Date(props.order.shipped_at)
    const sevenDaysLater = new Date(shippedDate.getTime() + (7 * 24 * 60 * 60 * 1000))
    return new Date() >= sevenDaysLater
})

// Check if reviews section should be shown
const canShowReviews = computed(() => {
    return props.order.status === 'shipped' || props.order.status === 'delivered'
})

// Check if specific item can be reviewed
const canReviewItem = (item) => {
    return canReviewOrder.value && !item.user_review
}

// Review form methods
const setRating = (itemId, rating) => {
    if (reviewForms.value[itemId]) {
        reviewForms.value[itemId].rating = rating
    }
}

const startReview = (itemId) => {
    if (reviewForms.value[itemId]) {
        reviewForms.value[itemId].isEditing = true
    }
}

const cancelReview = (itemId) => {
    if (reviewForms.value[itemId]) {
        reviewForms.value[itemId] = {
            rating: 0,
            title: '',
            comment: '',
            isEditing: false
        }
    }
}

const canSubmitReview = (itemId) => {
    const form = reviewForms.value[itemId]
    return form && form.rating > 0 && form.comment.trim().length > 0
}

const submitReview = async (item) => {
    if (!canSubmitReview(item.id)) return

    loading.value = true
    try {
        const form = reviewForms.value[item.id]
        const response = await axios.post(`/api/products/${item.product_id}/reviews`, {
            rating: form.rating,
            title: form.title,
            comment: form.comment,
            order_item_id: item.id
        })

        // Update the item with the new review
        item.user_review = response.data.review

        // Reset form
        cancelReview(item.id)

        // Show success message
        alert('Review submitted successfully!')

    } catch (error) {
        console.error('Error submitting review:', error)
        alert('Failed to submit review. Please try again.')
    } finally {
        loading.value = false
    }
}

const editReview = (item) => {
    if (item.user_review) {
        reviewForms.value[item.id] = {
            rating: item.user_review.rating,
            title: item.user_review.title || '',
            comment: item.user_review.comment,
            isEditing: true
        }
    }
}

// Load existing reviews for items
const loadItemReviews = async () => {
    try {
        for (const item of props.order.items) {
            const response = await axios.get(`/api/products/${item.product_id}/reviews?user_only=true`)
            if (response.data.reviews && response.data.reviews.length > 0) {
                item.user_review = response.data.reviews[0]
            }
        }
        reviewableItems.value = props.order.items
    } catch (error) {
        console.error('Error loading reviews:', error)
        reviewableItems.value = props.order.items
    }
}

// Initialize on component mount
onMounted(() => {
    initializeReviewForms()
    loadItemReviews()
})

// Computed properties
const formattedSubtotal = computed(() => '£' + parseFloat(props.order.subtotal || 0).toFixed(2))
const formattedShipping = computed(() => 'Free')
const formattedTotal = computed(() => '£' + parseFloat(props.order.total_amount || 0).toFixed(2))

const statusColor = computed(() => {
    switch (props.order.status) {
        case 'pending': return 'bg-yellow-100 text-yellow-800'
        case 'processing': return 'bg-blue-100 text-blue-800'
        case 'shipped': return 'bg-purple-100 text-purple-800'
        case 'delivered': return 'bg-green-100 text-green-800'
        case 'cancelled': return 'bg-red-100 text-red-800'
        default: return 'bg-gray-100 text-gray-800'
    }
})

const paymentStatusColor = computed(() => {
    switch (props.order.payment_status) {
        case 'pending': return 'bg-yellow-100 text-yellow-800'
        case 'paid': return 'bg-green-100 text-green-800'
        case 'failed': return 'bg-red-100 text-red-800'
        case 'refunded': return 'bg-gray-100 text-gray-800'
        default: return 'bg-gray-100 text-gray-800'
    }
})

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}
</script>

<template>
    <Head :title="`Order #${order.order_number} - Medroid`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Order #{{ order.order_number }}</h1>
                                <p class="text-gray-600 mt-1">Placed on {{ formatDate(order.created_at) }}</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span :class="statusColor" class="px-3 py-1 rounded-full text-sm font-medium">
                                    {{ order.status.charAt(0).toUpperCase() + order.status.slice(1) }}
                                </span>
                                <span :class="paymentStatusColor" class="px-3 py-1 rounded-full text-sm font-medium">
                                    {{ order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Left Column - Order Items & Details -->
                    <div class="space-y-6">
                        <!-- Order Items -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6">Order Items</h2>

                                <div class="space-y-6">
                                    <div v-for="item in order.items" :key="item.id" class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                                        <div class="flex-shrink-0">
                                            <img
                                                :src="item.product?.primary_image || item.product?.images?.[0]?.full_url || '/images/placeholder.jpg'"
                                                :alt="item.product_name"
                                                class="w-20 h-20 object-cover rounded-lg border border-gray-200"
                                            />
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-base font-semibold text-gray-900 mb-2">{{ item.product_name }}</h3>
                                            <div class="space-y-1">
                                                <p class="text-sm text-gray-600"><span class="font-medium">SKU:</span> {{ item.product_sku }}</p>
                                                <p class="text-sm text-gray-600"><span class="font-medium">Type:</span> {{ item.product_type.charAt(0).toUpperCase() + item.product_type.slice(1) }}</p>
                                                <p class="text-sm text-gray-600"><span class="font-medium">Quantity:</span> {{ item.quantity }}</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-lg font-semibold text-gray-900">£{{ parseFloat(item.total_price).toFixed(2) }}</p>
                                            <p class="text-sm text-gray-500">£{{ parseFloat(item.unit_price).toFixed(2) }} each</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Digital Downloads -->
                                <div v-if="order.digital_downloads && order.digital_downloads.length > 0" class="mt-8 pt-6 border-t border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        Digital Downloads
                                    </h3>
                                    <div class="space-y-3">
                                        <div v-for="download in order.digital_downloads" :key="download.id" class="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
                                            <div>
                                                <p class="text-sm font-semibold text-gray-900">{{ download.file_name }}</p>
                                                <p class="text-sm text-gray-600">{{ download.file_size }} MB</p>
                                            </div>
                                            <a
                                                :href="`/download/${download.download_token}`"
                                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
                                                target="_blank"
                                            >
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                Download
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Tracking -->
                        <div v-if="order.tracking_number || order.shipping_company || order.shipped_at" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4m0 0L9 7l-2-2M7 7l2 2"></path>
                                    </svg>
                                    Order Tracking
                                </h2>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div v-if="order.shipping_company" class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                                        <div class="text-sm text-blue-600 font-medium mb-1">Shipping Carrier</div>
                                        <div class="font-semibold text-gray-900">{{ order.shipping_company }}</div>
                                    </div>
                                    <div v-if="order.tracking_number" class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                                        <div class="text-sm text-green-600 font-medium mb-1">Tracking Number</div>
                                        <div class="font-semibold text-gray-900 font-mono text-sm">{{ order.tracking_number }}</div>
                                    </div>
                                    <div v-if="order.shipped_at" class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                                        <div class="text-sm text-purple-600 font-medium mb-1">Shipped Date</div>
                                        <div class="font-semibold text-gray-900">{{ formatDate(order.shipped_at) }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Progress -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Order Progress
                                </h2>

                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="relative">
                                        <div class="flex items-center justify-between">
                                            <div class="flex flex-col items-center">
                                                <div :class="['w-4 h-4 rounded-full border-2', order.status === 'pending' || order.status === 'processing' || order.status === 'shipped' || order.status === 'delivered' ? 'bg-green-500 border-green-500' : 'bg-white border-gray-300']"></div>
                                                <span class="mt-2 text-xs text-gray-600 text-center">Order<br>Placed</span>
                                            </div>
                                            <div class="flex-1 h-0.5 mx-2" :class="order.status === 'processing' || order.status === 'shipped' || order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'"></div>
                                            <div class="flex flex-col items-center">
                                                <div :class="['w-4 h-4 rounded-full border-2', order.status === 'processing' || order.status === 'shipped' || order.status === 'delivered' ? 'bg-green-500 border-green-500' : 'bg-white border-gray-300']"></div>
                                                <span class="mt-2 text-xs text-gray-600 text-center">Processing</span>
                                            </div>
                                            <div class="flex-1 h-0.5 mx-2" :class="order.status === 'shipped' || order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'"></div>
                                            <div class="flex flex-col items-center">
                                                <div :class="['w-4 h-4 rounded-full border-2', order.status === 'shipped' || order.status === 'delivered' ? 'bg-green-500 border-green-500' : 'bg-white border-gray-300']"></div>
                                                <span class="mt-2 text-xs text-gray-600 text-center">Shipped</span>
                                            </div>
                                            <div class="flex-1 h-0.5 mx-2" :class="order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'"></div>
                                            <div class="flex flex-col items-center">
                                                <div :class="['w-4 h-4 rounded-full border-2', order.status === 'delivered' ? 'bg-green-500 border-green-500' : 'bg-white border-gray-300']"></div>
                                                <span class="mt-2 text-xs text-gray-600 text-center">Delivered</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Product Reviews -->
                        <div v-if="canShowReviews" class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <svg class="w-6 h-6 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.915a1 1 0 00.95-.69l1.519-4.674z"></path>
                                    </svg>
                                    Product Reviews
                                </h2>

                                <div class="space-y-4">
                                    <div v-for="item in reviewableItems" :key="item.id" class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-start space-x-4">
                                            <img
                                                :src="item.product?.primary_image || item.product?.images?.[0]?.full_url || '/images/placeholder.jpg'"
                                                :alt="item.product_name"
                                                class="w-16 h-16 object-cover rounded-lg border border-gray-200"
                                            />
                                            <div class="flex-1">
                                                <h3 class="font-semibold text-gray-900 mb-2">{{ item.product_name }}</h3>

                                                <!-- Show existing review if any -->
                                                <div v-if="item.user_review" class="mb-4">
                                                    <div class="flex items-center mb-2">
                                                        <div class="flex items-center">
                                                            <span v-for="star in 5" :key="star" class="text-lg">
                                                                {{ star <= item.user_review.rating ? '★' : '☆' }}
                                                            </span>
                                                        </div>
                                                        <span class="ml-2 text-sm text-gray-600">{{ item.user_review.formatted_date }}</span>
                                                    </div>
                                                    <p v-if="item.user_review.title" class="font-medium text-gray-900 mb-1">{{ item.user_review.title }}</p>
                                                    <p class="text-gray-700 text-sm">{{ item.user_review.comment }}</p>
                                                    <button
                                                        @click="editReview(item)"
                                                        class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                                                    >
                                                        Edit Review
                                                    </button>
                                                </div>

                                                <!-- Show review form if no review exists and can review -->
                                                <div v-else-if="canReviewItem(item)" class="space-y-3">
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">Rating</label>
                                                        <div class="flex items-center space-x-1">
                                                            <button
                                                                v-for="star in 5"
                                                                :key="star"
                                                                @click="setRating(item.id, star)"
                                                                class="text-2xl focus:outline-none"
                                                                :class="star <= (reviewForms[item.id]?.rating || 0) ? 'text-yellow-400' : 'text-gray-300'"
                                                            >
                                                                ★
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">Title (optional)</label>
                                                        <input
                                                            v-model="reviewForms[item.id].title"
                                                            type="text"
                                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                            placeholder="Brief summary of your review"
                                                        />
                                                    </div>

                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">Review</label>
                                                        <textarea
                                                            v-model="reviewForms[item.id].comment"
                                                            rows="3"
                                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                            placeholder="Share your experience with this product..."
                                                        ></textarea>
                                                    </div>

                                                    <div class="flex space-x-3">
                                                        <button
                                                            @click="submitReview(item)"
                                                            :disabled="!canSubmitReview(item.id)"
                                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm font-medium"
                                                        >
                                                            Submit Review
                                                        </button>
                                                        <button
                                                            @click="cancelReview(item.id)"
                                                            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm font-medium"
                                                        >
                                                            Cancel
                                                        </button>
                                                    </div>
                                                </div>

                                                <!-- Show message if can't review yet -->
                                                <div v-else-if="!canReviewOrder" class="text-sm text-gray-600 bg-yellow-50 border border-yellow-200 rounded-md p-3">
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        Reviews are available 7 days after shipping. We'll send you an email when you can review this product.
                                                    </div>
                                                </div>

                                                <!-- Show button to start review if eligible -->
                                                <div v-else class="text-center">
                                                    <button
                                                        @click="startReview(item.id)"
                                                        class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 text-sm font-medium"
                                                    >
                                                        Write a Review
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Order Summary & Details -->
                    <div class="space-y-6">
                        <!-- Order Summary -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>

                                <div class="space-y-4">
                                    <div class="flex justify-between items-center py-2">
                                        <span class="text-gray-600 font-medium">Subtotal</span>
                                        <span class="text-gray-900 font-semibold">{{ formattedSubtotal }}</span>
                                    </div>
                                    <div class="flex justify-between items-center py-2">
                                        <span class="text-gray-600 font-medium">Shipping</span>
                                        <span class="text-green-600 font-semibold">{{ formattedShipping }}</span>
                                    </div>
                                    <div class="border-t border-gray-200 pt-4">
                                        <div class="flex justify-between items-center">
                                            <span class="text-lg font-bold text-gray-900">Total</span>
                                            <span class="text-xl font-bold text-gray-900">{{ formattedTotal }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Address -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Shipping Address
                                </h2>

                                <div v-if="order.shipping_address" class="bg-gray-50 rounded-lg p-4">
                                    <div class="space-y-2">
                                        <p class="font-semibold text-gray-900 text-lg">
                                            {{ order.shipping_address.first_name }} {{ order.shipping_address.last_name }}
                                        </p>
                                        <p class="text-gray-700">{{ order.shipping_address.address_line_1 }}</p>
                                        <p v-if="order.shipping_address.address_line_2" class="text-gray-700">{{ order.shipping_address.address_line_2 }}</p>
                                        <p class="text-gray-700">{{ order.shipping_address.city }}, {{ order.shipping_address.state }} {{ order.shipping_address.postal_code }}</p>
                                        <p class="text-gray-700 font-medium">{{ order.shipping_address.country }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Billing Address -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                    Billing Address
                                </h2>

                                <div v-if="order.billing_address" class="bg-gray-50 rounded-lg p-4">
                                    <div class="space-y-2">
                                        <p class="font-semibold text-gray-900 text-lg">
                                            {{ order.billing_address.first_name }} {{ order.billing_address.last_name }}
                                        </p>
                                        <p class="text-gray-700">{{ order.billing_address.address_line_1 }}</p>
                                        <p v-if="order.billing_address.address_line_2" class="text-gray-700">{{ order.billing_address.address_line_2 }}</p>
                                        <p class="text-gray-700">{{ order.billing_address.city }}, {{ order.billing_address.state }} {{ order.billing_address.postal_code }}</p>
                                        <p class="text-gray-700 font-medium">{{ order.billing_address.country }}</p>
                                        <div v-if="order.billing_address.email || order.billing_address.phone" class="pt-2 mt-3 border-t border-gray-200">
                                            <p v-if="order.billing_address.email" class="text-gray-700">{{ order.billing_address.email }}</p>
                                            <p v-if="order.billing_address.phone" class="text-gray-700">{{ order.billing_address.phone }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>

                                <div class="space-y-4">
                                    <Link
                                        href="/shop/orders"
                                        class="w-full inline-flex justify-center items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                                    >
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                        </svg>
                                        Back to Orders
                                    </Link>

                                    <Link
                                        href="/shop"
                                        class="w-full inline-flex justify-center items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium shadow-sm"
                                    >
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                        </svg>
                                        Continue Shopping
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
