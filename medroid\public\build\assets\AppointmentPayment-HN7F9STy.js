import{r as f,o as K,w as Q,C as X,d as o,e as l,f as U,u as Z,m as ee,g as te,i as e,n as h,x as p,t as a,l as L,s as se,j as ae,v as ne,F as oe,a as N,W as j}from"./vendor-BhKTHoN5.js";import{_ as le}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const re={class:"max-w-2xl mx-auto py-8 px-4"},ie={class:"bg-white rounded-lg shadow-md p-6 mb-6"},de={class:"border-l-4 border-blue-500 pl-4 mb-6"},ue={class:"space-y-2 text-sm text-gray-600"},ce={class:"mt-1"},me={key:0,class:"text-gray-600"},pe={key:1,class:"text-gray-600"},ve={class:"whitespace-pre-wrap"},fe={key:0},ye={key:0,class:"bg-blue-50 rounded-lg p-4 mb-4"},ge={class:"space-y-3"},xe={class:"flex justify-between items-center"},be={class:"text-lg font-semibold text-green-600"},he={class:"flex items-center space-x-3"},we={key:0,class:"space-y-2"},_e={for:"credit_amount",class:"block text-sm font-medium text-gray-700"},ke=["value","max"],Ce={class:"bg-gray-50 rounded-lg p-4"},Pe={key:0,class:"space-y-2 mb-3"},Fe={class:"flex justify-between items-center text-sm"},Me={class:"text-gray-900"},Ae={class:"flex justify-between items-center text-sm"},ze={class:"text-green-600"},$e={class:"flex justify-between items-center"},Se={class:"text-lg font-semibold text-gray-900"},je={class:"text-2xl font-bold text-green-600"},Be={key:1,class:"mt-2 text-xs text-gray-500"},Re={key:2,class:"mt-2 text-xs text-red-500"},Ue={class:"bg-white rounded-lg shadow-md p-6"},Le={key:0,class:"bg-red-50 border border-red-200 rounded-md p-4 mb-4"},Ne={class:"flex"},Ve={class:"ml-3"},De={class:"text-sm text-red-800"},Te={key:1,class:"bg-green-50 border border-green-200 rounded-md p-4 mb-4"},Ie={class:"flex"},Ge={class:"ml-3"},Ee={class:"text-sm text-green-800"},He={key:0},Oe={class:"mb-4"},We={key:1,class:"bg-green-50 border border-green-200 rounded-md p-4"},Ye={class:"flex space-x-4 pt-6"},qe=["disabled"],Je={key:0,class:"flex items-center justify-center"},Ke={key:1},st={__name:"AppointmentPayment",props:{appointment:Object,stripe_public_key:String,user_credit:Object},setup(n){const r=n,y=f(!1),i=f(""),w=f("");let P=null,B=null,_=null;const F=f(""),c=f(!1),d=f(0),g=f(0),V=()=>{if(!r.user_credit||!r.appointment)return 0;const s=parseFloat(r.user_credit.balance)||0,t=parseFloat(r.appointment.amount)||0;return Math.min(s,t)},b=f(0),k=()=>{var u;const s=((u=r.appointment)==null?void 0:u.amount)||0,t=c.value?d.value:0;b.value=Math.max(0,s-t)},D=[{name:"Appointments",href:"/appointments"},{name:"Payment",href:"#"}],T=s=>new Date(s).toLocaleDateString("en-GB",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),I=s=>typeof s=="object"&&s.start_time?`${s.start_time} - ${s.end_time}`:s;K(async()=>{g.value=V(),d.value=g.value,k(),await H()}),Q([c,d],()=>{k()});const G=()=>{c.value?d.value=g.value:d.value=0,k()},E=s=>{const t=parseFloat(s.target.value)||0;d.value=Math.min(Math.max(0,t),g.value),k()},H=async()=>{try{if(!window.Stripe){const s=document.createElement("script");s.src="https://js.stripe.com/v3/",s.async=!0,document.head.appendChild(s),await new Promise((t,u)=>{s.onload=t,s.onerror=u})}P=window.Stripe(r.stripe_public_key,{locale:"en-GB"}),B=P.elements({locale:"en-GB"}),_=B.create("card",{style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"}},invalid:{color:"#9e2146"}}}),await X(),_.mount("#card-element"),_.on("change",({error:s})=>{s?i.value=s.message:i.value=""})}catch(s){console.error("Error initializing Stripe:",s),i.value="Failed to initialize payment system. Please refresh the page."}},O=async()=>{var s,t,u,x;if(W()){y.value=!0,i.value="",w.value="";try{if(b.value<=0){const v=await N.post("/confirm-elements-payment",{payment_intent_id:r.appointment.payment_intent_id,appointment_id:r.appointment.id,use_credits:!0,credit_amount:d.value});if(v.data.success){let M="Payment completed using credits!";v.data.credit_applied&&v.data.credit_applied>0&&(M+=` $${(parseFloat(v.data.credit_applied)||0).toFixed(2)} credit was applied.`),M+=" Redirecting back to chat...",w.value=M,setTimeout(()=>{const A=new URLSearchParams(window.location.search).get("conversation"),J=A?`/chat?payment_success=true&appointment_id=${r.appointment.id}&conversation=${A}`:`/chat?payment_success=true&appointment_id=${r.appointment.id}`;j.visit(J,{onSuccess:()=>{console.log("Redirected to chat after successful credit payment")}})},2e3)}else i.value=v.data.message||"Credit payment failed. Please try again.";y.value=!1;return}if(!P||!_){i.value="Payment system not ready. Please refresh the page.",y.value=!1;return}if(!r.appointment.client_secret){i.value="Payment information not found. Please refresh the page and try again.",y.value=!1;return}const{error:m,paymentIntent:$}=await P.confirmCardPayment(r.appointment.client_secret,{payment_method:{card:_,billing_details:{name:F.value}}});if(m){i.value=m.message,y.value=!1;return}const C=await N.post("/confirm-elements-payment",{payment_intent_id:$.id,appointment_id:r.appointment.id,use_credits:c.value,credit_amount:c.value?d.value:0});if(C.data.success){let v="Payment processed successfully!";C.data.credit_applied&&C.data.credit_applied>0&&(v+=` $${(parseFloat(C.data.credit_applied)||0).toFixed(2)} credit was applied.`),v+=" Redirecting back to chat...",w.value=v,setTimeout(()=>{const S=new URLSearchParams(window.location.search).get("conversation"),A=S?`/chat?payment_success=true&appointment_id=${r.appointment.id}&conversation=${S}`:`/chat?payment_success=true&appointment_id=${r.appointment.id}`;j.visit(A,{onSuccess:()=>{console.log("Redirected to chat after successful payment")}})},2e3)}else i.value=C.data.message||"Payment failed. Please try again."}catch(m){if(console.error("Payment error:",m),(t=(s=m.response)==null?void 0:s.data)!=null&&t.message)i.value=m.response.data.message;else if((x=(u=m.response)==null?void 0:u.data)!=null&&x.errors){const $=Object.values(m.response.data.errors).flat();i.value=$.join(", ")}else i.value="An error occurred while processing your payment. Please try again."}finally{y.value=!1}}},W=()=>b.value<=0||F.value.trim()?!0:(i.value="Please enter the cardholder name.",!1),Y=()=>{j.visit("/appointments")},q=s=>{if(!s)return"General consultation";const t=s.replace(/\*\*/g,"").replace(/###/g,"").replace(/\n+/g," ").trim();let x=t.split(/[.!?]+/).filter(m=>m.trim().length>0).slice(0,3).join(". ");return x.length>200&&(x=t.substring(0,200)),x.length<t.length&&(x+="..."),x},z=f(!1),R=()=>{z.value=!z.value};return(s,t)=>(l(),o(oe,null,[U(Z(ee),{title:"Payment - Medroid"}),U(le,{breadcrumbs:D},{default:te(()=>[e("div",re,[e("div",ie,[t[18]||(t[18]=e("h1",{class:"text-2xl font-bold text-gray-900 mb-4"},"Complete Payment",-1)),e("div",de,[t[10]||(t[10]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-2"},"Appointment Details",-1)),e("div",ue,[e("p",null,[t[3]||(t[3]=e("span",{class:"font-medium"},"Provider:",-1)),p(" "+a(n.appointment.provider.name),1)]),e("p",null,[t[4]||(t[4]=e("span",{class:"font-medium"},"Specialization:",-1)),p(" "+a(n.appointment.provider.specialization),1)]),e("p",null,[t[5]||(t[5]=e("span",{class:"font-medium"},"Service:",-1)),p(" "+a(n.appointment.service.name),1)]),e("p",null,[t[6]||(t[6]=e("span",{class:"font-medium"},"Date:",-1)),p(" "+a(T(n.appointment.date)),1)]),e("p",null,[t[7]||(t[7]=e("span",{class:"font-medium"},"Time:",-1)),p(" "+a(I(n.appointment.time_slot)),1)]),e("div",null,[t[8]||(t[8]=e("span",{class:"font-medium"},"Reason:",-1)),e("div",ce,[z.value?(l(),o("div",pe,[e("p",ve,a(n.appointment.reason),1),e("button",{onClick:R,class:"mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"}," Show less ")])):(l(),o("p",me,[p(a(q(n.appointment.reason))+" ",1),n.appointment.reason&&n.appointment.reason.length>200?(l(),o("button",{key:0,onClick:R,class:"ml-2 text-blue-600 hover:text-blue-800 text-sm font-medium"}," See more ")):h("",!0)]))])]),n.appointment.notes?(l(),o("p",fe,[t[9]||(t[9]=e("span",{class:"font-medium"},"Notes:",-1)),p(" "+a(n.appointment.notes),1)])):h("",!0)])]),n.user_credit&&(parseFloat(n.user_credit.balance)||0)>0?(l(),o("div",ye,[t[13]||(t[13]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-3"},"Available Credits",-1)),e("div",ge,[e("div",xe,[t[11]||(t[11]=e("span",{class:"text-sm text-gray-600"},"Your Credit Balance:",-1)),e("span",be,"$"+a((parseFloat(n.user_credit.balance)||0).toFixed(2)),1)]),e("div",he,[L(e("input",{id:"use_credits","onUpdate:modelValue":t[0]||(t[0]=u=>c.value=u),onChange:G,type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,544),[[se,c.value]]),t[12]||(t[12]=e("label",{for:"use_credits",class:"text-sm font-medium text-gray-700"}," Use credits for this payment ",-1))]),c.value?(l(),o("div",we,[e("label",_e," Credit Amount to Use (Max: £"+a(g.value.toFixed(2))+") ",1),e("input",{id:"credit_amount",value:d.value.toFixed(2),onInput:E,type:"number",step:"0.01",min:0,max:g.value,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,ke),e("button",{onClick:t[1]||(t[1]=u=>{d.value=g.value,k()}),type:"button",class:"text-sm text-blue-600 hover:text-blue-800 font-medium"}," Use maximum credit (£"+a(g.value.toFixed(2))+") ",1)])):h("",!0)])])):h("",!0),e("div",Ce,[c.value&&d.value>0?(l(),o("div",Pe,[e("div",Fe,[t[14]||(t[14]=e("span",{class:"text-gray-600"},"Appointment Cost:",-1)),e("span",Me,"£"+a((parseFloat(n.appointment.amount)||0).toFixed(2)),1)]),e("div",Ae,[t[15]||(t[15]=e("span",{class:"text-gray-600"},"Credit Applied:",-1)),e("span",ze,"-£"+a(d.value.toFixed(2)),1)]),t[16]||(t[16]=e("hr",{class:"border-gray-300"},null,-1))])):h("",!0),e("div",$e,[e("span",Se,a(c.value&&d.value>0?"Amount to Pay:":"Total Amount:"),1),e("span",je,"£"+a(b.value.toFixed(2)),1)]),n.appointment.client_secret?(l(),o("div",Be,[p(" Payment Intent: "+a(n.appointment.payment_intent_id)+" ",1),t[17]||(t[17]=e("br",null,null,-1)),p("Client Secret: "+a(n.appointment.client_secret.substring(0,30))+"... ",1)])):(l(),o("div",Re," No payment information found "))])]),e("div",Ue,[t[26]||(t[26]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Payment Information",-1)),i.value?(l(),o("div",Le,[e("div",Ne,[t[19]||(t[19]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",Ve,[e("p",De,a(i.value),1)])])])):h("",!0),w.value?(l(),o("div",Te,[e("div",Ie,[t[20]||(t[20]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),e("div",Ge,[e("p",Ee,a(w.value),1)])])])):h("",!0),e("form",{onSubmit:ae(O,["prevent"]),class:"space-y-4"},[b.value>0?(l(),o("div",He,[t[22]||(t[22]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Payment Method",-1)),e("div",Oe,[t[21]||(t[21]=e("label",{for:"cardholder_name",class:"block text-sm font-medium text-gray-700 mb-1"}," Cardholder Name * ",-1)),L(e("input",{id:"cardholder_name","onUpdate:modelValue":t[2]||(t[2]=u=>F.value=u),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"John Doe"},null,512),[[ne,F.value]])]),t[23]||(t[23]=e("div",null,[e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Card Information * "),e("div",{id:"card-element",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",style:{"min-height":"40px"}})],-1))])):(l(),o("div",We,t[24]||(t[24]=[e("div",{class:"flex"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("p",{class:"text-sm text-green-800"}," Great! Your appointment cost is fully covered by your available credits. No additional payment required. ")])],-1)]))),e("div",Ye,[e("button",{type:"button",onClick:Y,class:"flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}," Cancel "),e("button",{type:"submit",disabled:y.value,class:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"},[y.value?(l(),o("span",Je,t[25]||(t[25]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),p(" Processing... ")]))):(l(),o("span",Ke,a(b.value>0?`Pay £${b.value.toFixed(2)}`:"Complete Payment"),1))],8,qe)])],32),t[27]||(t[27]=e("div",{class:"mt-6 p-4 bg-gray-50 rounded-md"},[e("div",{class:"flex"},[e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z","clip-rule":"evenodd"})])]),e("div",{class:"ml-3"},[e("p",{class:"text-sm text-gray-600"}," Your payment information is secure and encrypted. We use Stripe for payment processing. ")])])],-1))])])]),_:1})],64))}};export{st as default};
