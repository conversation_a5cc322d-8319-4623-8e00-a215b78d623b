// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKqJVJKqJVJKqJVJKqJVJKqJVJKqJ',
    appId: '1:123456789:web:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'medroid-staging',
    authDomain: 'medroid-staging.firebaseapp.com',
    storageBucket: 'medroid-staging.appspot.com',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCeaypzN1UPAp6IFdKNAMAl9fVlHjoTU_E',
    appId: '1:539973599625:android:3278ed48b28378e95ba158',
    messagingSenderId: '539973599625',
    projectId: 'medroid-app-e58be',
    storageBucket: 'medroid-app-e58be.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCeaypzN1UPAp6IFdKNAMAl9fVlHjoTU_E',
    appId: '1:539973599625:ios:3278ed48b28378e95ba158',
    messagingSenderId: '539973599625',
    projectId: 'medroid-app-e58be',
    iosBundleId: 'com.medroid.medroidapp',
    storageBucket: 'medroid-app-e58be.firebasestorage.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKqJVJKqJVJKqJVJKqJVJKqJVJKqJ',
    appId: '1:123456789:macos:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'medroid-staging',
    iosBundleId: 'com.example.medroidApp',
    storageBucket: 'medroid-staging.appspot.com',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKqJVJKqJVJKqJVJKqJVJKqJVJKqJ',
    appId: '1:123456789:web:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'medroid-staging',
    authDomain: 'medroid-staging.firebaseapp.com',
    storageBucket: 'medroid-staging.appspot.com',
    measurementId: 'G-XXXXXXXXXX',
  );
}
