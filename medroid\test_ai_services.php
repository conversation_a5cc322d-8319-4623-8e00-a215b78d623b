<?php
/**
 * AI Services Test Script for Production
 * Upload this file to your Laravel root directory and run: php test_ai_services.php
 */

echo "=== MEDROID AI SERVICES TEST ===\n";
echo "Testing AI service configuration and health...\n\n";

try {
    // Load Laravel
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

    echo "✅ Laravel loaded successfully\n\n";

    // Test 1: Check Environment Configuration
    echo "1. ENVIRONMENT CONFIGURATION\n";
    echo "============================\n";
    echo "Primary Provider: " . config('services.chat.primary_provider', 'NOT SET') . "\n";
    echo "Fallback Provider: " . config('services.chat.fallback_provider', 'NOT SET') . "\n";
    echo "AI Service Provider: " . env('AI_SERVICE_PROVIDER', 'NOT SET') . "\n";
    echo "App Environment: " . config('app.env') . "\n";
    echo "App Debug: " . (config('app.debug') ? 'true' : 'false') . "\n\n";

    // Test 2: Check API Keys
    echo "2. API KEYS STATUS\n";
    echo "==================\n";
    $nscaleKey = config('services.nscale.api_key');
    $groqKey = config('services.groq.api_key');
    $openaiKey = config('services.openai.api_key');
    
    echo "NScale API Key: " . ($nscaleKey ? '✅ Set (' . substr($nscaleKey, 0, 10) . '...)' : '❌ Not Set') . "\n";
    echo "Groq API Key: " . ($groqKey ? '✅ Set (' . substr($groqKey, 0, 10) . '...)' : '❌ Not Set') . "\n";
    echo "OpenAI API Key: " . ($openaiKey ? '✅ Set (' . substr($openaiKey, 0, 10) . '...)' : '❌ Not Set') . "\n\n";

    // Test 3: Service URLs and Models
    echo "3. SERVICE CONFIGURATION\n";
    echo "========================\n";
    echo "NScale URL: " . config('services.nscale.api_url', 'NOT SET') . "\n";
    echo "NScale Model: " . config('services.nscale.model', 'NOT SET') . "\n";
    echo "Groq URL: " . config('services.groq.api_url', 'NOT SET') . "\n";
    echo "Groq Model: " . config('services.groq.model', 'NOT SET') . "\n\n";

    // Test 4: Initialize Chat Service Manager
    echo "4. CHAT SERVICE MANAGER TEST\n";
    echo "=============================\n";
    
    try {
        $chatService = new \App\Services\ChatServiceManager();
        echo "✅ Chat Service Manager initialized successfully\n";
        
        $primaryService = $chatService->getPrimaryService();
        $fallbackService = $chatService->getFallbackService();
        
        echo "Primary Service: " . $primaryService->getServiceName() . "\n";
        echo "Fallback Service: " . $fallbackService->getServiceName() . "\n";
        
        // Test primary service health
        echo "Primary Service Health: ";
        if ($primaryService->isHealthy()) {
            echo "✅ Healthy\n";
        } else {
            echo "❌ Unhealthy\n";
        }
        
        // Test fallback service health
        echo "Fallback Service Health: ";
        if ($fallbackService->isHealthy()) {
            echo "✅ Healthy\n";
        } else {
            echo "❌ Unhealthy\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error initializing Chat Service Manager: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // Test 5: Test Individual Services
    echo "5. INDIVIDUAL SERVICE TESTS\n";
    echo "===========================\n";
    
    // Test NScale Service
    if ($nscaleKey) {
        echo "Testing NScale Service...\n";
        try {
            $nscaleService = new \App\Services\NScaleService();
            echo "✅ NScale Service created successfully\n";
            echo "Service Name: " . $nscaleService->getServiceName() . "\n";
            echo "Service Healthy: " . ($nscaleService->isHealthy() ? '✅ Yes' : '❌ No') . "\n";
        } catch (Exception $e) {
            echo "❌ NScale Service Error: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️ NScale Service: Skipped (no API key)\n";
    }
    
    echo "\n";
    
    // Test Groq Service
    if ($groqKey) {
        echo "Testing Groq Service...\n";
        try {
            $groqService = new \App\Services\GroqService();
            echo "✅ Groq Service created successfully\n";
            echo "Service Name: " . $groqService->getServiceName() . "\n";
            echo "Service Healthy: " . ($groqService->isHealthy() ? '✅ Yes' : '❌ No') . "\n";
        } catch (Exception $e) {
            echo "❌ Groq Service Error: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️ Groq Service: Skipped (no API key)\n";
    }
    
    echo "\n";

    // Test 6: Test Chat Response (Simple)
    echo "6. CHAT RESPONSE TEST\n";
    echo "=====================\n";
    
    try {
        // Create a test conversation
        $user = \App\Models\User::first();
        if (!$user) {
            echo "⚠️ No users found in database, skipping chat test\n";
        } else {
            $conversation = new \App\Models\ChatConversation();
            $conversation->user_id = $user->id;
            $conversation->title = 'Test Conversation';
            $conversation->save();
            
            // Add a test message
            $message = new \App\Models\ChatMessage();
            $message->conversation_id = $conversation->id;
            $message->role = 'user';
            $message->content = 'Hello, this is a test message';
            $message->save();
            
            echo "✅ Test conversation created\n";
            echo "Testing AI response generation...\n";
            
            $chatService = new \App\Services\ChatServiceManager();
            $response = $chatService->generateMedicalConsultation($conversation, false, 'Test context');
            
            if (is_array($response) && isset($response['message'])) {
                echo "✅ AI Response generated successfully\n";
                echo "Used Service: " . ($response['used_service'] ?? 'Unknown') . "\n";
                echo "Used Fallback: " . ($response['used_fallback'] ? 'Yes' : 'No') . "\n";
                echo "Response Length: " . strlen($response['message']) . " characters\n";
                echo "Response Preview: " . substr($response['message'], 0, 100) . "...\n";
            } else {
                echo "❌ Failed to generate AI response\n";
                echo "Response: " . print_r($response, true) . "\n";
            }
            
            // Clean up test data
            $message->delete();
            $conversation->delete();
            echo "✅ Test data cleaned up\n";
        }
    } catch (Exception $e) {
        echo "❌ Chat Response Test Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // Test 7: Check Recent Logs
    echo "7. RECENT LOG ANALYSIS\n";
    echo "======================\n";
    
    $logFile = storage_path('logs/laravel.log');
    if (file_exists($logFile)) {
        echo "✅ Log file found: " . $logFile . "\n";
        
        // Get last 50 lines and filter for service-related entries
        $logLines = array_slice(file($logFile), -50);
        $serviceLines = array_filter($logLines, function($line) {
            return stripos($line, 'service') !== false || 
                   stripos($line, 'nscale') !== false || 
                   stripos($line, 'groq') !== false ||
                   stripos($line, 'fallback') !== false;
        });
        
        if (!empty($serviceLines)) {
            echo "Recent service-related log entries:\n";
            foreach (array_slice($serviceLines, -5) as $line) {
                echo "  " . trim($line) . "\n";
            }
        } else {
            echo "⚠️ No recent service-related log entries found\n";
        }
    } else {
        echo "❌ Log file not found\n";
    }
    
    echo "\n";

    // Test 8: Summary and Recommendations
    echo "8. SUMMARY & RECOMMENDATIONS\n";
    echo "=============================\n";
    
    $primaryProvider = config('services.chat.primary_provider');
    $fallbackProvider = config('services.chat.fallback_provider');
    
    if ($primaryProvider === 'nscale' && $nscaleKey) {
        echo "✅ Primary service (NScale) is properly configured\n";
    } elseif ($primaryProvider === 'groq' && $groqKey) {
        echo "✅ Primary service (Groq) is properly configured\n";
    } else {
        echo "❌ Primary service configuration issue detected\n";
    }
    
    if ($fallbackProvider === 'groq' && $groqKey) {
        echo "✅ Fallback service (Groq) is properly configured\n";
    } elseif ($fallbackProvider === 'nscale' && $nscaleKey) {
        echo "✅ Fallback service (NScale) is properly configured\n";
    } else {
        echo "❌ Fallback service configuration issue detected\n";
    }
    
    echo "\n";
    echo "Current Setup:\n";
    echo "- Primary: " . $primaryProvider . " → " . ($primaryProvider === 'nscale' ? 'NScale' : 'Groq') . "\n";
    echo "- Fallback: " . $fallbackProvider . " → " . ($fallbackProvider === 'groq' ? 'Groq' : 'NScale') . "\n";
    echo "\n";
    echo "To monitor which service is being used in real-time:\n";
    echo "tail -f storage/logs/laravel.log | grep -E '(NScale|Groq|service)'\n";

} catch (Exception $e) {
    echo "❌ CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "Upload this script to your Laravel root directory and run: php test_ai_services.php\n";
?>
