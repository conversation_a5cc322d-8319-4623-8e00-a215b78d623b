<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StaticAssetCors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the response first
        $response = $next($request);

        // Check if this is a request from app.medroid.ai
        $origin = $request->headers->get('Origin');

        if ($origin === 'https://app.medroid.ai') {
            // Add CORS headers to the response
            $response->headers->set('Access-Control-Allow-Origin', 'https://app.medroid.ai');
            $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE');
            $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN');
            $response->headers->set('Access-Control-Allow-Credentials', 'true');
            $response->headers->set('Access-Control-Max-Age', '86400');
        }

        return $response;
    }
    
    /**
     * Check if the request is for a static asset
     */
    private function isStaticAssetRequest(Request $request): bool
    {
        $path = $request->getPathInfo();
        
        // Check if it's a build asset
        if (str_starts_with($path, '/build/assets/')) {
            return true;
        }
        
        // Check if it's a static file by extension
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        $staticExtensions = ['js', 'css', 'map', 'woff', 'woff2', 'ttf', 'eot', 'svg', 'png', 'jpg', 'jpeg', 'gif', 'ico'];
        
        return in_array(strtolower($extension), $staticExtensions);
    }
}
