#!/bin/bash

# Complete Medroid Development Environment Startup
# Starts Laravel server + Stable tunnel infrastructure

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}🚀 Starting Complete Medroid Development Environment${NC}"
echo ""

# Check if Laravel server is running
if pgrep -f "php artisan serve" > /dev/null; then
    echo -e "${GREEN}✅ Laravel server already running${NC}"
else
    echo -e "${BLUE}🔧 Starting Laravel server on port 8000...${NC}"
    php artisan serve --port=8000 &
    sleep 3
    
    if pgrep -f "php artisan serve" > /dev/null; then
        echo -e "${GREEN}✅ Laravel server started successfully${NC}"
    else
        echo -e "${RED}❌ Failed to start Laravel server${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${BLUE}🌐 Setting up stable tunnel infrastructure...${NC}"

# Start tunnel and configure app
./tunnel-connect.sh start

echo ""
echo -e "${GREEN}🎉 Development environment ready!${NC}"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Your app is now accessible via the tunnel URL above"
echo "2. Update Instagram app settings with the callback URL"
echo "3. For permanent setup, run: ./tunnel-connect.sh install-service"
echo ""
echo -e "${BLUE}💡 Useful commands:${NC}"
echo "  ./tunnel-connect.sh status    # Check tunnel status"
echo "  ./tunnel-connect.sh url       # Get current URL"  
echo "  ./tunnel-connect.sh logs      # View tunnel logs"
echo "  ./tunnel-connect.sh stop      # Stop tunnel"
echo ""