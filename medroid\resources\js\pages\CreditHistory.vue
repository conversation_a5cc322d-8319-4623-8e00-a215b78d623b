<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'

const breadcrumbs = [
    {
        title: 'Credit History',
        href: '/credit-history',
    },
]

// Reactive data
const transactions = ref([])
const loading = ref(false)
const error = ref(null)
const creditBalance = ref({
    balance: 0,
    total_earned: 0,
    total_used: 0,
    referral_earnings: 0,
    admin_credits: 0
})

// Filters
const filters = ref({
    type: '',
    source: '',
    start_date: '',
    end_date: ''
})

// Methods
const fetchCreditBalance = async () => {
    try {
        const response = await axios.get('/credits-balance')
        creditBalance.value = response.data
    } catch (error) {
        console.error('Error loading credit balance:', error)
    }
}

const fetchTransactionHistory = async () => {
    loading.value = true
    error.value = null
    
    try {
        const params = new URLSearchParams()
        if (filters.value.type) params.append('type', filters.value.type)
        if (filters.value.source) params.append('source', filters.value.source)
        if (filters.value.start_date) params.append('start_date', filters.value.start_date)
        if (filters.value.end_date) params.append('end_date', filters.value.end_date)
        
        const response = await axios.get(`/credits-transactions?${params}`)
        transactions.value = response.data.data || []
    } catch (err) {
        console.error('Error fetching transaction history:', err)
        error.value = 'Failed to load transaction history'
        transactions.value = []
    } finally {
        loading.value = false
    }
}

const applyFilters = () => {
    fetchTransactionHistory()
}

const clearFilters = () => {
    filters.value = {
        type: '',
        source: '',
        start_date: '',
        end_date: ''
    }
    fetchTransactionHistory()
}





const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const formatAmount = (amount, type) => {
    const formattedAmount = parseFloat(amount).toFixed(2)
    return type === 'earned' ? `+£${formattedAmount}` : `-£${formattedAmount}`
}

const getTransactionIcon = (type, source) => {
    if (type === 'earned') {
        if (source === 'admin') return '👨‍💼'
        if (source === 'referral') return '👥'
        return '💰'
    } else {
        if (source === 'appointment') return '🏥'
        return '💸'
    }
}

const getTransactionColor = (type) => {
    return type === 'earned' ? 'text-green-600' : 'text-red-600'
}

const getSourceLabel = (source) => {
    const labels = {
        'admin': 'Admin Credit',
        'referral': 'Referral Bonus',
        'appointment': 'Appointment Payment',
        'bonus': 'Bonus Credit',
        'promotion': 'Promotional Credit'
    }
    return labels[source] || source
}

// Computed
const filteredTransactions = computed(() => {
    return transactions.value
})

// Lifecycle
onMounted(() => {
    fetchCreditBalance()
    fetchTransactionHistory()
})
</script>

<template>
    <Head title="Credit History - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-12">
            <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Credit History</h1>
                                <p class="text-gray-600 mt-1">View your credit transactions and balance</p>
                            </div>
                            <Link
                                href="/chat"
                                class="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                                <span>Back to Chat</span>
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Credit Balance Summary -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Current Balance</p>
                                <p class="text-2xl font-bold text-gray-900">${{ creditBalance.balance.toFixed(2) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Earned</p>
                                <p class="text-2xl font-bold text-gray-900">${{ creditBalance.total_earned.toFixed(2) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-red-100 rounded-lg">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Used</p>
                                <p class="text-2xl font-bold text-gray-900">${{ creditBalance.total_used.toFixed(2) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Referral Credits</p>
                                <p class="text-2xl font-bold text-gray-900">${{ creditBalance.referral_earnings.toFixed(2) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Admin Credits</p>
                                <p class="text-2xl font-bold text-gray-900">${{ creditBalance.admin_credits.toFixed(2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Transactions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                            <select v-model="filters.type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                                <option value="">All Types</option>
                                <option value="earned">Earned</option>
                                <option value="used">Used</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Source</label>
                            <select v-model="filters.source" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                                <option value="">All Sources</option>
                                <option value="admin">Admin Credit</option>
                                <option value="referral">Referral</option>
                                <option value="appointment">Appointment</option>
                                <option value="bonus">Bonus</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                            <input v-model="filters.start_date" type="date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                            <input v-model="filters.end_date" type="date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button @click="applyFilters" class="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors">
                            Apply Filters
                        </button>
                        <button @click="clearFilters" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                            Clear Filters
                        </button>

                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mr-3"></div>
                        <span class="text-gray-600">Loading transactions...</span>
                    </div>
                </div>

                <!-- Error State -->
                <div v-else-if="error" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="text-center py-8">
                        <div class="text-red-600 mb-2">
                            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Transactions</h3>
                        <p class="text-gray-600 mb-4">{{ error }}</p>
                        <button @click="fetchTransactionHistory" class="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors">
                            Try Again
                        </button>
                    </div>
                </div>

                <!-- Transaction History -->
                <div v-else-if="transactions.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Transaction History</h3>
                        <div class="space-y-4">
                            <div
                                v-for="transaction in filteredTransactions"
                                :key="transaction.id"
                                class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div class="flex items-center space-x-4">
                                    <div class="text-2xl">
                                        {{ getTransactionIcon(transaction.type, transaction.source) }}
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ getSourceLabel(transaction.source) }}</h4>
                                        <p v-if="transaction.description" class="text-sm text-gray-600 mt-1">{{ transaction.description }}</p>
                                        <p class="text-xs text-gray-500 mt-1">{{ formatDate(transaction.created_at) }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold" :class="getTransactionColor(transaction.type)">
                                        {{ formatAmount(transaction.amount, transaction.type) }}
                                    </p>
                                    <p class="text-xs text-gray-500 capitalize">{{ transaction.type }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="text-center py-8">
                        <div class="w-20 h-20 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
                        <p class="text-gray-600 mb-6">Your credit transactions will appear here once you start earning or using credits.</p>
                        <Link
                            href="/chat"
                            class="inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                            Start Chatting
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
