import 'package:flutter/material.dart';

/// Custom page route transition for smooth navigation between feed and full screen views
class FeedToFullScreenTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Offset? startOffset;
  final Duration duration;

  FeedToFullScreenTransition({
    required this.child,
    this.startOffset,
    this.duration = const Duration(milliseconds: 300),
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          settings: settings,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildTransition(
              context,
              animation,
              secondaryAnimation,
              child,
              startOffset,
            );
          },
        );

  static Widget _buildTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
    Offset? startOffset,
  ) {
    // Fade transition combined with scale for smooth effect
    const begin = 0.0;
    const end = 1.0;
    const curve = Curves.easeInOutCubic;

    var fadeAnimation = Tween(begin: begin, end: end).animate(
      CurvedAnimation(parent: animation, curve: curve),
    );

    var scaleAnimation = Tween(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: animation, curve: curve),
    );

    // If we have a start offset, use slide transition
    if (startOffset != null) {
      var slideAnimation = Tween(
        begin: startOffset,
        end: Offset.zero,
      ).animate(CurvedAnimation(parent: animation, curve: curve));

      return SlideTransition(
        position: slideAnimation,
        child: FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        ),
      );
    }

    // Default fade + scale transition
    return FadeTransition(
      opacity: fadeAnimation,
      child: ScaleTransition(
        scale: scaleAnimation,
        child: child,
      ),
    );
  }
}

/// Hero-style transition for media content
class MediaHeroTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final String heroTag;
  final Duration duration;

  MediaHeroTransition({
    required this.child,
    required this.heroTag,
    this.duration = const Duration(milliseconds: 400),
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          settings: settings,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildHeroTransition(
              context,
              animation,
              secondaryAnimation,
              child,
            );
          },
        );

  static Widget _buildHeroTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const curve = Curves.easeInOutCubic;

    var fadeAnimation = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animation, curve: curve),
    );

    var scaleAnimation = Tween(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: animation, curve: curve),
    );

    return FadeTransition(
      opacity: fadeAnimation,
      child: ScaleTransition(
        scale: scaleAnimation,
        child: child,
      ),
    );
  }
}

/// Slide up transition for modal-like presentation
class SlideUpTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Duration duration;

  SlideUpTransition({
    required this.child,
    this.duration = const Duration(milliseconds: 350),
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          settings: settings,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildSlideUpTransition(
              context,
              animation,
              secondaryAnimation,
              child,
            );
          },
        );

  static Widget _buildSlideUpTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const begin = Offset(0.0, 1.0);
    const end = Offset.zero;
    const curve = Curves.easeInOutCubic;

    var slideAnimation = Tween(begin: begin, end: end).animate(
      CurvedAnimation(parent: animation, curve: curve),
    );

    var fadeAnimation = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animation, curve: Interval(0.0, 0.7, curve: curve)),
    );

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: child,
      ),
    );
  }
}

/// Custom transition that mimics Instagram's feed to story transition
class InstagramStyleTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Duration duration;

  InstagramStyleTransition({
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          settings: settings,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildInstagramTransition(
              context,
              animation,
              secondaryAnimation,
              child,
            );
          },
        );

  static Widget _buildInstagramTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const curve = Curves.easeInOutCubic;

    // Scale animation for the incoming page
    var scaleAnimation = Tween(begin: 0.85, end: 1.0).animate(
      CurvedAnimation(parent: animation, curve: curve),
    );

    // Fade animation
    var fadeAnimation = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animation, curve: Interval(0.0, 0.8, curve: curve)),
    );

    // Background fade for smooth transition
    var backgroundFade = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: animation, curve: Interval(0.2, 1.0, curve: curve)),
    );

    return Stack(
      children: [
        // Background overlay
        FadeTransition(
          opacity: backgroundFade,
          child: Container(
            color: Colors.black,
          ),
        ),
        // Main content
        FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        ),
      ],
    );
  }
}
