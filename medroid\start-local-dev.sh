#!/bin/bash

# Local Development Server (No Tunnel Required)
# This will start <PERSON><PERSON> and temporarily change Flutter to use localhost

echo "🚀 Starting local development setup..."

# Kill existing Laravel processes
pkill -f "php.*serve"
sleep 1

# Clear Laravel caches
php artisan route:clear
php artisan config:clear

# Start Laravel server
echo "🌐 Starting Laravel server on localhost:8000..."
php artisan serve --host=0.0.0.0 --port=8000 &
LARAVEL_PID=$!

sleep 3

# Test Laravel
if curl -s http://localhost:8000/api/user > /dev/null; then
    echo "✅ Laravel server is running"
else
    echo "❌ Laravel server failed to start"
    exit 1
fi

# Update Flutter app to use localhost (backup original first)
FLUTTER_ENV="/Users/<USER>/medroid-main-new/medroid-app/.env"
if [ -f "$FLUTTER_ENV" ]; then
    echo "📝 Updating Flutter app to use localhost..."
    
    # Backup original
    cp "$FLUTTER_ENV" "$FLUTTER_ENV.backup"
    
    # Update to localhost
    sed -i '' 's|https://api.medroid.ai/api/|http://localhost:8000/api/|g' "$FLUTTER_ENV"
    sed -i '' 's|https://api.medroid.ai/|http://localhost:8000/|g' "$FLUTTER_ENV"
    
    echo "✅ Flutter configured for localhost"
    echo "💡 Remember to restart your Flutter app"
fi

echo ""
echo "✨ Local development ready!"
echo "   Laravel: http://localhost:8000"
echo "   Flutter app now configured for localhost"
echo ""
echo "🛑 Press Ctrl+C to stop and restore settings"

# Cleanup function
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    
    # Stop Laravel
    kill $LARAVEL_PID 2>/dev/null
    
    # Restore Flutter settings
    if [ -f "$FLUTTER_ENV.backup" ]; then
        mv "$FLUTTER_ENV.backup" "$FLUTTER_ENV"
        echo "✅ Flutter settings restored"
    fi
    
    echo "✅ Cleanup complete"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Keep running
wait $LARAVEL_PID