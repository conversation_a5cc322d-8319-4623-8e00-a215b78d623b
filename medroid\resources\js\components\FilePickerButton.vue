<template>
    <div>
        <!-- File Picker Button -->
        <button
            type="button"
            @click="openFilePicker"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
            <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
            </svg>
            {{ buttonText }}
        </button>

        <!-- Selected Files Display -->
        <div v-if="selectedFiles.length > 0" class="mt-3 space-y-2">
            <div class="text-sm font-medium text-gray-700">
                Selected {{ multiple ? 'Files' : 'File' }}:
            </div>
            <div class="space-y-2">
                <div 
                    v-for="file in selectedFiles" 
                    :key="file.id"
                    class="flex items-center justify-between p-2 bg-gray-50 rounded border"
                >
                    <div class="flex items-center space-x-3">
                        <!-- File Preview -->
                        <div class="flex-shrink-0">
                            <img 
                                v-if="file.is_image" 
                                :src="file.url" 
                                :alt="file.name" 
                                class="w-10 h-10 object-cover rounded"
                            >
                            <div v-else class="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        
                        <!-- File Info -->
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</div>
                            <div class="text-xs text-gray-500">
                                {{ formatFileSize(file.size) }} • {{ getCategoryLabel(file.category) }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Remove Button -->
                    <button
                        @click="removeFile(file.id)"
                        class="flex-shrink-0 text-red-400 hover:text-red-600"
                    >
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- File Picker Modal -->
        <FilePicker
            :is-open="isPickerOpen"
            :multiple="multiple"
            :accepted-types="acceptedTypes"
            :accepted-types-text="acceptedTypesText"
            :category="category"
            @close="closeFilePicker"
            @select="handleFileSelection"
        />
    </div>
</template>

<script>
import { ref, computed } from 'vue'
import FilePicker from '@/components/FilePicker.vue'

export default {
    name: 'FilePickerButton',
    components: {
        FilePicker
    },
    props: {
        modelValue: {
            type: [Array, Object],
            default: () => []
        },
        multiple: {
            type: Boolean,
            default: false
        },
        acceptedTypes: {
            type: String,
            default: '*/*'
        },
        acceptedTypesText: {
            type: String,
            default: 'All file types supported'
        },
        category: {
            type: String,
            default: 'all'
        },
        buttonText: {
            type: String,
            default: 'Choose Files'
        }
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        const isPickerOpen = ref(false)
        
        const selectedFiles = computed({
            get() {
                if (props.multiple) {
                    return Array.isArray(props.modelValue) ? props.modelValue : []
                } else {
                    return props.modelValue ? [props.modelValue] : []
                }
            },
            set(value) {
                if (props.multiple) {
                    emit('update:modelValue', value)
                } else {
                    emit('update:modelValue', value.length > 0 ? value[0] : null)
                }
            }
        })

        const openFilePicker = () => {
            isPickerOpen.value = true
        }

        const closeFilePicker = () => {
            isPickerOpen.value = false
        }

        const handleFileSelection = (files) => {
            selectedFiles.value = files
        }

        const removeFile = (fileId) => {
            const updatedFiles = selectedFiles.value.filter(file => file.id !== fileId)
            selectedFiles.value = updatedFiles
        }

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes'
            const k = 1024
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        const getCategoryLabel = (categoryValue) => {
            const categories = {
                'product_images': 'Product Images',
                'profile_images': 'Profile Images',
                'documents': 'Documents',
                'videos': 'Videos',
                'audio': 'Audio',
                'stories': 'Stories',
                'chat_attachments': 'Chat Attachments',
                'general': 'General'
            }
            return categories[categoryValue] || categoryValue
        }

        return {
            isPickerOpen,
            selectedFiles,
            openFilePicker,
            closeFilePicker,
            handleFileSelection,
            removeFile,
            formatFileSize,
            getCategoryLabel
        }
    }
}
</script>
