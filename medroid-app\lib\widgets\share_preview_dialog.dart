import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/app_colors.dart';

class SharePreviewDialog extends StatefulWidget {
  final Map<String, dynamic> shareData;

  const SharePreviewDialog({
    Key? key,
    required this.shareData,
  }) : super(key: key);

  @override
  State<SharePreviewDialog> createState() => _SharePreviewDialogState();
}

class _SharePreviewDialogState extends State<SharePreviewDialog> {
  late TextEditingController _contentController;
  late List<String> _selectedTopics;
  bool _isPosting = false;

  @override
  void initState() {
    super.initState();
    _contentController = TextEditingController(text: widget.shareData['content']);
    _selectedTopics = List<String>.from(widget.shareData['healthTopics']);
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.share, color: AppColors.tealSurge),
                    SizedBox(width: 8),
                    Text(
                      'Share to Feed',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Public indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.tealSurge.withAlpha(25),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppColors.tealSurge, width: 1),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.public, size: 16, color: AppColors.tealSurge),
                  SizedBox(width: 4),
                  Text(
                    'PUBLIC',
                    style: TextStyle(
                      color: AppColors.tealSurge,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Content editor
            const Text(
              'Post Content',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            Flexible(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: _contentController,
                  maxLines: null,
                  minLines: 6,
                  style: const TextStyle(fontSize: 14, height: 1.4),
                  decoration: const InputDecoration(
                    hintText: 'Edit your post content...',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(12),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Health topics
            const Text(
              'Health Topics',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: _selectedTopics.map((topic) {
                return Chip(
                  label: Text(
                    topic,
                    style: const TextStyle(fontSize: 12),
                  ),
                  backgroundColor: AppColors.tealSurge.withAlpha(25),
                  deleteIcon: const Icon(Icons.close, size: 18),
                  onDeleted: () {
                    setState(() {
                      _selectedTopics.remove(topic);
                    });
                  },
                );
              }).toList(),
            ),
            
            if (widget.shareData['hashtags'] != null) ...[
              const SizedBox(height: 12),
              const Text(
                'Suggested Hashtags',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                (widget.shareData['hashtags'] as List<String>).join(' '),
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.tealSurge,
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isPosting ? null : () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isPosting ? null : _shareToFeed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.tealSurge,
                      foregroundColor: Colors.white,
                    ),
                    child: _isPosting
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text('Share to Feed'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _shareToFeed() async {
    if (_contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add some content to share')),
      );
      return;
    }

    setState(() {
      _isPosting = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final success = await apiService.postConversationToFeed(
        content: _contentController.text.trim(),
        healthTopics: _selectedTopics,
      );

      if (!mounted) return;

      if (success) {
        Navigator.of(context).pop(true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully shared to feed!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isPosting = false;
        });
      }
    }
  }
}