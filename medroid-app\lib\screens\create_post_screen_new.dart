import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/screens/main_navigation.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/image_cropper_widget.dart';
import 'package:image/image.dart' as img;

class CreatePostScreenNew extends StatefulWidget {
  final String? initialContent;

  const CreatePostScreenNew({Key? key, this.initialContent}) : super(key: key);

  @override
  State<CreatePostScreenNew> createState() => _CreatePostScreenNewState();
}

class _CreatePostScreenNewState extends State<CreatePostScreenNew>
    with TickerProviderStateMixin {
  final TextEditingController _captionController = TextEditingController();
  final TextEditingController _aiPromptController = TextEditingController();
  final PageController _pageController = PageController();

  File? _imageFile;
  Uint8List? _webImageBytes;
  XFile? _pickedImage;

  bool _isLoading = false;
  bool _isGeneratingAi = false;
  bool _isGeneratingHashtags = false;

  // AI content state
  int _currentStep = 0; // 0: Write/AI, 1: Image, 2: Review
  String _generatedContent = '';
  List<String> _generatedHashtags = [];

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;

  // AI prompt suggestions
  final List<String> _promptSuggestions = [
    "Write about the benefits of morning meditation",
    "Explain healthy meal prep tips for busy professionals",
    "Share facts about the importance of sleep hygiene",
    "Discuss natural ways to boost immunity",
    "Write about managing stress through exercise",
    "Explain the benefits of staying hydrated",
    "Share tips for maintaining mental wellness",
    "Discuss the importance of regular health checkups"
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Set initial content if provided
    if (widget.initialContent != null) {
      _captionController.text = _cleanMarkdownText(widget.initialContent!);
      _generateHashtagsFromContent();
    }

    _fadeController.forward();
  }

  @override
  void dispose() {
    _captionController.dispose();
    _aiPromptController.dispose();
    _pageController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  // Helper property to check if an image is selected
  bool get _hasImage => _imageFile != null || _webImageBytes != null;

  // Helper method to clean markdown formatting from AI-generated text
  String _cleanMarkdownText(String text) {
    String cleaned = text;
    
    // Remove markdown bold formatting (**text** or __text__)
    cleaned = cleaned.replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1');
    cleaned = cleaned.replaceAll(RegExp(r'__(.*?)__'), r'$1');
    
    // Remove markdown italic formatting (*text* or _text_)
    cleaned = cleaned.replaceAll(RegExp(r'\*(.*?)\*'), r'$1');
    cleaned = cleaned.replaceAll(RegExp(r'_(.*?)_'), r'$1');
    
    // Remove markdown headers (## or ###)
    cleaned = cleaned.replaceAll(RegExp(r'^#{1,6}\s*'), '', );
    
    // Replace markdown bullet points with simple dashes
    cleaned = cleaned.replaceAll(RegExp(r'^\*\s+', multiLine: true), '- ');
    cleaned = cleaned.replaceAll(RegExp(r'^\+\s+', multiLine: true), '- ');
    
    // Clean up extra whitespace
    cleaned = cleaned.replaceAll(RegExp(r'\n\s*\n\s*\n'), '\n\n');
    
    return cleaned.trim();
  }

  // Helper method to get the appropriate image provider based on platform
  ImageProvider _getImageProvider() {
    if (kIsWeb && _webImageBytes != null) {
      return MemoryImage(_webImageBytes!);
    } else if (!kIsWeb && _imageFile != null) {
      return FileImage(_imageFile!);
    }
    return const AssetImage('assets/images/placeholder.png');
  }

  // Generate hashtags from content
  Future<void> _generateHashtagsFromContent() async {
    if (_captionController.text.trim().isEmpty) return;

    setState(() {
      _isGeneratingHashtags = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final hashtags =
          await apiService.generateHashtags(_captionController.text.trim());

      setState(() {
        _generatedHashtags = hashtags;
        _isGeneratingHashtags = false;
      });
    } catch (e) {
      setState(() {
        _isGeneratingHashtags = false;
      });
      debugPrint('Error generating hashtags: $e');
    }
  }

  // AI Content Generation with enhanced flow
  Future<void> _generateAiContent() async {
    if (_aiPromptController.text.trim().isEmpty) {
      _showSnackBar('Please enter a prompt for AI generation');
      return;
    }

    setState(() {
      _isGeneratingAi = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final content = await apiService.generateAiContent(
          _aiPromptController.text.trim(), 'health_post');

      setState(() {
        _generatedContent = content;
        _captionController.text = content;
        _isGeneratingAi = false;
      });

      // Auto-generate hashtags from AI content
      await _generateHashtagsFromContent();

      _showSnackBar('AI content generated successfully!');

      // Animate to next step
      _nextStep();
    } catch (e) {
      setState(() {
        _isGeneratingAi = false;
      });
      _showSnackBar('Failed to generate content: ${e.toString()}');
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();

    // Show source selection
    final source = await _showImageSourceDialog();
    if (source == null) return;

    final XFile? image = await picker.pickImage(source: source);

    if (image != null) {
      _pickedImage = image;

      // Check if image needs cropping (not 4:3 aspect ratio)
      final bytes = await image.readAsBytes();
      final decodedImage = img.decodeImage(bytes);

      if (decodedImage != null) {
        final aspectRatio = decodedImage.width / decodedImage.height;
        const targetAspectRatio = 4.0 / 3.0;
        const tolerance = 0.05;

        if ((aspectRatio - targetAspectRatio).abs() > tolerance) {
          // Image needs cropping
          if (!mounted) return;

          final result = await Navigator.push<Map<String, dynamic>>(
            context,
            MaterialPageRoute(
              builder: (context) => ImageCropperWidget(
                imageFile: kIsWeb ? null : File(image.path),
                imageBytes: kIsWeb ? bytes : null,
                fileName: image.name,
                onCropped: (File? croppedFile, Uint8List? croppedBytes) {
                  Navigator.pop(context, {
                    'file': croppedFile,
                    'bytes': croppedBytes,
                  });
                },
                onCancel: () {
                  Navigator.pop(context);
                },
              ),
            ),
          );

          if (result != null) {
            setState(() {
              if (kIsWeb) {
                _webImageBytes = result['bytes'];
                _imageFile = null;
              } else {
                _imageFile = result['file'];
                _webImageBytes = null;
              }
            });
          }
        } else {
          // Image is already 4:3, use as is
          if (kIsWeb) {
            setState(() {
              _webImageBytes = bytes;
              _imageFile = null;
            });
          } else {
            setState(() {
              _imageFile = File(image.path);
              _webImageBytes = null;
            });
          }
        }
      }
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    return showModalBottomSheet<ImageSource>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Select Image Source',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildSourceOption(
                    icon: Icons.photo_library,
                    label: 'Gallery',
                    onTap: () => Navigator.pop(context, ImageSource.gallery),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSourceOption(
                    icon: Icons.camera_alt,
                    label: 'Camera',
                    onTap: () => Navigator.pop(context, ImageSource.camera),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: AppColors.backgroundLight,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.slateGrey.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: AppColors.coralPop),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.midnightNavy,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createPost() async {
    // Prevent multiple submissions
    if (_isLoading) return;

    if (_captionController.text.trim().isEmpty) {
      _showSnackBar('Please enter a caption');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Show uploading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.tealSurge),
            ),
            const SizedBox(height: 16),
            const Text(
              'Uploading your post...',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            const Text(
              'Please wait while we publish your content',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      // Add hashtags to caption
      String finalCaption = _captionController.text.trim();
      if (_generatedHashtags.isNotEmpty) {
        finalCaption +=
            '\n\n${_generatedHashtags.map((tag) => '#$tag').join(' ')}';
      }

      // Validate final caption length
      if (finalCaption.length > 1000) {
        setState(() {
          _isLoading = false;
        });
        _showSnackBar('Caption is too long. Please reduce content or hashtags.');
        return;
      }

      if (kIsWeb && _webImageBytes != null && _pickedImage != null) {
        await apiService.createPostWeb(
          caption: finalCaption,
          topics: _generatedHashtags, // Use hashtags as topics
          imageBytes: _webImageBytes,
          fileName: _pickedImage!.name,
        );
      } else {
        await apiService.createPost(
          caption: finalCaption,
          topics: _generatedHashtags, // Use hashtags as topics
          imageFile: _imageFile,
        );
      }

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Close uploading dialog
      Navigator.of(context).pop();
      
      _showSnackBar('Post created successfully! 🎉');
      
      // Small delay to show success message, then navigate
      await Future.delayed(const Duration(milliseconds: 1500));
      
      if (!mounted) return;
      
      // Navigate back to main navigation
      Navigator.of(context).popUntil((route) => route.isFirst);
      
      // Switch to feed tab (index 1) if possible
      try {
        final mainNavigationContext = Navigator.of(context).context;
        final mainNavigation = mainNavigationContext.findAncestorStateOfType<MainNavigationState>();
        if (mainNavigation != null) {
          mainNavigation.switchToTab(1); // Feed tab
        }
      } catch (e) {
        debugPrint('Could not switch to feed tab: $e');
      }
    } catch (e) {
      if (!mounted) return;
      
      // Close uploading dialog
      Navigator.of(context).pop();
      
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('Error creating post: ${e.toString()}');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _nextStep() {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
      });
      _pageController.animateToPage(
        _currentStep,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.animateToPage(
        _currentStep,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundLight,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildContentStep(),
                _buildImageStep(),
                _buildReviewStep(),
              ],
            ),
          ),
          _buildBottomNavigation(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Create Post',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      backgroundColor: AppColors.cloudWhite,
      foregroundColor: AppColors.midnightNavy,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: List.generate(3, (index) {
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: index < 2 ? 8 : 0),
              height: 4,
              decoration: BoxDecoration(
                color: index <= _currentStep
                    ? AppColors.coralPop
                    : AppColors.slateGrey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildContentStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStepHeader(
            title: 'What\'s on your mind?',
            subtitle: 'Share your health insights with the community',
            icon: Icons.edit_note,
          ),
          const SizedBox(height: 24),
          _buildContentInput(),
          const SizedBox(height: 24),
          _buildAiSection(),
          if (_generatedHashtags.isNotEmpty) ...[
            const SizedBox(height: 24),
            _buildHashtagsPreview(),
          ],
        ],
      ),
    );
  }

  Widget _buildImageStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStepHeader(
            title: 'Add a visual',
            subtitle: 'Make your post stand out with an image',
            icon: Icons.image,
          ),
          const SizedBox(height: 24),
          _buildImagePicker(),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStepHeader(
            title: 'Review your post',
            subtitle: 'Take a final look before sharing',
            icon: Icons.preview,
          ),
          const SizedBox(height: 24),
          _buildPostPreview(),
        ],
      ),
    );
  }

  Widget _buildStepHeader({
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.coralPop.withValues(alpha: 0.1),
            AppColors.coralPop.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.coralPop.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.coralPop,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.midnightNavy,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.slateGrey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentInput() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cloudWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.slateGrey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _captionController,
        decoration: InputDecoration(
          hintText: 'Share your health knowledge...',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.all(20),
          hintStyle: TextStyle(
            color: AppColors.slateGrey,
            fontSize: 16,
          ),
          suffixIcon: Container(
            margin: const EdgeInsets.all(8),
            child: IconButton(
              icon: Icon(
                _isGeneratingHashtags ? Icons.hourglass_empty : Icons.tag,
                color: AppColors.coralPop,
              ),
              onPressed:
                  _isGeneratingHashtags ? null : _generateHashtagsFromContent,
              tooltip: 'Generate hashtags',
            ),
          ),
        ),
        maxLines: 6,
        maxLength: 1000,
        style: const TextStyle(
          fontSize: 16,
          height: 1.5,
          color: AppColors.midnightNavy,
        ),
        onChanged: (value) {
          // Auto-generate hashtags when user stops typing
          if (value.trim().isNotEmpty) {
            Future.delayed(const Duration(milliseconds: 1000), () {
              if (_captionController.text == value) {
                _generateHashtagsFromContent();
              }
            });
          }
        },
      ),
    );
  }

  Widget _buildAiSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.coralPop.withValues(alpha: 0.05),
            AppColors.cloudWhite,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.coralPop.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.coralPop,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'AI Content Generator',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.midnightNavy,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Prompt suggestions
          const Text(
            'Try these prompts:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.slateGrey,
            ),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _promptSuggestions.take(3).map((suggestion) {
              return GestureDetector(
                onTap: () {
                  _aiPromptController.text = suggestion;
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.backgroundLight,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.slateGrey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    suggestion,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.slateGrey,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          TextField(
            controller: _aiPromptController,
            decoration: InputDecoration(
              hintText: 'Or write your own prompt...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.slateGrey.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.coralPop),
              ),
              contentPadding: const EdgeInsets.all(16),
              hintStyle: TextStyle(
                color: AppColors.slateGrey,
                fontSize: 14,
              ),
            ),
            maxLines: 2,
            style: const TextStyle(fontSize: 14),
          ),

          const SizedBox(height: 16),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isGeneratingAi ? null : _generateAiContent,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.coralPop,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: _isGeneratingAi
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Generating...'),
                      ],
                    )
                  : const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.auto_awesome, size: 18),
                        SizedBox(width: 8),
                        Text('Generate Content'),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHashtagsPreview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cloudWhite,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.coralPop.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.tag,
                color: AppColors.coralPop,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Generated Hashtags',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.midnightNavy,
                ),
              ),
              const Spacer(),
              if (_isGeneratingHashtags)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.coralPop),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _generatedHashtags.map((hashtag) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppColors.coralPop.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.coralPop.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  '#$hashtag',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.coralPop,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePicker() {
    return GestureDetector(
      onTap: _pickImage,
      child: AspectRatio(
        aspectRatio: 4 / 3,
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.backgroundLight,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.slateGrey.withValues(alpha: 0.2),
              width: 2,
              style: BorderStyle.solid,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.slateGrey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            image: _hasImage
                ? DecorationImage(
                    image: _getImageProvider(),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: !_hasImage
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: AppColors.coralPop.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.add_photo_alternate,
                        size: 40,
                        color: AppColors.coralPop,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Add a photo to your post',
                      style: TextStyle(
                        color: AppColors.slateGrey,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap to select from gallery or camera',
                      style: TextStyle(
                        color: AppColors.slateGrey.withValues(alpha: 0.7),
                        fontSize: 14,
                      ),
                    ),
                  ],
                )
              : Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: Colors.black.withValues(alpha: 0.1),
                      ),
                    ),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.6),
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.edit,
                                color: Colors.white,
                                size: 20,
                              ),
                              onPressed: _pickImage,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.6),
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 20,
                              ),
                              onPressed: () {
                                setState(() {
                                  _imageFile = null;
                                  _webImageBytes = null;
                                  _pickedImage = null;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildPostPreview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cloudWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.slateGrey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: AppColors.coralPop,
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'You',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'Just now',
                    style: TextStyle(
                      color: AppColors.slateGrey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            _captionController.text,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
              color: AppColors.midnightNavy,
            ),
          ),
          if (_generatedHashtags.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: _generatedHashtags.map((hashtag) {
                return Text(
                  '#$hashtag',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.coralPop,
                    fontWeight: FontWeight.w500,
                  ),
                );
              }).toList(),
            ),
          ],
          if (_hasImage) ...[
            const SizedBox(height: 16),
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: AspectRatio(
                aspectRatio: 4 / 3,
                child: Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: _getImageProvider(),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              _buildPreviewAction(Icons.favorite_border, '0'),
              const SizedBox(width: 24),
              _buildPreviewAction(Icons.chat_bubble_outline, '0'),
              const SizedBox(width: 24),
              _buildPreviewAction(Icons.share, '0'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewAction(IconData icon, String count) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.slateGrey,
        ),
        const SizedBox(width: 4),
        Text(
          count,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.slateGrey,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cloudWhite,
        border: Border(
          top: BorderSide(
            color: AppColors.slateGrey.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.slateGrey,
                  side: BorderSide(
                      color: AppColors.slateGrey.withValues(alpha: 0.3)),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Back'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 12),
          Expanded(
            flex: _currentStep == 0 ? 1 : 2,
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : (_currentStep == 2 ? _createPost : _nextStep),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.coralPop,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Publishing...'),
                      ],
                    )
                  : Text(
                      _currentStep == 2 ? 'Publish Post' : 'Continue',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
