<?php

/**
 * Fix Login Issues Script
 * 
 * This script attempts to fix common login-related 500 errors
 */

echo "=== FIX LOGIN ISSUES SCRIPT ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "ERROR: Run this script from the Laravel root directory.\n";
    exit(1);
}

function runCommand($command, $description) {
    echo "Running: $description\n";
    echo "Command: $command\n";
    $output = shell_exec($command . ' 2>&1');
    echo "Output: $output\n";
    return $output;
}

// 1. Clear all caches first
echo "1. CLEARING ALL CACHES\n";
echo "======================\n";

$cacheCommands = [
    'php artisan config:clear' => 'Clear config cache',
    'php artisan route:clear' => 'Clear route cache',
    'php artisan view:clear' => 'Clear view cache',
    'php artisan cache:clear' => 'Clear application cache',
    'php artisan optimize:clear' => 'Clear all optimization caches'
];

foreach ($cacheCommands as $command => $description) {
    runCommand($command, $description);
}

// 2. Check and fix APP_KEY
echo "\n2. CHECKING APP_KEY\n";
echo "===================\n";

$appKeyCheck = shell_exec('php artisan tinker --execute="echo config(\'app.key\') ? \'SET\' : \'NOT_SET\';" 2>&1');
if (strpos($appKeyCheck, 'NOT_SET') !== false) {
    echo "APP_KEY is not set. Generating new key...\n";
    runCommand('php artisan key:generate', 'Generate APP_KEY');
} else {
    echo "✓ APP_KEY is already set\n";
}

// 3. Check database connection and migrate if needed
echo "\n3. CHECKING DATABASE\n";
echo "====================\n";

$dbCheck = shell_exec('php artisan tinker --execute="try { DB::connection()->getPdo(); echo \'OK\'; } catch(Exception \$e) { echo \'ERROR: \' . \$e->getMessage(); }" 2>&1');
if (strpos($dbCheck, 'ERROR') !== false) {
    echo "Database connection issue: $dbCheck\n";
} else {
    echo "✓ Database connection is working\n";
    
    // Check if migrations are up to date
    echo "Checking migrations...\n";
    runCommand('php artisan migrate:status', 'Check migration status');
    
    // Run migrations if needed
    echo "Running migrations (if any pending)...\n";
    runCommand('php artisan migrate --force', 'Run migrations');
}

// 4. Check and fix session table
echo "\n4. CHECKING SESSION CONFIGURATION\n";
echo "=================================\n";

$sessionDriver = shell_exec('php artisan tinker --execute="echo config(\'session.driver\');" 2>&1');
echo "Session driver: " . trim($sessionDriver) . "\n";

if (strpos($sessionDriver, 'database') !== false) {
    echo "Database session driver detected. Ensuring sessions table exists...\n";
    runCommand('php artisan session:table', 'Create sessions table migration');
    runCommand('php artisan migrate --force', 'Run session table migration');
}

// 5. Fix common authentication issues
echo "\n5. FIXING AUTHENTICATION ISSUES\n";
echo "===============================\n";

// Check if auth routes exist
if (!file_exists('routes/auth.php')) {
    echo "Creating missing auth.php routes file...\n";
    
    $authRoutes = "<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\ConfirmablePasswordController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\Auth\EmailVerificationPromptController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\VerifyEmailController;
use Illuminate\Support\Facades\Route;

Route::middleware('guest')->group(function () {
    Route::get('register', [RegisteredUserController::class, 'create'])
                ->name('register');

    Route::post('register', [RegisteredUserController::class, 'store']);

    Route::get('login', [AuthenticatedSessionController::class, 'create'])
                ->name('login');

    Route::post('login', [AuthenticatedSessionController::class, 'store']);

    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
                ->name('password.request');

    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])
                ->name('password.email');

    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
                ->name('password.reset');

    Route::post('reset-password', [NewPasswordController::class, 'store'])
                ->name('password.store');
});

Route::middleware('auth')->group(function () {
    Route::get('verify-email', EmailVerificationPromptController::class)
                ->name('verification.notice');

    Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
                ->middleware(['signed', 'throttle:6,1'])
                ->name('verification.verify');

    Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
                ->middleware('throttle:6,1')
                ->name('verification.send');

    Route::get('confirm-password', [ConfirmablePasswordController::class, 'show'])
                ->name('password.confirm');

    Route::post('confirm-password', [ConfirmablePasswordController::class, 'store']);

    Route::put('password', [PasswordController::class, 'update'])->name('password.update');

    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
                ->name('logout');
});
";
    
    file_put_contents('routes/auth.php', $authRoutes);
    echo "✓ Created routes/auth.php\n";
}

// 6. Check web.php includes auth routes
echo "\n6. CHECKING WEB ROUTES\n";
echo "======================\n";

if (file_exists('routes/web.php')) {
    $webContent = file_get_contents('routes/web.php');
    
    if (strpos($webContent, "require __DIR__.'/auth.php'") === false && 
        strpos($webContent, 'Auth::routes()') === false) {
        
        echo "Adding auth routes to web.php...\n";
        $webContent .= "\n\n// Authentication Routes\nrequire __DIR__.'/auth.php';\n";
        file_put_contents('routes/web.php', $webContent);
        echo "✓ Added auth routes to web.php\n";
    } else {
        echo "✓ Auth routes are already included in web.php\n";
    }
}

// 7. Create missing auth controllers if needed
echo "\n7. CHECKING AUTH CONTROLLERS\n";
echo "============================\n";

if (!file_exists('app/Http/Controllers/Auth')) {
    echo "Creating Auth controllers directory...\n";
    mkdir('app/Http/Controllers/Auth', 0755, true);
}

// Check for basic auth controller
if (!file_exists('app/Http/Controllers/Auth/AuthenticatedSessionController.php')) {
    echo "Auth controllers missing. Installing Laravel Breeze or creating basic controllers...\n";
    
    // Try to install breeze
    $breezeInstall = shell_exec('composer require laravel/breeze --dev 2>&1');
    if (strpos($breezeInstall, 'successfully') !== false) {
        echo "✓ Laravel Breeze installed\n";
        runCommand('php artisan breeze:install api --no-interaction', 'Install Breeze API');
    } else {
        echo "Could not install Breeze. Creating basic auth controller...\n";
        // Create a basic auth controller here if needed
    }
}

// 8. Fix permissions
echo "\n8. FIXING PERMISSIONS\n";
echo "=====================\n";

$directories = [
    'storage',
    'storage/logs',
    'storage/framework',
    'storage/framework/sessions',
    'storage/framework/cache',
    'storage/framework/views',
    'bootstrap/cache'
];

foreach ($directories as $dir) {
    if (file_exists($dir)) {
        chmod($dir, 0755);
        echo "✓ Fixed permissions for $dir\n";
    }
}

// 9. Optimize for production
echo "\n9. OPTIMIZING FOR PRODUCTION\n";
echo "============================\n";

$optimizeCommands = [
    'php artisan config:cache' => 'Cache configuration',
    'php artisan route:cache' => 'Cache routes',
    'php artisan view:cache' => 'Cache views'
];

foreach ($optimizeCommands as $command => $description) {
    runCommand($command, $description);
}

// 10. Final test
echo "\n10. FINAL VERIFICATION\n";
echo "======================\n";

$finalCheck = shell_exec('php artisan route:list | grep -i login 2>&1');
echo "Login routes after fix:\n";
echo $finalCheck . "\n";

echo "\n=== FIX COMPLETED ===\n";
echo "Login issues should now be resolved.\n";
echo "Please test your login functionality.\n";
echo "If issues persist, check the Laravel logs for specific error messages.\n";
