<template>
    <div v-if="isOpen" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="mt-3">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        {{ multiple ? 'Select Files' : 'Select File' }}
                    </h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Tabs -->
                <div class="border-b border-gray-200 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        <button
                            @click="activeTab = 'browse'"
                            :class="activeTab === 'browse' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                        >
                            Browse Files
                        </button>
                        <button
                            @click="activeTab = 'upload'"
                            :class="activeTab === 'upload' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                        >
                            Upload New
                        </button>
                    </nav>
                </div>

                <!-- Browse Tab -->
                <div v-if="activeTab === 'browse'" class="space-y-4">
                    <!-- Filters -->
                    <div class="flex flex-wrap gap-4 items-center bg-gray-50 p-4 rounded-lg">
                        <!-- Search -->
                        <div class="flex-1 min-w-64">
                            <input 
                                v-model="searchQuery" 
                                type="text" 
                                placeholder="Search files..." 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                            >
                        </div>
                        
                        <!-- Category Filter -->
                        <select v-model="selectedCategory" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 text-sm">
                            <option value="all">All Categories</option>
                            <option v-for="category in categories" :key="category.value" :value="category.value">
                                {{ category.label }}
                            </option>
                        </select>
                        
                        <!-- Type Filter -->
                        <select v-model="selectedType" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 text-sm">
                            <option value="all">All Types</option>
                            <option value="images">Images</option>
                            <option value="documents">Documents</option>
                            <option value="videos">Videos</option>
                        </select>
                    </div>

                    <!-- Loading State -->
                    <div v-if="loading" class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-gray-600">Loading files...</p>
                    </div>

                    <!-- Files Grid -->
                    <div v-else-if="files.data && files.data.length > 0" class="space-y-4">
                        <!-- Selected Files Info -->
                        <div v-if="selectedFiles.length > 0" class="bg-blue-50 p-3 rounded-lg">
                            <p class="text-blue-800 text-sm">
                                {{ selectedFiles.length }} file(s) selected
                                <button @click="clearSelection" class="ml-2 text-blue-600 hover:text-blue-800 underline">
                                    Clear
                                </button>
                            </p>
                        </div>

                        <!-- Files Grid -->
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-h-96 overflow-y-auto">
                            <div 
                                v-for="file in files.data" 
                                :key="file.id" 
                                class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer"
                                @click="toggleFileSelection(file)"
                                :class="{ 'ring-2 ring-blue-500 bg-blue-50': isFileSelected(file.id) }"
                            >
                                <div class="text-center">
                                    <!-- File Preview -->
                                    <div class="mb-2">
                                        <img v-if="file.is_image" :src="file.url" :alt="file.name" class="w-full h-16 object-cover rounded">
                                        <div v-else class="w-full h-16 bg-gray-100 rounded flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="text-xs font-medium text-gray-900 truncate" :title="file.name">{{ file.name }}</div>
                                    <div class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div v-if="files.last_page > 1" class="flex justify-center space-x-2">
                            <button 
                                v-for="page in paginationPages" 
                                :key="page"
                                @click="changePage(page)"
                                :class="page === files.current_page ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'"
                                class="px-3 py-1 border border-gray-300 rounded text-sm"
                                :disabled="page === '...'"
                            >
                                {{ page }}
                            </button>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-else class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No files found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or upload some files.</p>
                    </div>
                </div>

                <!-- Upload Tab -->
                <div v-if="activeTab === 'upload'" class="space-y-4">
                    <!-- Upload Area -->
                    <div 
                        @drop="handleDrop"
                        @dragover.prevent
                        @dragenter.prevent
                        class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                        :class="{ 'border-blue-500 bg-blue-50': isDragging }"
                    >
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="mt-4">
                            <label class="cursor-pointer">
                                <span class="mt-2 block text-sm font-medium text-gray-900">
                                    Drop files here or click to browse
                                </span>
                                <input 
                                    type="file" 
                                    :multiple="multiple" 
                                    :accept="acceptedTypes"
                                    @change="handleFileSelect" 
                                    class="hidden"
                                    ref="fileInput"
                                >
                            </label>
                            <p class="mt-2 text-xs text-gray-500">
                                {{ acceptedTypesText }}
                            </p>
                        </div>
                    </div>

                    <!-- Upload Progress -->
                    <div v-if="uploadingFiles.length > 0" class="space-y-2">
                        <h4 class="text-sm font-medium text-gray-900">Uploading Files</h4>
                        <div v-for="(file, index) in uploadingFiles" :key="index" class="bg-gray-50 p-3 rounded">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm text-gray-700 truncate">{{ file.name }}</span>
                                <span class="text-xs text-gray-500">{{ file.progress }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                    class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                    :style="{ width: file.progress + '%' }"
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                    <button 
                        @click="closeModal"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                        Cancel
                    </button>
                    <button 
                        @click="confirmSelection"
                        :disabled="selectedFiles.length === 0"
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Select {{ selectedFiles.length > 0 ? `(${selectedFiles.length})` : '' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- File Preview Modal -->
        <FilePreviewModal
            :is-open="previewModalOpen"
            :file="previewFile"
            @close="closePreviewModal"
            @edit="editFile"
            @delete="deleteFile"
            @download="downloadFile"
        />
    </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import axios from 'axios'
import FilePreviewModal from './FilePreviewModal.vue'

export default {
    name: 'FilePicker',
    components: {
        FilePreviewModal
    },
    props: {
        isOpen: {
            type: Boolean,
            default: false
        },
        multiple: {
            type: Boolean,
            default: false
        },
        acceptedTypes: {
            type: String,
            default: '*/*'
        },
        acceptedTypesText: {
            type: String,
            default: 'All file types supported'
        },
        category: {
            type: String,
            default: 'all'
        }
    },
    emits: ['close', 'select'],
    setup(props, { emit }) {
        // Reactive data
        const activeTab = ref('browse')
        const files = ref({ data: [], total: 0, current_page: 1, last_page: 1 })
        const categories = ref([])
        const loading = ref(false)
        const isDragging = ref(false)

        // Filters
        const searchQuery = ref('')
        const selectedCategory = ref(props.category)
        const selectedType = ref('all')

        // Selection
        const selectedFiles = ref([])

        // Upload
        const uploadingFiles = ref([])
        const fileInput = ref(null)

        // Computed properties
        const paginationPages = computed(() => {
            const pages = []
            const current = files.value.current_page
            const last = files.value.last_page

            if (current > 3) pages.push(1)
            if (current > 4) pages.push('...')

            for (let i = Math.max(1, current - 2); i <= Math.min(last, current + 2); i++) {
                pages.push(i)
            }

            if (current < last - 3) pages.push('...')
            if (current < last - 2) pages.push(last)

            return pages
        })

        // Methods
        const loadFiles = async (page = 1) => {
            loading.value = true
            try {
                const params = {
                    page,
                    search: searchQuery.value,
                    category: selectedCategory.value,
                    type: selectedType.value,
                    per_page: 24
                }

                const response = await axios.get('/web-api/files', { params })
                files.value = response.data
            } catch (error) {
                console.error('Error loading files:', error)
            } finally {
                loading.value = false
            }
        }

        const loadCategories = async () => {
            try {
                const response = await axios.get('/web-api/files/categories')
                categories.value = response.data
            } catch (error) {
                console.error('Error loading categories:', error)
            }
        }

        const toggleFileSelection = (file) => {
            if (props.multiple) {
                const index = selectedFiles.value.findIndex(f => f.id === file.id)
                if (index > -1) {
                    selectedFiles.value.splice(index, 1)
                } else {
                    selectedFiles.value.push(file)
                }
            } else {
                selectedFiles.value = [file]
            }
        }

        const isFileSelected = (fileId) => {
            return selectedFiles.value.some(f => f.id === fileId)
        }

        const clearSelection = () => {
            selectedFiles.value = []
        }

        const changePage = (page) => {
            if (page !== '...' && page !== files.value.current_page) {
                loadFiles(page)
            }
        }

        const handleFileSelect = (event) => {
            const files = Array.from(event.target.files)
            uploadFiles(files)
        }

        const handleDrop = (event) => {
            event.preventDefault()
            isDragging.value = false
            const files = Array.from(event.dataTransfer.files)
            uploadFiles(files)
        }

        const uploadFiles = async (files) => {
            if (files.length === 0) return

            // Initialize upload tracking
            uploadingFiles.value = files.map(file => ({
                name: file.name,
                progress: 0,
                status: 'uploading'
            }))

            try {
                for (let i = 0; i < files.length; i++) {
                    const file = files[i]
                    const formData = new FormData()
                    formData.append('file', file)
                    formData.append('category', selectedCategory.value !== 'all' ? selectedCategory.value : 'general')

                    const response = await axios.post('/web-api/files', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        },
                        onUploadProgress: (progressEvent) => {
                            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                            uploadingFiles.value[i].progress = progress
                        }
                    })

                    uploadingFiles.value[i].status = 'completed'

                    // Add uploaded file to selection if in single mode
                    if (!props.multiple) {
                        selectedFiles.value = [response.data.file]
                    } else {
                        selectedFiles.value.push(response.data.file)
                    }
                }

                // Reload files list
                await loadFiles()

                // Switch to browse tab
                activeTab.value = 'browse'

                // Clear upload tracking after a delay
                setTimeout(() => {
                    uploadingFiles.value = []
                }, 2000)

            } catch (error) {
                console.error('Error uploading files:', error)
                // Handle error
            }
        }

        const confirmSelection = () => {
            emit('select', selectedFiles.value)
            closeModal()
        }

        const closeModal = () => {
            selectedFiles.value = []
            searchQuery.value = ''
            selectedCategory.value = props.category
            selectedType.value = 'all'
            activeTab.value = 'browse'
            uploadingFiles.value = []
            emit('close')
        }

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes'
            const k = 1024
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        // Preview functionality
        const previewModalOpen = ref(false)
        const selectedPreviewFile = ref(null)

        const previewFile = (file) => {
            selectedPreviewFile.value = file
            previewModalOpen.value = true
        }

        const closePreviewModal = () => {
            previewModalOpen.value = false
            selectedPreviewFile.value = null
        }

        const editFile = (file) => {
            // TODO: Implement edit functionality
            console.log('Edit file:', file)
        }

        const deleteFile = async (file) => {
            try {
                await axios.delete(`/web-api/files/${file.id}`)
                await loadFiles() // Reload files
                closePreviewModal()
            } catch (error) {
                console.error('Error deleting file:', error)
            }
        }

        const downloadFile = (file) => {
            // Create a temporary link and trigger download
            const link = document.createElement('a')
            link.href = file.url
            link.download = file.original_name || file.name
            link.target = '_blank'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
        }

        // Watchers
        watch([searchQuery, selectedCategory, selectedType], () => {
            loadFiles(1)
        }, { debounce: 300 })

        watch(() => props.isOpen, (newValue) => {
            if (newValue) {
                loadFiles()
                loadCategories()
            }
        })

        // Drag and drop handlers
        const handleDragEnter = () => {
            isDragging.value = true
        }

        const handleDragLeave = () => {
            isDragging.value = false
        }

        // Lifecycle
        onMounted(() => {
            if (props.isOpen) {
                loadFiles()
                loadCategories()
            }
        })

        return {
            // Data
            activeTab,
            files,
            categories,
            loading,
            isDragging,
            searchQuery,
            selectedCategory,
            selectedType,
            selectedFiles,
            uploadingFiles,
            fileInput,

            // Computed
            paginationPages,

            // Methods
            loadFiles,
            toggleFileSelection,
            isFileSelected,
            clearSelection,
            changePage,
            handleFileSelect,
            handleDrop,
            handleDragEnter,
            handleDragLeave,
            confirmSelection,
            closeModal,
            formatFileSize
        }
    }
}
</script>
