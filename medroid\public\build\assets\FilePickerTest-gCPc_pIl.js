import{_ as x}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{F as y}from"./FilePickerButton-BUMxh5r7.js";import{_ as F}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{Q as p,y as f,e as i,g as _,i as t,f as a,d as r,n as c,t as s,r as u}from"./vendor-BhKTHoN5.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const b={name:"FilePickerTest",components:{AppLayout:x,FilePickerButton:y},setup(){const n=u(null),e=u([]),d=u([]);return{singleFile:n,multipleFiles:e,documentFiles:d,clearAll:()=>{n.value=null,e.value=[],d.value=[]}}}},v={class:"py-12"},h={class:"max-w-4xl mx-auto sm:px-6 lg:px-8"},k={class:"bg-white overflow-hidden shadow-xl sm:rounded-lg p-6"},S={class:"space-y-8"},V={key:0,class:"mt-4 p-4 bg-gray-50 rounded"},P={class:"mt-2 text-sm"},A={key:0,class:"mt-4 p-4 bg-gray-50 rounded"},C={class:"font-medium"},B={class:"mt-2 text-sm"},N={key:0,class:"mt-4 p-4 bg-gray-50 rounded"},w={class:"font-medium"},D={class:"mt-2 text-sm"},T={class:"pt-6 border-t"};function J(n,e,d,l,G,I){const m=p("FilePickerButton"),g=p("AppLayout");return i(),f(g,{title:"File Picker Test"},{default:_(()=>[t("div",v,[t("div",h,[t("div",k,[e[8]||(e[8]=t("h2",{class:"text-2xl font-bold text-gray-900 mb-6"},"File Picker Component Test",-1)),t("div",S,[t("div",null,[e[5]||(e[5]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Single File Selection",-1)),a(m,{modelValue:l.singleFile,"onUpdate:modelValue":e[0]||(e[0]=o=>l.singleFile=o),multiple:!1,"accepted-types":"image/*","accepted-types-text":"Images only (PNG, JPG, GIF)",category:"product_images","button-text":"Choose Single Image"},null,8,["modelValue"]),l.singleFile?(i(),r("div",V,[e[4]||(e[4]=t("h4",{class:"font-medium"},"Selected File:",-1)),t("pre",P,s(JSON.stringify(l.singleFile,null,2)),1)])):c("",!0)]),t("div",null,[e[6]||(e[6]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Multiple File Selection",-1)),a(m,{modelValue:l.multipleFiles,"onUpdate:modelValue":e[1]||(e[1]=o=>l.multipleFiles=o),multiple:!0,"accepted-types":"*/*","accepted-types-text":"All file types supported",category:"all","button-text":"Choose Multiple Files"},null,8,["modelValue"]),l.multipleFiles.length>0?(i(),r("div",A,[t("h4",C,"Selected Files ("+s(l.multipleFiles.length)+"):",1),t("pre",B,s(JSON.stringify(l.multipleFiles,null,2)),1)])):c("",!0)]),t("div",null,[e[7]||(e[7]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Document Selection",-1)),a(m,{modelValue:l.documentFiles,"onUpdate:modelValue":e[2]||(e[2]=o=>l.documentFiles=o),multiple:!0,"accepted-types":".pdf,.doc,.docx,.txt","accepted-types-text":"Documents only (PDF, Word, Text)",category:"documents","button-text":"Choose Documents"},null,8,["modelValue"]),l.documentFiles.length>0?(i(),r("div",N,[t("h4",w,"Selected Documents ("+s(l.documentFiles.length)+"):",1),t("pre",D,s(JSON.stringify(l.documentFiles,null,2)),1)])):c("",!0)]),t("div",T,[t("button",{onClick:e[3]||(e[3]=(...o)=>l.clearAll&&l.clearAll(...o)),class:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"}," Clear All Selections ")])])])])])]),_:1})}const W=F(b,[["render",J]]);export{W as default};
