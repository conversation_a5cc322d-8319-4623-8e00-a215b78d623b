<template>
  <AppLayout>
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        My Orders
      </h2>
    </template>

    <div class="py-6">
      <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-4">
          <div class="p-4 bg-white border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-xl font-bold text-gray-900">My Orders</h1>
                <p class="text-gray-600 text-sm mt-1">Track and manage your orders</p>
              </div>
              <Link
                href="/shop"
                class="inline-flex items-center px-3 py-2 bg-medroid-orange border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-medroid-orange-dark focus:bg-medroid-orange-dark active:bg-medroid-orange-dark focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:ring-offset-2 transition ease-in-out duration-150"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                Continue Shopping
              </Link>
            </div>
          </div>
        </div>

        <!-- Orders List -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-4 bg-white">
            <div v-if="orders.data.length === 0" class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No orders yet</h3>
              <p class="mt-1 text-sm text-gray-500">You haven't placed any orders yet.</p>
              <div class="mt-6">
                <Link
                  href="/shop"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-medroid-orange hover:bg-medroid-orange-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-medroid-orange"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                  Start Shopping
                </Link>
              </div>
            </div>

            <div v-else class="space-y-4">
              <div
                v-for="order in orders.data"
                :key="order.id"
                class="border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200"
              >
                <!-- Compact Order Header -->
                <div class="p-4 border-b border-gray-100">
                  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div class="flex items-center space-x-4">
                      <div>
                        <h3 class="text-lg font-semibold text-gray-900">
                          Order #{{ order.order_number }}
                        </h3>
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{{ formatDate(order.created_at) }}</span>
                          <span>•</span>
                          <span class="font-medium text-gray-900">{{ formatPrice(order.total_amount) }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span :class="getStatusColor(order.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                        {{ order.status_label }}
                      </span>
                      <span :class="getPaymentStatusColor(order.payment_status)" class="px-2 py-1 rounded-full text-xs font-medium">
                        {{ order.payment_status_label }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Main Content Area -->
                <div class="p-4">
                  <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    <!-- Left: Order Items -->
                    <div class="lg:col-span-2">
                      <h4 class="text-sm font-medium text-gray-900 mb-3">Items Ordered</h4>
                      <div class="space-y-3">
                        <div
                          v-for="item in order.items.slice(0, 3)"
                          :key="item.id"
                          class="flex items-center space-x-3"
                        >
                          <div class="flex-shrink-0 w-14 h-14 bg-gray-200 rounded-lg overflow-hidden">
                            <img
                              v-if="item.product?.primary_image || item.product?.images?.[0]?.full_url"
                              :src="getProductImage(item)"
                              :alt="item.product_name"
                              class="w-full h-full object-cover"
                            />
                            <div v-else class="w-full h-full flex items-center justify-center">
                              <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 1v6m6-6v6" />
                              </svg>
                            </div>
                          </div>
                          <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                              {{ item.product_name }}
                            </p>
                            <p class="text-sm text-gray-500">
                              Qty: {{ item.quantity }} × {{ formatPrice(item.unit_price) }}
                            </p>
                          </div>
                          <div class="text-sm font-medium text-gray-900">
                            {{ formatPrice(item.total_price) }}
                          </div>
                        </div>
                        <div v-if="order.items.length > 3" class="text-sm text-gray-500 pl-17">
                          +{{ order.items.length - 3 }} more item{{ order.items.length - 3 > 1 ? 's' : '' }}
                        </div>
                      </div>
                    </div>

                    <!-- Right: Order Summary & Tracking -->
                    <div class="lg:col-span-1 space-y-4">
                      <!-- Tracking Status -->
                      <div v-if="order.tracking_number || order.shipping_company || order.shipped_at" class="bg-blue-50 rounded-lg p-3">
                        <div class="flex items-center mb-2">
                          <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4m0 0L9 7l-2-2M7 7l2 2"></path>
                          </svg>
                          <span class="text-sm font-medium text-blue-900">Tracking</span>
                        </div>
                        <div class="space-y-1 text-xs">
                          <div v-if="order.shipping_company" class="flex justify-between">
                            <span class="text-gray-600">Carrier:</span>
                            <span class="font-medium text-gray-900">{{ order.shipping_company }}</span>
                          </div>
                          <div v-if="order.tracking_number" class="flex justify-between">
                            <span class="text-gray-600">Tracking:</span>
                            <span class="font-medium text-gray-900">{{ order.tracking_number }}</span>
                          </div>
                          <div v-if="order.shipped_at" class="flex justify-between">
                            <span class="text-gray-600">Shipped:</span>
                            <span class="font-medium text-gray-900">{{ formatDate(order.shipped_at) }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Order Summary -->
                      <div class="bg-gray-50 rounded-lg p-3">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">Order Summary</h5>
                        <div class="space-y-1 text-xs">
                          <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal:</span>
                            <span>{{ formatPrice(order.subtotal || order.total_amount) }}</span>
                          </div>
                          <div v-if="order.shipping_amount" class="flex justify-between">
                            <span class="text-gray-600">Shipping:</span>
                            <span>{{ formatPrice(order.shipping_amount) }}</span>
                          </div>
                          <div class="flex justify-between font-medium text-sm pt-1 border-t border-gray-200">
                            <span>Total:</span>
                            <span>{{ formatPrice(order.total_amount) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Order Footer -->
                <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div class="flex items-center space-x-3 text-sm">
                      <Link
                        :href="`/shop/orders/${order.order_number}`"
                        class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-medroid-orange"
                      >
                        View Details
                      </Link>
                      <button
                        v-if="order.status === 'delivered' || order.status === 'completed'"
                        @click="openReviewModal(order)"
                        class="inline-flex items-center px-3 py-1.5 border border-green-300 shadow-sm text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                        Review
                      </button>
                      <button
                        v-if="order.can_be_cancelled"
                        @click="cancelOrder(order)"
                        class="inline-flex items-center px-3 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pagination -->
            <div v-if="orders.data.length > 0" class="mt-6">
              <Pagination :links="orders.links" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Review Modal -->
    <div v-if="showReviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Write a Review</h3>
            <button @click="closeReviewModal" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <div v-if="selectedProduct" class="mb-4">
            <p class="text-sm text-gray-600">Product:</p>
            <p class="font-medium text-gray-900">{{ selectedProduct.product_name }}</p>
          </div>

          <div class="space-y-4">
            <!-- Rating -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Rating</label>
              <div class="flex space-x-1">
                <button
                  v-for="star in 5"
                  :key="star"
                  @click="reviewForm.rating = star"
                  class="text-2xl focus:outline-none"
                  :class="star <= reviewForm.rating ? 'text-yellow-400' : 'text-gray-300'"
                >
                  ★
                </button>
              </div>
            </div>

            <!-- Title -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Title (Optional)</label>
              <input
                v-model="reviewForm.title"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Give your review a title"
              />
            </div>

            <!-- Comment -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Review *</label>
              <textarea
                v-model="reviewForm.comment"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Share your experience with this product..."
                required
              ></textarea>
            </div>

            <!-- Buttons -->
            <div class="flex space-x-3 pt-4">
              <button
                @click="submitReview"
                :disabled="submittingReview || !reviewForm.comment.trim()"
                class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="submittingReview">Submitting...</span>
                <span v-else>Submit Review</span>
              </button>
              <button
                @click="closeReviewModal"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue';
import Pagination from '@/components/Pagination.vue';
import axios from 'axios';

const props = defineProps({
  orders: {
    type: Object,
    required: true,
  },
});

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const formatPrice = (price) => {
  return '£' + parseFloat(price || 0).toFixed(2);
};

const getStatusColor = (status) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    shipped: 'bg-purple-100 text-purple-800',
    delivered: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
    refunded: 'bg-gray-100 text-gray-800',
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

const getPaymentStatusColor = (paymentStatus) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    refunded: 'bg-gray-100 text-gray-800',
  };
  return colors[paymentStatus] || 'bg-gray-100 text-gray-800';
};

const getProductImage = (item) => {
  // Try to get image from product primary_image
  if (item.product?.primary_image) {
    return item.product.primary_image.startsWith('http')
      ? item.product.primary_image
      : `/storage/${item.product.primary_image}`;
  }

  // Try to get image from product images array
  if (item.product?.images?.[0]?.full_url) {
    return item.product.images[0].full_url;
  }

  if (item.product?.images?.[0]?.image_path) {
    return item.product.images[0].image_path.startsWith('http')
      ? item.product.images[0].image_path
      : `/storage/${item.product.images[0].image_path}`;
  }

  // Fallback to default image
  return '/images/default-product.png';
};

// Review modal state
const showReviewModal = ref(false);
const selectedOrder = ref(null);
const selectedProduct = ref(null);
const reviewForm = ref({
  rating: 5,
  title: '',
  comment: ''
});
const submittingReview = ref(false);

// Review functions
const openReviewModal = (order) => {
  selectedOrder.value = order;
  // For now, let user review the first product. In a full implementation,
  // you might want to show a product selection step
  selectedProduct.value = order.items[0];
  reviewForm.value = {
    rating: 5,
    title: '',
    comment: ''
  };
  showReviewModal.value = true;
};

const closeReviewModal = () => {
  showReviewModal.value = false;
  selectedOrder.value = null;
  selectedProduct.value = null;
  reviewForm.value = {
    rating: 5,
    title: '',
    comment: ''
  };
};

const submitReview = async () => {
  if (!selectedProduct.value || !reviewForm.value.comment.trim()) {
    alert('Please fill in all required fields');
    return;
  }

  submittingReview.value = true;

  try {
    await axios.post(`/shop/products/${selectedProduct.value.product_id}/reviews`, {
      rating: reviewForm.value.rating,
      title: reviewForm.value.title,
      comment: reviewForm.value.comment,
      order_item_id: selectedProduct.value.id
    });

    alert('Review submitted successfully!');
    closeReviewModal();
  } catch (error) {
    console.error('Error submitting review:', error);
    if (error.response?.data?.message) {
      alert(error.response.data.message);
    } else {
      alert('Failed to submit review. Please try again.');
    }
  } finally {
    submittingReview.value = false;
  }
};

const cancelOrder = (order) => {
  if (confirm(`Are you sure you want to cancel order #${order.order_number}?`)) {
    router.post(`/shop/orders/${order.order_number}/cancel`, {}, {
      onSuccess: () => {
        // Order list will be refreshed automatically
      },
      onError: () => {
        alert('Failed to cancel order. Please try again.');
      },
    });
  }
};
</script>
