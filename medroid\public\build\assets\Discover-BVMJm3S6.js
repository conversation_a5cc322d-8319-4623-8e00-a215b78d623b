import{r as x,B as H,c as Ke,w as Rt,o as Us,C as me,H as Fs,Q as Ut,d as r,e as n,f as pe,u as Hs,m as Ds,g as Is,n as i,i as t,t as d,l as G,v as ne,F as L,p as A,A as E,N as Ft,x as B,j as he,R as Ns,S as Ht}from"./vendor-BhKTHoN5.js";import{_ as Os}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{_ as qs}from"./ComingSoon-Bg3W8jN1.js";import{_ as Ws}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const Gs={key:0},Ks={key:2,class:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50"},Ys={class:"bg-white border-b border-gray-200 sticky top-0 z-40"},Js={class:"max-w-6xl mx-auto px-3 sm:px-4 lg:px-8"},Xs={class:"flex items-center justify-between py-3 sm:py-4"},Qs={class:"flex items-center space-x-2 sm:space-x-3"},Zs={key:0,class:"absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center shadow-lg border-2 border-white"},eo={class:"max-w-6xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-6"},to={class:"flex gap-3 xl:gap-6 items-start"},so={class:"w-56 xl:w-72 flex-shrink-0 hidden lg:block"},oo={class:"sticky top-28 w-56 xl:w-72 h-[calc(100vh-7rem)] overflow-y-auto pr-4"},ro={class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6 h-[180px] flex flex-col justify-center hover:shadow-md transition-all duration-200"},no={class:"text-center"},ao=["src"],lo={class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4"},io={class:"space-y-3"},co={class:"relative"},uo={key:0,class:"space-y-2"},go={class:"flex flex-wrap gap-1"},mo=["onClick"],po={key:1,class:"text-center py-2"},ho={class:"flex-1 max-w-2xl mx-auto lg:mx-0"},vo={key:0,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6"},fo={class:"flex items-center justify-between"},xo={class:"text-lg font-semibold text-gray-900"},yo={class:"text-sm text-gray-500"},bo={key:1,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6 text-center"},wo={class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6 h-[140px] hover:shadow-md transition-all duration-200"},ko={class:"flex items-center justify-between mb-3"},_o={class:"flex space-x-3 overflow-x-auto pb-2 h-20"},Co={class:"flex-shrink-0"},Mo=["onClick"],jo={class:"relative"},So={key:0,class:"w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400 p-0.5"},$o={class:"w-full h-full rounded-full bg-white p-0.5"},zo=["src","alt"],Lo={key:1,class:"w-12 h-12 rounded-full bg-gradient-to-br from-pink-400 to-purple-500 p-0.5"},Po={class:"w-full h-full rounded-full bg-white p-0.5"},Ao=["src","alt"],Bo={key:2,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"},Vo={key:3,class:"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white"},Eo={key:4,class:"absolute -bottom-1 -right-1 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full p-1"},To={class:"text-xs text-gray-700 text-center mt-1 max-w-12 truncate"},Ro={key:0,class:"flex-1 flex flex-col items-center justify-center h-20"},Uo={key:2,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4 mb-6"},Fo={class:"flex items-center justify-between mb-3"},Ho={key:0,class:"flex items-center space-x-2"},Do={key:0,class:"text-center py-6"},Io={class:"w-16 h-16 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"},No={key:0,class:"w-8 h-8 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Oo={key:1,class:"w-8 h-8 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},qo={key:2,class:"w-8 h-8 text-pink-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},Wo={class:"text-lg font-medium text-gray-900 mb-2"},Go={class:"text-sm text-gray-600 mb-4"},Ko={key:0,class:"w-full max-w-md mx-auto mb-4"},Yo={class:"flex justify-between text-xs text-gray-500 mb-1"},Jo={class:"w-full bg-gray-200 rounded-full h-2"},Xo={key:1,class:"text-sm text-green-600 font-medium"},Qo={key:1,class:"text-center py-6"},Zo=["disabled"],er={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},tr={key:1,class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 24 24"},sr={key:2,class:"space-y-4"},or={class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},rr={class:"flex items-center space-x-3"},nr={class:"font-medium text-gray-900"},ar={class:"text-sm text-gray-500"},lr={class:"flex items-center space-x-2"},ir=["disabled"],dr={key:0,class:"animate-spin -ml-1 mr-1 h-3 w-3 text-white",fill:"none",viewBox:"0 0 24 24"},cr=["disabled"],ur={key:0,class:"text-xs text-gray-500 text-center"},gr={class:"space-y-6"},mr={key:0,class:"text-center py-12"},pr={key:1,class:"text-center py-16"},hr={key:2},vr={class:"p-4 sm:p-6 pb-3 sm:pb-4"},fr={class:"flex items-center justify-between mb-3 sm:mb-4"},xr={class:"flex items-center space-x-3"},yr=["onClick"],br=["src","alt"],wr={class:"flex-1 min-w-0"},kr={class:"flex items-center space-x-2"},_r=["onClick"],Cr={key:0,class:"inline-flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-br from-purple-500 to-pink-500",title:"Instagram Post"},Mr={class:"text-xs sm:text-sm text-gray-500 mt-1"},jr={key:0,class:"ml-1"},Sr={class:"relative post-menu-container"},$r=["onClick"],zr={key:0,class:"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10"},Lr=["onClick"],Pr=["onClick"],Ar={key:0,class:"px-6 pb-4"},Br={class:"relative rounded-xl overflow-hidden bg-gray-100"},Vr={key:0,class:"relative"},Er=["data-post-id","poster","onClick","onLoadedmetadata","onPlay","onPause","onError"],Tr=["src"],Rr=["src"],Ur=["src"],Fr=["onClick"],Hr={class:"text-center text-white"},Dr={class:"text-sm text-gray-300 mt-2"},Ir=["onClick"],Nr={key:0,class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},Or={key:1,class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},qr=["src","alt"],Wr={key:2,class:"absolute top-3 right-3"},Gr=["href"],Kr={class:"px-4 sm:px-6 py-3 border-t border-gray-50"},Yr={class:"flex items-center justify-between"},Jr={class:"flex items-center space-x-6"},Xr=["onClick"],Qr=["fill"],Zr=["onClick"],en=["onClick"],tn=["onClick"],sn=["fill"],on={key:1,class:"px-4 sm:px-6 pb-3 sm:pb-4"},rn={class:"text-gray-800 leading-relaxed text-sm sm:text-base"},nn={key:0,class:"line-clamp-3",style:{display:"-webkit-box","-webkit-line-clamp":"3","-webkit-box-orient":"vertical",overflow:"hidden"}},an={key:1,class:"whitespace-pre-wrap"},ln=["onClick"],dn={key:0,class:"flex flex-wrap gap-2 mt-3"},cn=["onClick"],un=["onClick"],gn={key:2,class:"border-t border-gray-50 bg-gray-50"},mn={class:"p-4 border-b border-gray-100"},pn={class:"flex space-x-3"},hn={class:"w-8 h-8 rounded-full overflow-hidden border border-gray-200 flex-shrink-0"},vn=["src","alt"],fn={class:"flex-1"},xn=["onKeydown"],yn={class:"flex justify-end mt-2"},bn=["onClick","disabled"],wn={class:"p-4 space-y-4 max-h-96 overflow-y-auto"},kn={class:"flex space-x-3"},_n={class:"w-8 h-8 rounded-full overflow-hidden border border-gray-200 flex-shrink-0"},Cn=["src","alt"],Mn={class:"flex-1"},jn={class:"bg-white rounded-lg p-3"},Sn={class:"flex items-center justify-between mb-1"},$n={class:"text-sm font-medium text-gray-900"},zn={class:"flex items-center space-x-2"},Ln={class:"text-xs text-gray-500"},Pn={key:0,class:"relative comment-menu-container"},An=["onClick"],Bn={key:0,class:"absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10"},Vn=["onClick"],En={class:"text-sm text-gray-800 mb-2"},Tn={class:"flex items-center space-x-4 text-xs"},Rn=["onClick"],Un=["fill"],Fn=["onClick"],Hn={key:0,class:"mt-3 ml-4"},Dn={class:"flex space-x-2"},In=["onUpdate:modelValue","onKeydown"],Nn={class:"flex flex-col space-y-1"},On=["onClick","disabled"],qn=["onClick"],Wn={key:1,class:"mt-3 ml-4 space-y-2"},Gn={class:"w-6 h-6 rounded-full overflow-hidden border border-gray-200 flex-shrink-0"},Kn=["src","alt"],Yn={class:"flex-1"},Jn={class:"bg-gray-50 rounded-lg p-2"},Xn={class:"flex items-center justify-between mb-1"},Qn={class:"text-xs font-medium text-gray-900"},Zn={class:"flex items-center space-x-2"},ea={class:"text-xs text-gray-500"},ta={key:0,class:"relative comment-menu-container"},sa=["onClick"],oa={key:0,class:"absolute right-0 top-full mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10"},ra=["onClick"],na={class:"text-xs text-gray-800 mb-1"},aa={class:"flex items-center space-x-3 text-xs"},la=["onClick"],ia=["fill"],da={key:0,class:"text-center py-6"},ca={key:0,class:"flex items-center justify-center"},ua={key:1,class:"text-gray-400 text-sm"},ga={key:4,class:"text-center py-8"},ma={class:"w-48 xl:w-56 flex-shrink-0 hidden lg:block"},pa={class:"sticky top-28 space-y-4"},ha={class:"bg-white rounded-2xl shadow-sm border border-gray-100 h-[140px] flex flex-col justify-center hover:shadow-md transition-all duration-200"},va={class:"text-xs text-gray-500"},fa={key:0,class:"bg-white rounded-2xl shadow-sm border border-gray-100 p-4"},xa={class:"text-xs text-gray-600 mb-3"},ya={class:"space-y-2"},ba=["disabled"],wa=["disabled"],ka={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},_a={class:"bg-white rounded-2xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"},Ca={class:"p-6"},Ma={class:"flex items-center justify-between mb-6"},ja={class:"space-y-4"},Sa={key:0},$a={class:"flex flex-wrap gap-2 mb-3"},za=["onClick","disabled"],La={key:1},Pa={class:"flex flex-wrap gap-2 mb-3"},Aa=["onClick"],Ba={class:"border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-blue-400 transition-colors"},Va={for:"post-media-input",class:"cursor-pointer"},Ea={key:0,class:"mb-4"},Ta=["src"],Ra={class:"flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-100"},Ua=["disabled"],Fa={key:4,class:"fixed inset-0 z-50 overflow-y-auto"},Ha={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},Da={class:"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl"},Ia={class:"space-y-4"},Na={class:"flex items-center space-x-3"},Oa=["src","alt"],qa={class:"text-sm text-gray-500"},Wa={key:0,class:"rounded-xl overflow-hidden bg-gray-100"},Ga=["src","alt"],Ka={class:"prose max-w-none"},Ya={class:"text-gray-800 leading-relaxed whitespace-pre-wrap"},Ja={key:1,class:"flex flex-wrap gap-2"},Xa={class:"flex items-center justify-between pt-4 border-t border-gray-100"},Qa={class:"flex items-center space-x-6"},Za=["fill"],el=["fill"],tl={key:5,class:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"},sl={class:"relative w-full max-w-md mx-4 h-[80vh] bg-black rounded-2xl overflow-hidden"},ol={class:"absolute top-4 left-4 right-16 z-10 flex space-x-1"},rl={class:"absolute top-12 left-4 right-4 z-10 flex items-center space-x-3 text-white"},nl={class:"w-8 h-8 rounded-full overflow-hidden border-2 border-white"},al=["src","alt"],ll={class:"font-semibold text-sm"},il={class:"text-xs text-gray-300"},dl=["src","alt"],cl={key:0,class:"absolute bottom-4 left-4 right-4 z-10"},ul={class:"bg-black bg-opacity-50 rounded-lg p-3"},gl={class:"text-white text-sm"},ml={class:"absolute inset-0 flex"},pl={key:0,class:"w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity"},hl={key:0,class:"w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity"},vl={key:6,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},fl={class:"bg-white rounded-2xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"},xl={class:"p-6"},yl={class:"flex items-center justify-between mb-6"},bl={class:"space-y-4"},wl={key:0,class:"border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors"},kl={key:1,class:"relative"},_l=["src"],Cl={class:"text-xs text-gray-500 mt-1"},Ml={class:"flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-100"},jl=["disabled"],Sl={key:7,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},$l={class:"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"},zl={class:"p-6"},Ll={class:"space-y-4"},Pl={class:"flex space-x-3"},Al=["disabled"],Bl={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24"},Vl={key:0,class:"space-y-4"},El={class:"p-4 bg-gray-50 border border-gray-200 rounded-xl"},Tl={class:"text-gray-800 whitespace-pre-wrap"},Rl={class:"flex space-x-3"},Ul=["disabled"],Fl={key:8,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},Hl={class:"bg-white rounded-2xl w-full max-w-md mx-4 max-h-[80vh] overflow-hidden"},Dl={class:"p-4 border-b border-gray-200 flex items-center justify-between"},Il={class:"flex items-center space-x-2"},Nl={class:"overflow-y-auto max-h-96"},Ol={key:0,class:"p-8 text-center"},ql={key:1,class:"divide-y divide-gray-100"},Wl=["onClick"],Gl={class:"flex items-start space-x-3"},Kl={class:"flex-shrink-0 mt-1"},Yl={key:0,class:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center"},Jl={key:1,class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},Xl={key:2,class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},Ql={key:3,class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},Zl={class:"flex-1 min-w-0"},ei={class:"text-sm text-gray-900 font-medium"},ti={class:"text-sm text-gray-500 truncate"},si={class:"text-xs text-gray-400 mt-1"},oi={key:0,class:"flex-shrink-0"},ri=["src"],ni={key:1,class:"flex-shrink-0"},ai={class:"fixed top-4 right-4 z-50 space-y-2"},li={class:"p-4"},ii={class:"flex items-start"},di={class:"flex-shrink-0"},ci={key:0,class:"h-6 w-6 text-green-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},ui={key:1,class:"h-6 w-6 text-red-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},gi={key:2,class:"h-6 w-6 text-blue-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},mi={key:3,class:"h-6 w-6 text-yellow-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},pi={class:"ml-3 w-0 flex-1 pt-0.5"},hi={class:"text-sm font-medium text-gray-900"},vi={class:"ml-4 flex-shrink-0 flex"},fi=["onClick"],xi={key:9,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"},yi={class:"bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto"},bi={key:0,class:"p-6 border-b border-gray-100"},wi={class:"bg-gray-50 rounded-xl p-4"},ki={class:"flex items-center space-x-3 mb-3"},_i={class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"},Ci={class:"text-white font-medium text-sm"},Mi={class:"font-medium text-gray-900 text-sm"},ji={class:"text-xs text-gray-500"},Si={key:0,class:"mb-3"},$i=["src","alt"],zi={key:1,class:"text-sm text-gray-700 line-clamp-3"},Li={__name:"Discover",props:{initialFeed:{type:Object,default:()=>({data:[],current_page:1,last_page:1,total:0})},availableTopics:{type:Array,default:()=>[]},initialStories:{type:Array,default:()=>[]}},setup(Dt){const K=Dt,It=[{title:"Discover",href:"/discover"}],Nt=x(!1),Ot=["Health and wellness content discovery","Personalized health recommendations","Community health discussions","Expert health tips and articles"],g=H({posts:K.initialFeed.data||[],currentPage:K.initialFeed.current_page||1,lastPage:K.initialFeed.last_page||1,total:K.initialFeed.total||0,loading:!1,selectedTopic:null,sortBy:"relevance",stories:K.initialStories||[]}),f=H({show:!1,post:null,shareUrl:"",shareText:""}),Y=x(!1),J=x(!1),ve=x(!1),Pe=x(!1),Ae=x(null),Be=x(!1),b=x(null),fe=x({}),T=x({}),ae=x({}),xe=x({}),$=x({}),ye=x(null),be=x([]);let qt=0;const Ve=x(!1),R=x([]),we=Ke(()=>R.value.filter(s=>!s.read).length),ke=x(null),V=x({}),X=x({}),D=x({}),le=x(0),_e=x(!1),Ce=x([]),p=H({caption:"",hashtags:[],suggestedHashtags:[],media:null,mediaPreview:null}),k=H({isGenerating:!1,showAiModal:!1,prompt:"",generatedContent:""}),v=H({query:"",results:[],isSearching:!1,recentSearches:[]}),O=H({content:"",postId:null}),S=H({}),q=x(null),I=x(null),U=x(!0),Q=Ke(()=>g.currentPage<g.lastPage),Wt=async()=>{var s;if(console.log("🔄 loadMorePosts called - loading:",g.loading,"hasMorePosts:",Q.value),!(g.loading||!Q.value)){g.loading=!0;try{const e={page:g.currentPage+1,sort_by:g.sortBy};g.selectedTopic&&(e.content_type=g.selectedTopic),console.log("📡 Fetching page",e.page,"with params:",e);const l=(await axios.get("/web-api/feed",{params:e})).data;console.log("📦 Received",((s=l.data)==null?void 0:s.length)||0,"new posts"),console.log("📄 Page info - current:",l.current_page,"last:",l.last_page,"total:",l.total),g.posts.push(...l.data||[]),g.currentPage=l.current_page,g.lastPage=l.last_page,g.total=l.total}catch(e){console.error("Error loading more posts:",e)}finally{g.loading=!1}}},Gt=async()=>{if(p.caption.trim())try{const s=new FormData;s.append("caption",p.caption),s.append("hashtags",JSON.stringify(p.hashtags)),s.append("source","internal"),p.media&&s.append("media",p.media);const e=await axios.post("/web-api/feed/create",s,{headers:{"Content-Type":"multipart/form-data"}});g.posts.unshift(e.data.post),Kt(),Y.value=!1}catch(s){console.error("Error creating post:",s)}},Kt=()=>{p.caption="",p.hashtags=[],p.suggestedHashtags=[],p.media=null,p.mediaPreview=null},Yt=()=>{k.showAiModal=!0,k.prompt="",k.generatedContent=""},Ye=async()=>{var s,e,a;if(!k.prompt.trim()){alert("Please enter a prompt for AI to generate content");return}k.isGenerating=!0;try{const l=(s=document.head.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content");console.log("Current CSRF token:",l);const m=await axios.post("/web-api/ai/generate-content",{prompt:k.prompt,type:"social_post"},{headers:{"X-CSRF-TOKEN":l,Accept:"application/json","Content-Type":"application/json"}});if(m.data.success)k.generatedContent=m.data.content;else throw new Error(m.data.message||"Failed to generate content")}catch(l){console.error("Error generating AI content:",l),((e=l.response)==null?void 0:e.status)===419?alert("Session expired. Please refresh the page and try again."):((a=l.response)==null?void 0:a.status)===422?alert("Invalid input. Please check your prompt and try again."):alert("Failed to generate content. Please try again.")}finally{k.isGenerating=!1}},Jt=()=>{k.generatedContent&&(p.caption=k.generatedContent,ie(k.generatedContent),k.showAiModal=!1)},Xt=()=>{k.showAiModal=!1,k.prompt="",k.generatedContent=""},ie=s=>{if(!s||s.length<10){p.suggestedHashtags=[],p.hashtags=[];return}const e=[],a=/#\w+/g,l=s.match(a);l?(p.hashtags=[],l.forEach(M=>{e.push(M.toLowerCase()),p.hashtags.some(z=>z.toLowerCase()===M.toLowerCase())||p.hashtags.push(M)})):p.hashtags=[];const m=["health","wellness","fitness","nutrition","mental","physical","exercise","diet","meditation","yoga","sleep","stress","anxiety","depression","therapy","mindfulness","cardio","strength","weight","muscle","protein","vitamins","supplements","organic","natural","healing","recovery","prevention","immune","energy","balance","lifestyle","doctor","medical","treatment","symptoms","diagnosis","medicine","healthcare","selfcare","skincare","beauty","aging","longevity","chronic","pain","injury"],y=s.toLowerCase().split(/\s+/),h=[];y.forEach(M=>{const _=M.replace(/[^\w]/g,"");if(_.length>3&&m.some(z=>_.includes(z)||z.includes(_))){const z=`#${_}`,W=z.toLowerCase();!e.includes(W)&&!h.some(Le=>Le.toLowerCase()===W)&&h.length<8&&h.push(z)}}),h.length<3&&["#health","#wellness","#lifestyle","#selfcare","#mindfulness"].forEach(_=>{const z=_.toLowerCase();!e.includes(z)&&!h.some(W=>W.toLowerCase()===z)&&h.length<5&&h.push(_)}),p.suggestedHashtags=h},Qt=s=>{const e=s.toLowerCase(),a=[],l=/#\w+/g,m=p.caption.match(l);if(m&&m.forEach(h=>{a.push(h.toLowerCase())}),!a.includes(e)){let h=p.caption.trim();h&&!h.match(/#\w+\s*$/)?a.length>0?h+=" ":h+=`

`:h&&a.length>0&&(h+=" "),h+=s,p.caption=h,ie(p.caption)}},Zt=s=>{let e=p.caption;const a=new RegExp(`\\s*${s.replace("#","#")}\\s*`,"gi");e=e.replace(a," "),e=e.replace(/\s+/g," ").trim(),p.caption=e,ie(p.caption)};Rt(()=>p.caption,s=>{ie(s)},{debounce:500});const es=async s=>{if(!s||s.length<2){v.results=[];return}v.isSearching=!0;try{const e=await axios.get("/web-api/feed/search",{params:{q:s}});v.results=e.data.posts||[],v.recentSearches.includes(s)||(v.recentSearches.unshift(s),v.recentSearches.length>5&&v.recentSearches.pop())}catch(e){console.error("Search error:",e),v.results=[]}finally{v.isSearching=!1}};let Ee=null;const Je=s=>{v.query=s,Ee&&clearTimeout(Ee),Ee=setTimeout(()=>{es(s)},300)},ts=()=>{v.query="",v.results=[],v.isSearching=!1},Xe=async s=>{try{const e=await axios.post(`/web-api/feed/like/${s.id}`);s.liked=e.data.liked,s.engagement_metrics.likes=e.data.like_count,e.data.original_instagram_likes!==void 0&&(s.original_instagram_likes=e.data.original_instagram_likes),Z(`Post ${e.data.liked?"liked":"unliked"}!`,"success"),e.data.liked&&Te("like",s,"You liked this post")}catch(e){console.error("Error toggling like:",e),Z("Failed to like post","error")}},Qe=async s=>{try{const e=await axios.post(`/web-api/feed/save/${s.id}`);if(s.saved=e.data.saved,s.engagement_metrics.saves=e.data.save_count,e.data.original_instagram_saves!==void 0&&(s.original_instagram_saves=e.data.original_instagram_saves),e.data.saved)le.value+=1;else if(le.value=Math.max(0,le.value-1),_e.value){const a=Ce.value.findIndex(l=>l.id===s.id);a>-1&&Ce.value.splice(a,1)}Z(`Post ${e.data.saved?"saved":"unsaved"}!`,"success"),e.data.saved&&Te("save",s,"You saved this post")}catch(e){console.error("Error toggling save:",e),Z("Failed to save post","error")}},Ze=s=>{fe.value[s]=!fe.value[s],fe.value[s]&&!$.value[s]&&ns(s)},et=s=>{ae.value[s]=!ae.value[s]},ss=s=>{xe.value[s]=!xe.value[s]},Z=(s,e="info")=>{const a=++qt,l={id:a,message:s,type:e,timestamp:Date.now()};be.value.push(l),setTimeout(()=>{tt(a)},3e3)},tt=s=>{const e=be.value.findIndex(a=>a.id===s);e>-1&&be.value.splice(e,1)},Te=(s,e,a)=>{const l={id:Date.now()+Math.random(),type:s,post_id:e.id,post_title:e.caption?e.caption.substring(0,50)+"...":"Post",post_image:e.media_url,message:a,timestamp:new Date,read:!1};R.value.unshift(l),R.value.length>50&&(R.value=R.value.slice(0,50))},os=s=>{const e=R.value.find(a=>a.id===s);e&&(e.read=!0)},rs=()=>{R.value.forEach(s=>s.read=!0)},Re=()=>{if(console.log("🔄 Setting up infinite scroll..."),console.log("loadMoreTrigger.value:",ye.value),console.log("hasMorePosts:",Q.value),console.log("state.currentPage:",g.currentPage,"state.lastPage:",g.lastPage),!ye.value){console.log("❌ loadMoreTrigger ref is null, cannot setup infinite scroll");return}I.value&&I.value.disconnect();const s=new IntersectionObserver(e=>{const[a]=e;console.log("📍 Intersection observed:",a.isIntersecting,"hasMorePosts:",Q.value,"loading:",g.loading),a.isIntersecting&&Q.value&&!g.loading&&(console.log("🔄 Loading more posts via infinite scroll..."),Wt())},{root:null,rootMargin:"100px",threshold:.1});s.observe(ye.value),I.value=s,console.log("✅ Infinite scroll setup complete")},ns=async s=>{try{const e=await axios.get(`/web-api/feed/${s}/comments`);$.value[s]=e.data.comments||[]}catch(e){console.error("Error loading comments:",e),$.value[s]=[]}},st=async s=>{var e;if(O.content.trim())try{const a=await axios.post(`/web-api/feed/${s}/comments`,{content:O.content});$.value[s]||($.value[s]=[]),$.value[s].unshift(a.data.comment);const l=(v.query?v.results:g.posts).find(y=>y.id===s);if(l)if(!l.original_instagram_comments&&l.source==="instagram"&&(l.original_instagram_comments=((e=l.engagement_metrics)==null?void 0:e.comments)||0),l.source==="instagram"){const y=l.original_instagram_comments||0,h=a.data.local_comment_count||l.engagement_metrics.comments-y+1;l.engagement_metrics.comments=y+h}else l.engagement_metrics.comments=(l.engagement_metrics.comments||0)+1;O.content="",O.postId=null,Z("Comment added successfully!","success");const m=(v.query?v.results:g.posts).find(y=>y.id===s);m&&Te("comment",m,"You commented on this post")}catch(a){console.error("Error adding comment:",a),Z("Failed to add comment","error")}},ot=s=>{Object.keys(V.value).forEach(e=>{e!==s.toString()&&(V.value[e]=!1)}),V.value[s]=!V.value[s]},rt=async(s,e)=>{if(confirm("Are you sure you want to delete this comment?"))try{if(await axios.delete(`/web-api/feed/${s}/comments/${e}`),$.value[s]){const l=$.value[s].findIndex(m=>m.id===e);l>-1&&$.value[s].splice(l,1)}const a=(v.query?v.results:g.posts).find(l=>l.id===s);a&&(a.engagement_metrics.comments=Math.max(0,(a.engagement_metrics.comments||0)-1)),alert("Comment deleted successfully.")}catch(a){console.error("Error deleting comment:",a),alert("Failed to delete comment. Please try again.")}},nt=async(s,e)=>{try{const a=await axios.post(`/web-api/feed/${s}/comments/${e}/react`,{reaction_type:"like"}),l=$.value[s]||[],m=y=>{y.forEach(h=>{h.id===e&&(h.user_reaction=a.data.user_reaction,h.reaction_counts=a.data.reaction_counts),h.replies&&m(h.replies)})};m(l)}catch(a){console.error("Error toggling comment like:",a)}},as=s=>{X.value[s]=!X.value[s],X.value[s]||(D.value[s]="")},at=async(s,e)=>{var a;if((a=D.value[e])!=null&&a.trim())try{const l=await axios.post(`/web-api/feed/${s}/comments/${e}/reply`,{content:D.value[e]}),m=$.value[s]||[];(M=>{M.forEach(_=>{_.id===e&&(_.replies||(_.replies=[]),_.replies.push(l.data.comment))})})(m);const h=(v.query?v.results:g.posts).find(M=>M.id===s);h&&(h.engagement_metrics.comments=(h.engagement_metrics.comments||0)+1),D.value[e]="",X.value[e]=!1}catch(l){console.error("Error adding reply:",l)}},ls=s=>{D.value[s]="",X.value[s]=!1},is=async()=>{if(!_e.value)try{const s=await axios.get("/web-api/saved-posts");Ce.value=s.data.posts||[]}catch(s){console.error("Error loading saved posts:",s),Ce.value=[]}_e.value=!_e.value},ds=async s=>{if(confirm("Are you sure you want to report this post?"))try{await axios.post(`/web-api/feed/${s.id}/report`,{reason:"inappropriate_content"}),alert("Post reported successfully. Thank you for helping keep our community safe.")}catch(e){console.error("Error reporting post:",e),alert("Failed to report post. Please try again.")}},cs=async s=>{if(confirm("Are you sure you want to delete this post? This action cannot be undone."))try{await axios.delete(`/web-api/feed/${s.id}`);const e=v.query?v.results:g.posts,a=e.findIndex(l=>l.id===s.id);a>-1&&e.splice(a,1),alert("Post deleted successfully.")}catch(e){console.error("Error deleting post:",e),alert("Failed to delete post. Please try again.")}},us=s=>{f.post=s,f.shareUrl=ms(s),f.shareText=ps(s),f.show=!0},gs=()=>{f.show=!1,f.post=null,f.shareUrl="",f.shareText=""},ms=s=>`${window.location.origin}/discover?post=${s.id}`,ps=s=>{var m;const e=s.source==="instagram"?"Instagram":"Medroid AI",a=s.source==="instagram"?`@${s.instagram_username||s.username||"Instagram User"}`:`${((m=s.user)==null?void 0:m.name)||"Medroid AI User"}`;return`${s.caption?s.caption.substring(0,100)+"...":"Check out this health post"}

Shared from ${e} by ${a} on Medroid AI Health Community`},hs=async()=>{const s=encodeURIComponent(`${f.shareText}

${f.shareUrl}`);window.open(`https://wa.me/?text=${s}`,"_blank"),await ee("whatsapp")},vs=async()=>{const s=encodeURIComponent(f.shareUrl),e=encodeURIComponent("Health Post from Medroid AI Community"),a=encodeURIComponent(f.shareText);window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${s}&title=${e}&summary=${a}`,"_blank"),await ee("linkedin")},fs=async()=>{const s=encodeURIComponent("Health Post from Medroid AI Community"),e=encodeURIComponent(`${f.shareText}

View post: ${f.shareUrl}`);window.open(`mailto:?subject=${s}&body=${e}`),await ee("email")},xs=async()=>{const s=encodeURIComponent(`${f.shareText}

${f.shareUrl}`);window.open(`sms:?body=${s}`),await ee("sms")},ys=async()=>{try{await navigator.clipboard.writeText(f.shareUrl),alert("Link copied to clipboard!"),await ee("copy")}catch(s){console.error("Failed to copy link:",s);const e=document.createElement("textarea");e.value=f.shareUrl,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),alert("Link copied to clipboard!"),await ee("copy")}},ee=async s=>{var e;try{if(!f.post)return;await axios.post(`/web-api/feed/${f.post.id}/share`,{platform:s});const a=g.posts.findIndex(l=>l.id===f.post.id);if(a!==-1){const l=((e=g.posts[a].engagement_metrics)==null?void 0:e.shares)||0;g.posts[a].engagement_metrics={...g.posts[a].engagement_metrics,shares:l+1}}console.log(`Share tracked: ${s}`)}catch(a){console.error("Failed to track share:",a)}},bs=s=>{Object.keys(T.value).forEach(e=>{e!==s.toString()&&(T.value[e]=!1)}),T.value[s]=!T.value[s]},ws=s=>{const e=s.target.files[0];e&&(e.size>1024*1024?De(e,a=>{lt(a)}):lt(e))},lt=s=>{const e=new Image;e.onload=()=>{const a=e.width/e.height,l=4/3;if(Math.abs(a-l)>.05)ke.value=s,ve.value=!0;else{p.media=s;const y=new FileReader;y.onload=h=>{p.mediaPreview=h.target.result},y.readAsDataURL(s)}},e.src=URL.createObjectURL(s)},ks=s=>{if(s){const e=new File([s],"cropped-image.jpg",{type:"image/jpeg"});if(e.size>1024*1024)De(e,a=>{p.media=a;const l=new FileReader;l.onload=m=>{p.mediaPreview=m.target.result},l.readAsDataURL(a)});else{p.media=e;const a=new FileReader;a.onload=l=>{p.mediaPreview=l.target.result},a.readAsDataURL(e)}}ve.value=!1,ke.value=null},_s=()=>{ve.value=!1,ke.value=null},de=x(!1),te=x(null),P=x(0),F=x([]),Me=x(null),ce=x(0),Cs=async s=>{console.log("Viewing story:",s),te.value=s,P.value=0;try{const e=await axios.get(`/web-api/stories/user/${s.user_id}`);F.value=e.data.stories||[],de.value=!0,je(),document.addEventListener("keydown",ct)}catch(e){console.error("Error loading user stories:",e)}},Ue=()=>{de.value=!1,te.value=null,P.value=0,F.value=[],He(),document.removeEventListener("keydown",ct)},it=()=>{He()},dt=()=>{de.value&&je()},ct=s=>{if(de.value)switch(s.key){case"ArrowLeft":s.preventDefault(),ut();break;case"ArrowRight":case" ":s.preventDefault(),Fe();break;case"Escape":s.preventDefault(),Ue();break}},Fe=()=>{F.value&&P.value<F.value.length-1?(P.value++,je()):Ue()},ut=()=>{P.value>0&&(P.value--,je())},je=()=>{He(),ce.value=0;const s=5e3,e=50,a=e/s*100;Me.value=setInterval(()=>{ce.value+=a,ce.value>=100&&Fe()},e)},He=()=>{Me.value&&(clearInterval(Me.value),Me.value=null),ce.value=0},se=Ke(()=>F.value&&F.value[P.value]?F.value[P.value]:null),oe=(s=null)=>{Ae.value=s,Pe.value=!0},Ms=()=>{Pe.value=!1,Ae.value=null},js=s=>{b.value=s,Be.value=!0},Se=()=>{Be.value=!1,b.value=null},C=H({media:null,mediaPreview:null,caption:""}),Ss=async()=>{var s,e;if(!C.media){alert("Please select an image or video for your story");return}try{const a=new FormData;a.append("media",C.media),C.caption.trim()&&a.append("caption",C.caption),console.log("Creating story with:",{media:C.media.name,caption:C.caption});const l=await axios.post("/web-api/stories",a,{headers:{"Content-Type":"multipart/form-data"}});console.log("Story creation response:",l.data),await ht(),gt(),J.value=!1,alert("Story created successfully!")}catch(a){console.error("Error creating story:",a),(e=(s=a.response)==null?void 0:s.data)!=null&&e.message?alert("Failed to create story: "+a.response.data.message):alert("Failed to create story. Please try again.")}},gt=()=>{C.media=null,C.mediaPreview=null,C.caption=""},$s=s=>{const e=s.target.files[0];if(e){console.log("Story file selected:",e.name,e.type,e.size);const a=10*1024*1024;if(e.size>a){alert("File size too large. Please choose a file smaller than 10MB.");return}if(e.type.startsWith("image/"))De(e,l=>{console.log("Image compressed:",l.name,l.size),C.media=l;const m=new FileReader;m.onload=y=>{C.mediaPreview=y.target.result,console.log("Story preview created")},m.readAsDataURL(l)});else{console.log("Using video file as-is"),C.media=e;const l=new FileReader;l.onload=m=>{C.mediaPreview=m.target.result,console.log("Video preview created")},l.readAsDataURL(e)}}},De=(s,e,a=.8,l=1080)=>{const m=document.createElement("canvas"),y=m.getContext("2d"),h=new Image;h.onload=()=>{let{width:M,height:_}=h;M>l&&(_=_*l/M,M=l),m.width=M,m.height=_,y.drawImage(h,0,0,M,_),m.toBlob(e,"image/jpeg",a)},h.src=URL.createObjectURL(s)},re=s=>{const e=new Date(s),l=Math.floor((new Date-e)/(1e3*60*60));return l<1?"Just now":l<24?`${l}h ago`:l<168?`${Math.floor(l/24)}d ago`:e.toLocaleDateString()},mt=s=>{s.target.closest(".post-menu-container")||Object.keys(T.value).forEach(e=>{T.value[e]=!1}),s.target.closest(".comment-menu-container")||Object.keys(V.value).forEach(e=>{V.value[e]=!1})},pt=async()=>{g.loading=!0;try{const e=(await axios.get("/web-api/feed")).data;g.posts=e.data||[],g.currentPage=e.current_page||1,g.lastPage=e.last_page||1,g.total=e.total||0,le.value=g.posts.filter(a=>a.saved).length,console.log("Feed loaded with",g.posts.length,"posts"),console.log("Current page:",g.currentPage,"Last page:",g.lastPage),me(()=>{We(),Re()})}catch(s){console.error("Error loading feed:",s),g.posts=[]}finally{g.loading=!1}},ht=async()=>{try{const s=await axios.get("/web-api/stories");console.log("Stories response:",s.data);const e=s.data.stories||[],a=await zs();g.stories=[...a,...e],console.log("Updated stories state:",g.stories)}catch(s){console.error("Error loading stories:",s),g.stories=[]}},zs=async()=>{try{const s=await axios.get("/web-api/instagram/stories");return s.data.success?s.data.stories.map(e=>({...e,source:"instagram",user_id:e.user_id||"instagram_user",username:e.username||"Instagram User",profile_image:e.profile_image||"/images/instagram-avatar.svg",story_count:e.story_count||1,latest_story_time:e.latest_story_time||e.created_at})):[]}catch(s){return console.error("Error loading Instagram stories:",s),[]}},c=H({connected:!1,account:null,loading:!1,syncing:!1,connecting:!1,progress:{status:"idle",step:"",progress:0,message:"",imported_count:0}}),$e=async()=>{try{const s=await axios.get("/web-api/instagram/account-status");s.data.success&&(c.connected=s.data.connected,c.account=s.data.account,console.log("Instagram status updated:",{connected:s.data.connected,account:s.data.account}))}catch(s){console.error("Error checking Instagram status:",s),c.connected=!1,c.account=null}},Ie=async()=>{try{const s=await axios.get("/web-api/instagram/connection-progress");s.data.success&&(c.progress=s.data.progress,["connecting","importing","syncing"].includes(c.progress.status)?setTimeout(Ie,1e3):c.progress.status==="completed"?(c.connecting=!1,await $e(),setTimeout(()=>{$e()},1e3),c.progress.imported_count>0?alert(`Instagram connected successfully! Imported ${c.progress.imported_count} health-related posts.`):alert("Instagram connected successfully! No health-related posts found to import.")):c.progress.status==="error"&&(c.connecting=!1,alert(`Instagram connection failed: ${c.progress.message}`)))}catch(s){console.error("Error checking connection progress:",s)}},Ls=async()=>{try{c.loading=!0,c.connecting=!0;const s=await axios.get("/web-api/instagram/auth-url");s.data.success&&(setTimeout(Ie,2e3),window.location.href=s.data.auth_url)}catch(s){console.error("Error getting Instagram auth URL:",s),alert("Failed to connect Instagram. Please try again."),c.connecting=!1}finally{c.loading=!1}},vt=async()=>{if(confirm("Are you sure you want to disconnect your Instagram account?"))try{c.loading=!0,(await axios.post("/web-api/instagram/disconnect")).data.success&&(c.connected=!1,c.account=null,alert("Instagram account disconnected successfully"))}catch(s){console.error("Error disconnecting Instagram:",s),alert("Failed to disconnect Instagram. Please try again.")}finally{c.loading=!1}},ft=async()=>{var s;try{c.syncing=!0;const e=await axios.post("/web-api/instagram/sync");e.data.success&&(alert(`Successfully imported ${e.data.imported_count} health-related posts`),pt())}catch(e){console.error("Error syncing Instagram content:",e),((s=e.response)==null?void 0:s.status)===401?alert("Instagram access token has expired. Please reconnect your account."):alert("Failed to sync Instagram content. Please try again.")}finally{c.syncing=!1}},ze=s=>{S[s]||(S[s]={playing:!1,loaded:!1,unavailable:!1})},ue=s=>document.querySelector(`video[data-post-id="${s}"]`),Ne=s=>{console.log("Toggle video play for post:",s);const e=ue(s);if(console.log("Video element found:",e),!e){console.error("Video element not found for post:",s);return}ze(s),S[s].playing?qe(s):xt(s)},xt=async s=>{const e=ue(s);if(!e){console.error("Video element not found for play:",s);return}if(q.value&&q.value!==s&&qe(q.value),ze(s),e.muted=U.value,console.log("Attempting to play video:",s,"muted:",e.muted),e.readyState===0){console.log("Video not ready, waiting for load...",s);try{await new Promise((a,l)=>{const m=setTimeout(()=>{l(new Error("Video load timeout"))},1e4);e.addEventListener("loadeddata",()=>{clearTimeout(m),a()},{once:!0}),e.addEventListener("error",y=>{clearTimeout(m),l(y)},{once:!0}),e.load()})}catch(a){console.error("Video failed to load:",a,s),await Oe(s,a);return}}try{await e.play(),console.log("Video playing successfully:",s),S[s].playing=!0,q.value=s}catch(a){console.error("Video play failed:",a,s),await Oe(s,a)}},Oe=async(s,e)=>{console.log("Handling video error for post:",s,e);const a=[...discoveryPosts.value,...socialPosts.value].find(l=>l.id===s);if(a)if(a.source==="instagram")try{console.log("Attempting to refresh Instagram video URL for post:",s);const l=await fetch("/api/instagram/refresh-media",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({post_id:s})});if(l.ok){const m=await l.json();if(m.success&&m.new_media_url){console.log("Successfully refreshed Instagram video URL:",m.new_media_url),a.video_url=m.new_media_url,a.media_url=m.new_media_url;const y=ue(s);y&&(y.src=m.new_media_url,y.load(),setTimeout(()=>{y.play().then(()=>{console.log("Video playing after refresh:",s),S[s].playing=!0,q.value=s}).catch(h=>{console.error("Video still failed after refresh:",h),ge(s)})},500))}else console.error("Failed to refresh video URL:",m),ge(s)}else console.error("Video refresh request failed:",l.status),ge(s)}catch(l){console.error("Error refreshing video URL:",l),ge(s)}else ge(s)},ge=s=>{S[s]&&(S[s].unavailable=!0,S[s].playing=!1),console.log("Video marked as unavailable:",s)},Ps=(s,e)=>{console.error("Video load error for post:",s,e);const a=e.target;a&&a.error&&(console.error("Video error code:",a.error.code,"message:",a.error.message),setTimeout(()=>{Oe(s,a.error)},100))},As=async s=>{console.log("Retrying video load for post:",s),S[s]&&(S[s].unavailable=!1),await me(),setTimeout(()=>{Ne(s)},500)},qe=s=>{const e=ue(s);if(!e){console.error("Video element not found for pause:",s);return}ze(s),console.log("Pausing video:",s),e.pause(),S[s].playing=!1,q.value===s&&(q.value=null)},Bs=()=>{console.log("Toggle global mute state from:",U.value),U.value=!U.value;const s=document.querySelectorAll("video[data-post-id]");s.forEach(e=>{e.muted=U.value}),console.log("Global mute state changed to:",U.value),console.log("Applied to",s.length,"videos")},Vs=s=>{var e;return((e=S[s])==null?void 0:e.playing)||!1},Es=()=>U.value,Ts=s=>{console.log("Video loaded for post:",s),ze(s),S[s].loaded=!0;const e=ue(s);e&&(e.muted=U.value)},Rs=()=>{I.value=new IntersectionObserver(s=>{s.forEach(e=>{const l=e.target.getAttribute("data-post-id");console.log(`Video ${l} intersection:`,{isIntersecting:e.isIntersecting,intersectionRatio:e.intersectionRatio}),e.isIntersecting&&e.intersectionRatio>.5?(console.log(`Auto-playing video ${l}`),setTimeout(()=>{e.isIntersecting&&xt(l)},200)):(console.log(`Auto-pausing video ${l}`),qe(l))})},{threshold:[.5],rootMargin:"0px"})},We=()=>{me(()=>{I.value&&I.value.disconnect(),Rs();const s=document.querySelectorAll("video[data-post-id]");console.log(`Found ${s.length} videos to observe`),s.forEach(e=>{var l;const a=e.getAttribute("data-post-id");console.log(`Observing video for post ${a}`),e.muted=U.value,(l=I.value)==null||l.observe(e)})})};return Us(()=>{console.log("🚀 Discover component mounted"),console.log("Initial stories from props:",K.initialStories),console.log("Initial stories state:",g.stories),pt().then(()=>{console.log("📺 Feed loaded, setting up video auto-play..."),setTimeout(()=>{console.log("🔍 Calling observeVideos..."),We()},1e3)}),ht(),$e();const s=new URLSearchParams(window.location.search);if(s.get("instagram_success"))c.connecting=!0,Ie(),setTimeout(()=>{$e()},3e3),window.history.replaceState({},document.title,window.location.pathname);else if(s.get("instagram_error")){const e=s.get("instagram_error");alert(`Instagram connection failed: ${decodeURIComponent(e)}`),window.history.replaceState({},document.title,window.location.pathname)}document.addEventListener("click",mt),me(()=>{Re()})}),Rt(()=>g.posts,()=>{me(()=>{We(),Re()})},{deep:!0,flush:"post"}),Fs(()=>{var s;document.removeEventListener("click",mt),(s=I.value)==null||s.disconnect()}),(s,e)=>{const a=Ut("ImageCropper"),l=Ut("UserProfile");return n(),r(L,null,[pe(Hs(Ds),{title:"Discover - Medroid"}),pe(Os,{breadcrumbs:It},{default:Is(()=>{var m,y,h,M,_,z,W,Le,yt,bt;return[Nt.value?(n(),r("div",Gs,[pe(qs,{description:"We're working hard to bring you an amazing discovery experience with health content, community posts, and social feeds.",features:Ot,"action-button":{text:"Continue Chatting with AI Doctor",action:"/chat"},"action-description":"Get instant health advice while we prepare the discover feature","min-height":"min-h-screen",onAction:e[0]||(e[0]=o=>s.window.location.href=o)})])):(n(),r("div",Ks,[t("div",Ys,[t("div",Js,[t("div",Xs,[e[47]||(e[47]=t("div",{class:"flex items-center space-x-3 sm:space-x-4"},[t("div",{class:"flex items-center space-x-2 sm:space-x-3"},[t("div",{class:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-full flex items-center justify-center",style:{background:"linear-gradient(135deg, #17C3B2 0%, #8BE9C8 100%)"}},[t("svg",{class:"w-4 h-4 sm:w-6 sm:h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])]),t("div",null,[t("h1",{class:"text-lg sm:text-xl font-bold text-gray-900"},"Discover"),t("p",{class:"text-xs text-gray-500 hidden sm:block"},"Health insights & community")])])],-1)),t("div",Qs,[t("button",{onClick:e[3]||(e[3]=o=>Ve.value=!0),class:"relative p-2.5 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-full transition-all duration-200 shadow-sm border border-gray-200 hover:border-gray-300 bg-white"},[e[45]||(e[45]=t("svg",{class:"w-5 h-5 sm:w-6 sm:h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","stroke-width":"2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 17h5l-3.5-3.5a8.38 8.38 0 01-1.5-5V6a6 6 0 10-12 0v2.5a8.38 8.38 0 01-1.5 5L5 17h5m5 0v1a3 3 0 11-6 0v-1m6 0H9"})],-1)),we.value>0?(n(),r("span",Zs,d(we.value>99?"99+":we.value),1)):i("",!0)]),t("button",{onClick:e[4]||(e[4]=o=>Y.value=!0),class:"medroid-create-btn inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 text-white text-xs sm:text-sm font-medium rounded-full transition-all duration-200 shadow-lg hover:shadow-xl"},e[46]||(e[46]=[t("svg",{class:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})],-1),t("span",{class:"hidden sm:inline"},"Create Post",-1),t("span",{class:"sm:hidden"},"Post",-1)]))])])])]),t("div",eo,[t("div",to,[t("div",so,[t("div",oo,[t("div",ro,[t("div",no,[t("button",{onClick:e[5]||(e[5]=o=>oe()),class:"w-12 h-12 mx-auto mb-2 rounded-full overflow-hidden border-2 border-gray-200 hover:border-teal-400 transition-colors"},[t("img",{src:(m=s.$page.props.auth.user)!=null&&m.profile_image?s.$page.props.auth.user.profile_image.startsWith("http")?s.$page.props.auth.user.profile_image:`/storage/${s.$page.props.auth.user.profile_image}`:"/images/default-avatar.svg",alt:"Profile",class:"w-full h-full object-cover"},null,8,ao)]),e[48]||(e[48]=t("h3",{class:"text-sm font-semibold text-gray-900 mb-1"},"Your Profile",-1)),e[49]||(e[49]=t("p",{class:"text-xs text-gray-500 mb-3"},"View your posts & stats",-1)),t("button",{onClick:e[6]||(e[6]=o=>oe()),class:"medroid-profile-btn w-full py-2 text-white text-sm font-medium rounded-lg transition-all duration-200"}," View Profile ")])]),t("div",lo,[e[54]||(e[54]=t("h3",{class:"text-sm font-semibold text-gray-900 mb-3"},"Search Posts",-1)),t("div",io,[t("div",co,[G(t("input",{"onUpdate:modelValue":e[7]||(e[7]=o=>v.query=o),onInput:e[8]||(e[8]=o=>Je(o.target.value)),type:"text",placeholder:"Search by hashtags, keywords...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm"},null,544),[[ne,v.query]]),e[51]||(e[51]=t("svg",{class:"absolute left-3 top-2.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),v.query?(n(),r("button",{key:0,onClick:ts,class:"absolute right-3 top-2.5 w-4 h-4 text-gray-400 hover:text-gray-600"},e[50]||(e[50]=[t("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):i("",!0)]),v.recentSearches.length>0&&!v.query?(n(),r("div",uo,[e[52]||(e[52]=t("p",{class:"text-xs text-gray-500 font-medium"},"Recent Searches",-1)),t("div",go,[(n(!0),r(L,null,A(v.recentSearches,o=>(n(),r("button",{key:o,onClick:j=>Je(o),class:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors"},d(o),9,mo))),128))])])):i("",!0),v.isSearching?(n(),r("div",po,e[53]||(e[53]=[t("div",{class:"w-4 h-4 border-2 border-teal-200 border-t-teal-500 rounded-full animate-spin mx-auto"},null,-1)]))):i("",!0)])])])]),t("div",ho,[v.query&&v.results.length>0?(n(),r("div",vo,[t("div",fo,[t("h3",xo,' Search Results for "'+d(v.query)+'" ',1),t("span",yo,d(v.results.length)+" posts found",1)])])):i("",!0),v.query&&v.results.length===0&&!v.isSearching?(n(),r("div",bo,e[55]||(e[55]=[t("svg",{class:"w-12 h-12 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1),t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No posts found",-1),t("p",{class:"text-gray-500"},"Try searching with different keywords or hashtags",-1)]))):i("",!0),t("div",wo,[t("div",ko,[e[56]||(e[56]=t("h3",{class:"text-sm font-semibold text-gray-900"},"Stories",-1)),t("button",{onClick:e[9]||(e[9]=o=>J.value=!0),class:"text-xs font-medium",style:{color:"#17C3B2"}}," Add Story ")]),t("div",_o,[t("div",Co,[t("button",{onClick:e[10]||(e[10]=o=>J.value=!0),class:"w-12 h-12 rounded-full flex items-center justify-center border-2 border-dashed transition-colors",style:{background:"linear-gradient(135deg, #17C3B2 0.1, #8BE9C8 0.1)","border-color":"#17C3B2"}},e[57]||(e[57]=[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1)])),e[58]||(e[58]=t("p",{class:"text-xs text-gray-500 text-center mt-1"},"Add Story",-1))]),(n(!0),r(L,null,A(g.stories,o=>{var j;return n(),r("div",{key:`${o.source||"local"}-${o.user_id}`,class:"flex-shrink-0 cursor-pointer",onClick:Ge=>Cs(o)},[t("div",jo,[o.source==="instagram"?(n(),r("div",So,[t("div",$o,[t("img",{src:o.profile_image||o.user_avatar||"/images/default-avatar.svg",alt:o.username,class:"w-full h-full rounded-full object-cover"},null,8,zo)])])):(n(),r("div",Lo,[t("div",Po,[t("img",{src:o.user_avatar||o.profile_image||"/images/default-avatar.svg",alt:o.username,class:"w-full h-full rounded-full object-cover"},null,8,Ao)])])),o.story_count>1?(n(),r("div",Bo,d(o.story_count),1)):o.has_unviewed?(n(),r("div",Vo)):i("",!0),o.source==="instagram"?(n(),r("div",Eo,e[59]||(e[59]=[t("svg",{class:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})],-1)]))):i("",!0)]),t("p",To,d(o.username||((j=o.user)==null?void 0:j.name)||"Anonymous"),1)],8,Mo)}),128)),g.stories.length===0?(n(),r("div",Ro,e[60]||(e[60]=[t("svg",{class:"w-6 h-6 text-gray-300 mb-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1),t("p",{class:"text-xs text-gray-500"},"No stories yet",-1),t("p",{class:"text-xs text-gray-400"},"Be the first to share a story!",-1)]))):i("",!0)])]),c.connected?i("",!0):(n(),r("div",Uo,[t("div",Fo,[e[61]||(e[61]=t("div",{class:"flex items-center space-x-2"},[t("svg",{class:"w-5 h-5 text-pink-500",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})]),t("h3",{class:"text-sm font-semibold text-gray-900"},"Instagram Integration")],-1)),c.connected&&c.account?(n(),r("div",Ho,[t("span",{class:E(["px-2 py-1 text-xs font-medium rounded-full",c.account.account_type==="BUSINESS"?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"])},d(c.account.account_type_display),3)])):i("",!0)]),c.connecting&&c.progress.status!=="idle"?(n(),r("div",Do,[t("div",Io,[c.progress.status==="error"?(n(),r("svg",No,e[62]||(e[62]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)]))):c.progress.status==="completed"?(n(),r("svg",Oo,e[63]||(e[63]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(n(),r("svg",qo,e[64]||(e[64]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)])))]),t("h4",Wo,d(c.progress.status==="completed"?"Connection Successful!":c.progress.status==="error"?"Connection Failed":"Connecting Instagram..."),1),t("p",Go,d(c.progress.message),1),["connecting","importing","syncing"].includes(c.progress.status)?(n(),r("div",Ko,[t("div",Yo,[e[65]||(e[65]=t("span",null,"Progress",-1)),t("span",null,d(c.progress.progress)+"%",1)]),t("div",Jo,[t("div",{class:"bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full transition-all duration-500",style:Ft({width:c.progress.progress+"%"})},null,4)])])):i("",!0),c.progress.imported_count>0?(n(),r("div",Xo," ✅ Imported "+d(c.progress.imported_count)+" health-related posts ",1)):i("",!0)])):c.connected?(n(),r("div",sr,[t("div",or,[t("div",rr,[e[72]||(e[72]=t("div",{class:"w-10 h-10 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069z"})])],-1)),t("div",null,[t("p",nr,"@"+d(c.account.username),1),t("p",ar,d(c.account.media_count)+" posts",1)])]),t("div",lr,[t("button",{onClick:ft,disabled:c.syncing,class:"px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[c.syncing?(n(),r("svg",dr,e[73]||(e[73]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):i("",!0),B(" "+d(c.syncing?"Syncing...":"Sync"),1)],8,ir),t("button",{onClick:vt,disabled:c.loading,class:"px-3 py-1 bg-gray-200 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Disconnect ",8,cr)])]),c.account.last_synced_at?(n(),r("div",ur," Last synced: "+d(c.account.last_synced_at),1)):i("",!0)])):(n(),r("div",Qo,[e[68]||(e[68]=t("div",{class:"w-16 h-16 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4"},[t("svg",{class:"w-8 h-8 text-pink-500",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})])],-1)),e[69]||(e[69]=t("h4",{class:"text-lg font-medium text-gray-900 mb-2"},"Connect Your Instagram",-1)),e[70]||(e[70]=t("p",{class:"text-sm text-gray-500 mb-4 max-w-md mx-auto"}," Connect your Instagram Business or Creator account to share your health-related posts with the community. ",-1)),t("button",{onClick:Ls,disabled:c.loading,class:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-medium rounded-lg hover:from-pink-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"},[c.loading?(n(),r("svg",er,e[66]||(e[66]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(n(),r("svg",tr,e[67]||(e[67]=[t("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069z"},null,-1)]))),B(" "+d(c.loading?"Connecting...":"Connect Instagram"),1)],8,Zo),e[71]||(e[71]=t("p",{class:"text-xs text-gray-400 mt-2"}," Only Business and Creator accounts are supported ",-1))]))])),t("div",gr,[g.loading&&g.posts.length===0?(n(),r("div",mr,e[74]||(e[74]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"},null,-1),t("p",{class:"text-gray-500"},"Loading amazing content...",-1)]))):g.posts.length===0?(n(),r("div",pr,[e[76]||(e[76]=t("div",{class:"w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6"},[t("svg",{class:"w-12 h-12 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1)),e[77]||(e[77]=t("h3",{class:"text-xl font-semibold text-gray-900 mb-3"},"No posts yet",-1)),e[78]||(e[78]=t("p",{class:"text-gray-500 mb-6 max-w-md mx-auto"},"Be the first to share health insights with the community!",-1)),t("button",{onClick:e[11]||(e[11]=o=>Y.value=!0),class:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium rounded-full hover:from-blue-600 hover:to-cyan-600 transition-all duration-200 shadow-lg hover:shadow-xl"},e[75]||(e[75]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})],-1),B(" Create First Post ")]))])):(n(),r("div",hr,[(n(!0),r(L,null,A(v.query?v.results:g.posts,o=>{var j,Ge,wt,kt,_t,Ct,Mt,jt,St;return n(),r("article",{key:o.id,class:"bg-white rounded-xl sm:rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200 mb-4 sm:mb-6"},[t("div",vr,[t("div",fr,[t("div",xr,[t("button",{onClick:u=>{var N;return oe((N=o.user)==null?void 0:N.id)},class:"w-10 h-10 sm:w-12 sm:h-12 rounded-full overflow-hidden border-2 border-gray-200 hover:border-blue-400 transition-colors"},[t("img",{src:(j=o.user)!=null&&j.profile_image?o.user.profile_image.startsWith("http")?o.user.profile_image:`/storage/${o.user.profile_image}`:"/images/default-avatar.svg",alt:((Ge=o.user)==null?void 0:Ge.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,br)],8,yr),t("div",wr,[t("div",kr,[t("button",{onClick:u=>{var N;return oe((N=o.user)==null?void 0:N.id)},class:"font-semibold text-gray-900 hover:text-blue-600 transition-colors text-left text-sm sm:text-base"},d(((wt=o.user)==null?void 0:wt.name)||"Anonymous"),9,_r),o.source==="instagram"?(n(),r("div",Cr,e[79]||(e[79]=[t("svg",{class:"w-3.5 h-3.5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})],-1)]))):i("",!0)]),t("p",Mr,[B(d(re(o.published_at||o.created_at))+" ",1),o.source==="instagram"&&(o.instagram_username||o.username)?(n(),r("span",jr," @"+d(o.instagram_username||o.username),1)):i("",!0)])])]),t("div",Sr,[t("button",{onClick:u=>bs(o.id),class:"p-1.5 sm:p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},e[80]||(e[80]=[t("svg",{class:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,$r),T.value[o.id]?(n(),r("div",zr,[o.can_delete?(n(),r("button",{key:0,onClick:u=>{cs(o),T.value[o.id]=!1},class:"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"},e[81]||(e[81]=[t("svg",{class:"w-4 h-4 inline mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),B(" Delete Post ")]),8,Lr)):i("",!0),o.can_delete?i("",!0):(n(),r("button",{key:1,onClick:u=>{ds(o),T.value[o.id]=!1},class:"w-full text-left px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 transition-colors"},e[82]||(e[82]=[t("svg",{class:"w-4 h-4 inline mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1),B(" Report Post ")]),8,Pr))])):i("",!0)])])]),o.media_url?(n(),r("div",Ar,[t("div",Br,[o.content_type==="video"?(n(),r("div",Vr,[(kt=S[o.id])!=null&&kt.unavailable?(n(),r("div",{key:1,class:"w-full bg-gray-900 flex items-center justify-center cursor-pointer",style:{"aspect-ratio":"9/16",height:"85vh","min-height":"600px"},onClick:u=>As(o.id)},[t("div",Hr,[e[84]||(e[84]=t("svg",{class:"w-16 h-16 mx-auto mb-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1)),e[85]||(e[85]=t("p",{class:"text-lg font-medium"},"Video Unavailable",-1)),t("p",Dr,d(o.source==="instagram"?"Instagram video expired":"Video cannot be loaded"),1),e[86]||(e[86]=t("button",{class:"mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors"}," Click to Retry ",-1))])],8,Fr)):(n(),r("video",{key:0,"data-post-id":o.id,poster:o.thumbnail_url||o.media_url,class:"w-full object-cover cursor-pointer",style:{"aspect-ratio":"9/16",height:"85vh","min-height":"600px"},muted:"",loop:"",playsinline:"",preload:"metadata",onClick:he(u=>Ne(o.id),["stop"]),onLoadedmetadata:u=>Ts(o.id),onPlay:u=>S[o.id]&&(S[o.id].playing=!0),onPause:u=>S[o.id]&&(S[o.id].playing=!1),onError:u=>Ps(o.id,u)},[o.video_url&&o.video_url!==o.media_url?(n(),r("source",{key:0,src:o.video_url,type:"video/mp4"},null,8,Tr)):i("",!0),o.media_url?(n(),r("source",{key:1,src:o.media_url,type:"video/mp4"},null,8,Rr)):i("",!0),o.source==="instagram"&&o.external_url?(n(),r("source",{key:2,src:o.external_url,type:"video/mp4"},null,8,Ur)):i("",!0),e[83]||(e[83]=B(" Your browser does not support the video tag. "))],40,Er)),G(t("div",{class:"absolute inset-0 flex items-center justify-center",onClick:he(u=>Ne(o.id),["stop"])},e[87]||(e[87]=[t("div",{class:"bg-black bg-opacity-50 text-white rounded-full p-4 hover:bg-opacity-70 transition-all cursor-pointer"},[t("svg",{class:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"})])],-1)]),8,Ir),[[Ns,!Vs(o.id)]]),e[90]||(e[90]=t("div",{class:"absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs font-medium"},[t("svg",{class:"w-3 h-3 inline mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"})]),B(" Video ")],-1)),t("button",{onClick:e[12]||(e[12]=he(u=>Bs(),["stop"])),class:"absolute bottom-2 right-2 bg-black bg-opacity-70 text-white rounded-full p-2 hover:bg-opacity-80 transition-all z-10"},[Es()?(n(),r("svg",Nr,e[88]||(e[88]=[t("path",{"fill-rule":"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]))):(n(),r("svg",Or,e[89]||(e[89]=[t("path",{"fill-rule":"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.414A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z","clip-rule":"evenodd"},null,-1)])))])])):(n(),r("img",{key:1,src:o.media_url,alt:o.caption,class:"w-full object-cover",style:{"max-height":"600px","min-height":"300px"}},null,8,qr)),o.source==="instagram"&&o.permalink?(n(),r("div",Wr,[t("a",{href:o.permalink,target:"_blank",rel:"noopener noreferrer",class:"inline-flex items-center px-2 py-1 bg-black bg-opacity-70 text-white text-xs font-medium rounded-lg hover:bg-opacity-80 transition-all"},e[91]||(e[91]=[t("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),B(" View on Instagram ")]),8,Gr)])):i("",!0)])])):i("",!0),t("div",Kr,[t("div",Yr,[t("div",Jr,[t("button",{onClick:u=>Xe(o),class:E(["flex items-center space-x-2 text-sm font-medium transition-all duration-200",o.liked?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-6 h-6",fill:o.liked?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[92]||(e[92]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Qr)),t("span",null,d(((_t=o.engagement_metrics)==null?void 0:_t.likes)||0),1)],10,Xr),t("button",{onClick:u=>Ze(o.id),class:"flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-blue-600 transition-all duration-200"},[e[93]||(e[93]=t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),t("span",null,d(((Ct=o.engagement_metrics)==null?void 0:Ct.comments)||0),1)],8,Zr),t("button",{onClick:u=>us(o),class:"flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-purple-600 transition-all duration-200"},e[94]||(e[94]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})],-1),t("span",null,"Share",-1)]),8,en),t("button",{onClick:u=>Qe(o),class:E(["flex items-center space-x-2 text-sm font-medium transition-all duration-200",o.saved?"text-green-600 hover:text-green-700":"text-gray-500 hover:text-green-600"])},[(n(),r("svg",{class:"w-6 h-6",fill:o.saved?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[95]||(e[95]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"},null,-1)]),8,sn)),t("span",null,d(((Mt=o.engagement_metrics)==null?void 0:Mt.saves)||0),1)],10,tn)])])]),o.caption?(n(),r("div",on,[t("div",rn,[xe.value[o.id]?(n(),r("p",an,d(o.caption),1)):(n(),r("p",nn,d(o.caption),1)),o.caption&&o.caption.length>150?(n(),r("button",{key:2,onClick:u=>ss(o.id),class:"text-gray-500 hover:text-gray-700 text-sm font-medium mt-1 transition-colors"},d(xe.value[o.id]?"Show less":"Show more"),9,ln)):i("",!0)]),o.source!=="instagram"&&o.health_topics&&o.health_topics.length>0?(n(),r("div",dn,[(n(!0),r(L,null,A(ae.value[o.id]?o.health_topics:o.health_topics.slice(0,2),u=>(n(),r("span",{key:u,class:"px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-xs font-medium rounded-full"}," #"+d(u),1))),128)),o.health_topics.length>2&&!ae.value[o.id]?(n(),r("button",{key:0,onClick:u=>et(o.id),class:"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"}," +"+d(o.health_topics.length-2)+" more ",9,cn)):i("",!0),o.health_topics.length>2&&ae.value[o.id]?(n(),r("button",{key:1,onClick:u=>et(o.id),class:"px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs font-medium rounded-full transition-colors cursor-pointer"}," Show less ",8,un)):i("",!0)])):i("",!0)])):i("",!0),fe.value[o.id]?(n(),r("div",gn,[t("div",mn,[t("div",pn,[t("div",hn,[t("img",{src:(jt=s.$page.props.auth.user)!=null&&jt.profile_image?s.$page.props.auth.user.profile_image.startsWith("http")?s.$page.props.auth.user.profile_image:`/storage/${s.$page.props.auth.user.profile_image}`:"/images/default-avatar.svg",alt:((St=s.$page.props.auth.user)==null?void 0:St.name)||"You",class:"w-full h-full object-cover"},null,8,vn)]),t("div",fn,[G(t("textarea",{"onUpdate:modelValue":e[13]||(e[13]=u=>O.content=u),onKeydown:Ht(he(u=>st(o.id),["prevent"]),["enter"]),placeholder:"Add a comment...",class:"w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",rows:"2"},null,40,xn),[[ne,O.content]]),t("div",yn,[t("button",{onClick:u=>st(o.id),disabled:!O.content.trim(),class:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Comment ",8,bn)])])])]),t("div",wn,[(n(!0),r(L,null,A($.value[o.id],u=>{var N,$t,zt,Lt,Pt;return n(),r("div",{key:u.id,class:"space-y-3"},[t("div",kn,[t("div",_n,[t("img",{src:(N=u.user)!=null&&N.profile_image?u.user.profile_image.startsWith("http")?u.user.profile_image:`/storage/${u.user.profile_image}`:"/images/default-avatar.svg",alt:(($t=u.user)==null?void 0:$t.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,Cn)]),t("div",Mn,[t("div",jn,[t("div",Sn,[t("p",$n,d(((zt=u.user)==null?void 0:zt.name)||"Anonymous"),1),t("div",zn,[t("p",Ln,d(re(u.created_at)),1),u.user_id===s.$page.props.auth.user.id?(n(),r("div",Pn,[t("button",{onClick:w=>ot(u.id),class:"p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},e[96]||(e[96]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,An),V.value[u.id]?(n(),r("div",Bn,[t("button",{onClick:w=>{rt(o.id,u.id),V.value[u.id]=!1},class:"w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 transition-colors"}," Delete ",8,Vn)])):i("",!0)])):i("",!0)])]),t("p",En,d(u.content),1),t("div",Tn,[t("button",{onClick:w=>nt(o.id,u.id),class:E(["flex items-center space-x-1 transition-colors",u.user_reaction==="like"?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-4 h-4",fill:u.user_reaction==="like"?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[97]||(e[97]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Un)),t("span",null,d(((Lt=u.reaction_counts)==null?void 0:Lt.like)||0),1)],10,Rn),t("button",{onClick:w=>as(u.id),class:"text-gray-500 hover:text-blue-600 transition-colors"}," Reply ",8,Fn)])]),X.value[u.id]?(n(),r("div",Hn,[t("div",Dn,[G(t("textarea",{"onUpdate:modelValue":w=>D.value[u.id]=w,onKeydown:Ht(he(w=>at(o.id,u.id),["prevent"]),["enter"]),placeholder:"Write a reply...",class:"flex-1 p-2 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",rows:"2"},null,40,In),[[ne,D.value[u.id]]]),t("div",Nn,[t("button",{onClick:w=>at(o.id,u.id),disabled:!((Pt=D.value[u.id])!=null&&Pt.trim()),class:"px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Reply ",8,On),t("button",{onClick:w=>ls(u.id),class:"px-3 py-1 bg-gray-200 text-gray-700 text-xs font-medium rounded-lg hover:bg-gray-300 transition-colors"}," Cancel ",8,qn)])])])):i("",!0),u.replies&&u.replies.length>0?(n(),r("div",Wn,[(n(!0),r(L,null,A(u.replies,w=>{var At,Bt,Vt,Et;return n(),r("div",{key:w.id,class:"flex space-x-2"},[t("div",Gn,[t("img",{src:(At=w.user)!=null&&At.profile_image?w.user.profile_image.startsWith("http")?w.user.profile_image:`/storage/${w.user.profile_image}`:"/images/default-avatar.svg",alt:((Bt=w.user)==null?void 0:Bt.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,Kn)]),t("div",Yn,[t("div",Jn,[t("div",Xn,[t("p",Qn,d(((Vt=w.user)==null?void 0:Vt.name)||"Anonymous"),1),t("div",Zn,[t("p",ea,d(re(w.created_at)),1),w.user_id===s.$page.props.auth.user.id?(n(),r("div",ta,[t("button",{onClick:Tt=>ot(w.id),class:"p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},e[98]||(e[98]=[t("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,sa),V.value[w.id]?(n(),r("div",oa,[t("button",{onClick:Tt=>{rt(o.id,w.id),V.value[w.id]=!1},class:"w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 transition-colors"}," Delete ",8,ra)])):i("",!0)])):i("",!0)])]),t("p",na,d(w.content),1),t("div",aa,[t("button",{onClick:Tt=>nt(o.id,w.id),class:E(["flex items-center space-x-1 transition-colors",w.user_reaction==="like"?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-3 h-3",fill:w.user_reaction==="like"?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[99]||(e[99]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,ia)),t("span",null,d(((Et=w.reaction_counts)==null?void 0:Et.like)||0),1)],10,la)])])])])}),128))])):i("",!0)])])])}),128)),!$.value[o.id]||$.value[o.id].length===0?(n(),r("div",da,e[100]||(e[100]=[t("svg",{class:"w-8 h-8 text-gray-300 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1),t("p",{class:"text-sm text-gray-500"},"No comments yet",-1)]))):i("",!0)])])):i("",!0)])}),128))])),Q.value?(n(),r("div",{key:3,class:"text-center py-8",ref_key:"loadMoreTrigger",ref:ye},[g.loading?(n(),r("div",ca,e[101]||(e[101]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1),t("span",{class:"ml-3 text-gray-500"},"Loading more posts...",-1)]))):(n(),r("div",ua," Scroll to load more posts "))],512)):g.posts.length>0?(n(),r("div",ga,e[102]||(e[102]=[t("div",{class:"text-gray-400 text-sm"}," 🎉 You've reached the end! No more posts to load. ",-1)]))):i("",!0)])]),t("div",ma,[t("div",pa,[t("div",ha,[t("button",{onClick:is,class:"w-full p-4 hover:bg-gray-50 rounded-2xl transition-colors flex flex-col items-center text-center"},[e[104]||(e[104]=t("div",{class:"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mb-2 shadow-lg"},[t("svg",{class:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"})])],-1)),t("div",null,[e[103]||(e[103]=t("span",{class:"text-sm font-semibold text-gray-900 block"},"Saved Posts",-1)),t("span",va,d(le.value)+" saved",1)])])]),c.connected&&c.account?(n(),r("div",fa,[e[105]||(e[105]=t("div",{class:"flex items-center space-x-2 mb-3"},[t("svg",{class:"w-4 h-4 text-pink-500",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069z"})]),t("span",{class:"text-sm font-medium text-gray-900"},"Instagram")],-1)),t("div",xa," @"+d(c.account.username),1),t("div",ya,[t("button",{onClick:ft,disabled:c.syncing,class:"w-full px-3 py-2 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},d(c.syncing?"Syncing...":"Sync Content"),9,ba),t("button",{onClick:vt,disabled:c.loading,class:"w-full px-3 py-2 bg-gray-200 text-gray-700 text-xs font-medium rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"}," Disconnect ",8,wa)])])):i("",!0)])])])])])),Y.value?(n(),r("div",ka,[t("div",_a,[t("div",Ca,[t("div",Ma,[e[107]||(e[107]=t("h3",{class:"text-xl font-bold text-gray-900"},"Create Post",-1)),t("button",{onClick:e[14]||(e[14]=o=>Y.value=!1),class:"text-gray-400 hover:text-gray-600 transition-colors"},e[106]||(e[106]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",ja,[t("div",null,[t("div",{class:"flex items-center justify-between mb-2"},[e[109]||(e[109]=t("label",{class:"block text-sm font-medium text-gray-700"},"What's on your mind?",-1)),t("button",{onClick:Yt,class:"flex items-center space-x-2 px-3 py-1.5 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"},e[108]||(e[108]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})],-1),t("span",null,"Write with AI",-1)]))]),G(t("textarea",{"onUpdate:modelValue":e[15]||(e[15]=o=>p.caption=o),onInput:e[16]||(e[16]=o=>ie(o.target.value)),placeholder:"Share your health insights, tips, or experiences...",class:"w-full p-4 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-teal-500 focus:border-transparent",rows:"4"},null,544),[[ne,p.caption]])]),p.suggestedHashtags.length>0?(n(),r("div",Sa,[e[110]||(e[110]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Suggested Hashtags",-1)),t("div",$a,[(n(!0),r(L,null,A(p.suggestedHashtags,o=>(n(),r("button",{key:o,onClick:j=>Qt(o),disabled:p.hashtags.includes(o),class:E(["px-3 py-1 text-sm rounded-full border transition-colors",p.hashtags.includes(o)?"bg-teal-100 text-teal-700 border-teal-300 cursor-not-allowed":"bg-gray-100 text-gray-700 border-gray-300 hover:bg-teal-50 hover:border-teal-300 cursor-pointer"])},d(o),11,za))),128))])])):i("",!0),p.hashtags.length>0?(n(),r("div",La,[e[112]||(e[112]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Selected Hashtags",-1)),t("div",Pa,[(n(!0),r(L,null,A(p.hashtags,o=>(n(),r("span",{key:o,class:"inline-flex items-center px-3 py-1 text-sm bg-teal-100 text-teal-700 rounded-full"},[B(d(o)+" ",1),t("button",{onClick:j=>Zt(o),class:"ml-2 w-4 h-4 text-teal-500 hover:text-teal-700"},e[111]||(e[111]=[t("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Aa)]))),128))])])):i("",!0),t("div",null,[e[115]||(e[115]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Add Image (Optional)",-1)),t("div",Ba,[t("input",{type:"file",onChange:ws,accept:"image/*",class:"hidden",id:"post-media-input"},null,32),t("label",Va,[p.mediaPreview?(n(),r("div",Ea,[t("img",{src:p.mediaPreview,alt:"Preview",class:"max-h-32 mx-auto rounded-lg"},null,8,Ta)])):i("",!0),e[113]||(e[113]=t("svg",{class:"w-8 h-8 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),e[114]||(e[114]=t("p",{class:"text-sm text-gray-500"},"Click to upload an image",-1))])])])]),t("div",Ra,[t("button",{onClick:e[17]||(e[17]=o=>Y.value=!1),class:"px-6 py-2 text-gray-700 font-medium rounded-lg hover:bg-gray-100 transition-colors"}," Cancel "),t("button",{onClick:Gt,disabled:!p.caption.trim(),class:"medroid-post-btn px-6 py-2 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"}," Share Post ",8,Ua)])])])])):i("",!0),pe(a,{show:ve.value,imageFile:ke.value,onCropped:ks,onCancel:_s},null,8,["show","imageFile"]),pe(l,{show:Pe.value,userId:Ae.value,onClose:Ms,onOpenPostDetail:js},null,8,["show","userId"]),Be.value&&b.value?(n(),r("div",Fa,[t("div",Ha,[t("div",{class:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75",onClick:Se}),t("div",Da,[t("div",{class:"flex items-center justify-between mb-6"},[e[117]||(e[117]=t("h3",{class:"text-lg font-medium text-gray-900"},"Post Details",-1)),t("button",{onClick:Se,class:"text-gray-400 hover:text-gray-600 transition-colors"},e[116]||(e[116]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",Ia,[t("div",Na,[t("button",{onClick:e[18]||(e[18]=o=>{var j;oe((j=b.value.user)==null?void 0:j.id),Se()}),class:"w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 hover:border-blue-400 transition-colors"},[t("img",{src:(y=b.value.user)!=null&&y.profile_image?b.value.user.profile_image.startsWith("http")?b.value.user.profile_image:`/storage/${b.value.user.profile_image}`:"/images/default-avatar.svg",alt:((h=b.value.user)==null?void 0:h.name)||"Anonymous",class:"w-full h-full object-cover"},null,8,Oa)]),t("div",null,[t("button",{onClick:e[19]||(e[19]=o=>{var j;oe((j=b.value.user)==null?void 0:j.id),Se()}),class:"font-semibold text-gray-900 hover:text-blue-600 transition-colors text-left"},d(((M=b.value.user)==null?void 0:M.name)||"Anonymous"),1),t("p",qa,d(re(b.value.created_at)),1)])]),b.value.media_url?(n(),r("div",Wa,[t("img",{src:b.value.media_url,alt:b.value.caption,class:"w-full max-h-96 object-contain"},null,8,Ga)])):i("",!0),t("div",Ka,[t("p",Ya,d(b.value.caption),1)]),(_=b.value.health_topics)!=null&&_.length?(n(),r("div",Ja,[(n(!0),r(L,null,A(b.value.health_topics,o=>(n(),r("span",{key:o,class:"px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-xs font-medium rounded-full"},d(o),1))),128))])):i("",!0),t("div",Xa,[t("div",Qa,[t("button",{onClick:e[20]||(e[20]=o=>Xe(b.value)),class:E(["flex items-center space-x-2 text-sm font-medium transition-all duration-200",b.value.liked?"text-red-600 hover:text-red-700":"text-gray-500 hover:text-red-600"])},[(n(),r("svg",{class:"w-5 h-5",fill:b.value.liked?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[118]||(e[118]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),8,Za)),t("span",null,d(((z=b.value.engagement_metrics)==null?void 0:z.likes)||0),1)],2),t("button",{onClick:e[21]||(e[21]=o=>Ze(b.value.id)),class:"flex items-center space-x-2 text-sm font-medium text-gray-500 hover:text-blue-600 transition-colors"},[e[119]||(e[119]=t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),t("span",null,d(((W=b.value.engagement_metrics)==null?void 0:W.comments)||0),1)]),t("button",{onClick:e[22]||(e[22]=o=>Qe(b.value)),class:E(["flex items-center space-x-2 text-sm font-medium transition-colors",b.value.saved?"text-blue-600 hover:text-blue-700":"text-gray-500 hover:text-blue-600"])},[(n(),r("svg",{class:"w-5 h-5",fill:b.value.saved?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24"},e[120]||(e[120]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"},null,-1)]),8,el))],2)])])])])])])):i("",!0),de.value&&te.value&&se.value?(n(),r("div",tl,[t("div",sl,[t("button",{onClick:Ue,class:"absolute top-4 right-4 z-10 w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white hover:bg-opacity-70 transition-colors"},e[121]||(e[121]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),t("div",ol,[(n(!0),r(L,null,A(F.value,(o,j)=>(n(),r("div",{key:o.id,class:"flex-1 h-1 bg-white bg-opacity-30 rounded-full overflow-hidden"},[t("div",{class:"h-full bg-white transition-all duration-100",style:Ft({width:j<P.value?"100%":j===P.value?`${ce.value}%`:"0%"})},null,4)]))),128))]),t("div",rl,[t("div",nl,[t("img",{src:te.value.user_avatar||"/images/default-avatar.svg",alt:te.value.username,class:"w-full h-full object-cover"},null,8,al)]),t("div",null,[t("p",ll,d(te.value.username||"Anonymous"),1),t("p",il,d(re(se.value.created_at)),1)])]),t("div",{class:"w-full h-full flex items-center justify-center bg-gray-900",onMousedown:it,onMouseup:dt,onTouchstart:it,onTouchend:dt},[t("img",{src:se.value.media_url,alt:se.value.caption||"Story",class:"w-full h-full object-contain",onError:e[23]||(e[23]=o=>o.target.src="/images/default-avatar.svg")},null,40,dl)],32),se.value.caption?(n(),r("div",cl,[t("div",ul,[t("p",gl,d(se.value.caption),1)])])):i("",!0),t("div",ml,[t("div",{onClick:ut,class:"w-1/2 h-full cursor-pointer flex items-center justify-start pl-4"},[P.value>0?(n(),r("div",pl,e[122]||(e[122]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)]))):i("",!0)]),t("div",{onClick:Fe,class:"w-1/2 h-full cursor-pointer flex items-center justify-end pr-4"},[P.value<F.value.length-1?(n(),r("div",hl,e[123]||(e[123]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))):i("",!0)])])])])):i("",!0),J.value?(n(),r("div",vl,[t("div",fl,[t("div",xl,[t("div",yl,[e[125]||(e[125]=t("h3",{class:"text-xl font-bold text-gray-900"},"Create Story",-1)),t("button",{onClick:e[24]||(e[24]=o=>J.value=!1),class:"text-gray-400 hover:text-gray-600 transition-colors"},e[124]||(e[124]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",bl,[t("div",null,[e[128]||(e[128]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Add Image/Video",-1)),C.mediaPreview?i("",!0):(n(),r("div",wl,[t("input",{type:"file",accept:"image/*,video/*",class:"hidden",id:"story-media-input",onChange:$s},null,32),e[126]||(e[126]=t("label",{for:"story-media-input",class:"cursor-pointer"},[t("svg",{class:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),t("p",{class:"text-gray-500 mb-2"},"Click to upload media"),t("p",{class:"text-xs text-gray-400"},"Images and videos up to 10MB")],-1))])),C.mediaPreview?(n(),r("div",kl,[t("img",{src:C.mediaPreview,alt:"Story preview",class:"w-full h-64 object-cover rounded-xl"},null,8,_l),t("button",{onClick:gt,class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"},e[127]||(e[127]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):i("",!0)]),t("div",null,[e[129]||(e[129]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Caption (Optional)",-1)),G(t("textarea",{"onUpdate:modelValue":e[25]||(e[25]=o=>C.caption=o),placeholder:"Add a caption to your story...",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:"3",maxlength:"500"},null,512),[[ne,C.caption]]),t("p",Cl,d(C.caption.length)+"/500 characters",1)])]),t("div",Ml,[t("button",{onClick:e[26]||(e[26]=o=>J.value=!1),class:"px-6 py-2 text-gray-700 font-medium rounded-lg hover:bg-gray-100 transition-colors"}," Cancel "),t("button",{onClick:Ss,disabled:!C.media,class:"medroid-story-btn px-6 py-2 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"}," Share Story ",8,jl)])])])])):i("",!0),k.showAiModal?(n(),r("div",Sl,[t("div",$l,[t("div",zl,[t("div",{class:"flex items-center justify-between mb-6"},[e[131]||(e[131]=t("h3",{class:"text-xl font-semibold text-gray-900 flex items-center"},[t("svg",{class:"w-6 h-6 text-blue-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),B(" Write with AI ")],-1)),t("button",{onClick:Xt,class:"text-gray-400 hover:text-gray-600"},e[130]||(e[130]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",Ll,[t("div",null,[e[132]||(e[132]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"What would you like to write about?",-1)),G(t("textarea",{"onUpdate:modelValue":e[27]||(e[27]=o=>k.prompt=o),placeholder:"e.g., 'Write a motivational post about staying healthy during winter' or 'Share tips for managing stress at work'",class:"w-full p-4 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:"3"},null,512),[[ne,k.prompt]])]),t("div",Pl,[t("button",{onClick:Ye,disabled:!k.prompt.trim()||k.isGenerating,class:"flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"},[k.isGenerating?(n(),r("svg",Bl,e[133]||(e[133]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):i("",!0),B(" "+d(k.isGenerating?"Generating...":"Generate Content"),1)],8,Al)]),k.generatedContent?(n(),r("div",Vl,[t("div",null,[e[134]||(e[134]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Generated Content",-1)),t("div",El,[t("p",Tl,d(k.generatedContent),1)])]),t("div",Rl,[t("button",{onClick:Jt,class:"flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"}," Use This Content "),t("button",{onClick:Ye,disabled:k.isGenerating,class:"flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"}," Regenerate ",8,Ul)])])):i("",!0)])])])])):i("",!0),Ve.value?(n(),r("div",Fl,[t("div",Hl,[t("div",Dl,[e[136]||(e[136]=t("h3",{class:"text-lg font-semibold text-gray-900"},"Notifications",-1)),t("div",Il,[we.value>0?(n(),r("button",{key:0,onClick:rs,class:"text-sm text-blue-600 hover:text-blue-700 font-medium"}," Mark all read ")):i("",!0),t("button",{onClick:e[28]||(e[28]=o=>Ve.value=!1),class:"text-gray-400 hover:text-gray-600 transition-colors"},e[135]||(e[135]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),t("div",Nl,[R.value.length===0?(n(),r("div",Ol,e[137]||(e[137]=[t("svg",{class:"w-12 h-12 text-gray-300 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-3.5-3.5a8.38 8.38 0 01-1.5-5V6a6 6 0 10-12 0v2.5a8.38 8.38 0 01-1.5 5L5 17h5m5 0v1a3 3 0 11-6 0v-1m6 0H9"})],-1),t("p",{class:"text-gray-500"},"No notifications yet",-1),t("p",{class:"text-sm text-gray-400 mt-1"},"Your activity will appear here",-1)]))):(n(),r("div",ql,[(n(!0),r(L,null,A(R.value,o=>(n(),r("div",{key:o.id,onClick:j=>os(o.id),class:E(["p-4 hover:bg-gray-50 cursor-pointer transition-colors",{"bg-blue-50":!o.read}])},[t("div",Gl,[t("div",Kl,[o.type==="like"?(n(),r("div",Yl,e[138]||(e[138]=[t("svg",{class:"w-4 h-4 text-red-600",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})],-1)]))):o.type==="comment"?(n(),r("div",Jl,e[139]||(e[139]=[t("svg",{class:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)]))):o.type==="save"?(n(),r("div",Xl,e[140]||(e[140]=[t("svg",{class:"w-4 h-4 text-purple-600",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"})],-1)]))):o.type==="share"?(n(),r("div",Ql,e[141]||(e[141]=[t("svg",{class:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})],-1)]))):i("",!0)]),t("div",Zl,[t("p",ei,d(o.message),1),t("p",ti,d(o.post_title),1),t("p",si,d(re(o.timestamp)),1)]),o.post_image?(n(),r("div",oi,[t("img",{src:o.post_image,alt:"Post",class:"w-10 h-10 rounded object-cover"},null,8,ri)])):i("",!0),o.read?i("",!0):(n(),r("div",ni,e[142]||(e[142]=[t("div",{class:"w-2 h-2 bg-blue-600 rounded-full"},null,-1)])))])],10,Wl))),128))]))])])])):i("",!0),t("div",ai,[(n(!0),r(L,null,A(be.value,o=>(n(),r("div",{key:o.id,class:E(["max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 ease-in-out",{"border-l-4 border-green-500":o.type==="success","border-l-4 border-red-500":o.type==="error","border-l-4 border-blue-500":o.type==="info","border-l-4 border-yellow-500":o.type==="warning"}])},[t("div",li,[t("div",ii,[t("div",di,[o.type==="success"?(n(),r("svg",ci,e[143]||(e[143]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):o.type==="error"?(n(),r("svg",ui,e[144]||(e[144]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):o.type==="info"?(n(),r("svg",gi,e[145]||(e[145]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))):(n(),r("svg",mi,e[146]||(e[146]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"},null,-1)])))]),t("div",pi,[t("p",hi,d(o.message),1)]),t("div",vi,[t("button",{onClick:j=>tt(o.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},e[147]||(e[147]=[t("span",{class:"sr-only"},"Close",-1),t("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,fi)])])])],2))),128))]),f.show?(n(),r("div",xi,[t("div",yi,[t("div",{class:"flex items-center justify-between p-6 border-b border-gray-100"},[e[149]||(e[149]=t("h3",{class:"text-lg font-semibold text-gray-900"},"Share Post",-1)),t("button",{onClick:gs,class:"p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"},e[148]||(e[148]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),f.post?(n(),r("div",bi,[t("div",wi,[t("div",ki,[t("div",_i,[t("span",Ci,d(f.post.source==="instagram"?"IG":((yt=(Le=f.post.user)==null?void 0:Le.name)==null?void 0:yt.charAt(0))||"U"),1)]),t("div",null,[t("p",Mi,d(f.post.source==="instagram"?`@${f.post.instagram_username||f.post.username}`:((bt=f.post.user)==null?void 0:bt.name)||"Anonymous"),1),t("p",ji,d(f.post.source==="instagram"?"Instagram":"Medroid AI Community"),1)])]),f.post.media_url?(n(),r("div",Si,[t("img",{src:f.post.media_url,alt:f.post.caption||"Post image",class:"w-full h-32 object-cover rounded-lg"},null,8,$i)])):i("",!0),f.post.caption?(n(),r("p",zi,d(f.post.caption),1)):i("",!0)])])):i("",!0),t("div",{class:"p-6"},[e[155]||(e[155]=t("h4",{class:"text-sm font-medium text-gray-900 mb-4"},"Share via",-1)),t("div",{class:"grid grid-cols-2 gap-3 mb-4"},[t("button",{onClick:hs,class:"flex items-center justify-center space-x-3 p-4 bg-green-50 hover:bg-green-100 rounded-xl transition-colors group"},e[150]||(e[150]=[t("div",{class:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.700"})])],-1),t("span",{class:"text-sm font-medium text-gray-700 group-hover:text-green-700"},"WhatsApp",-1)])),t("button",{onClick:vs,class:"flex items-center justify-center space-x-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors group"},e[151]||(e[151]=[t("div",{class:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})])],-1),t("span",{class:"text-sm font-medium text-gray-700 group-hover:text-blue-700"},"LinkedIn",-1)]))]),t("div",{class:"grid grid-cols-2 gap-3 mb-4"},[t("button",{onClick:fs,class:"flex items-center justify-center space-x-3 p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors group"},e[152]||(e[152]=[t("div",{class:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})])],-1),t("span",{class:"text-sm font-medium text-gray-700 group-hover:text-gray-800"},"Email",-1)])),t("button",{onClick:xs,class:"flex items-center justify-center space-x-3 p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors group"},e[153]||(e[153]=[t("div",{class:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.255-.949L5 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"})])],-1),t("span",{class:"text-sm font-medium text-gray-700 group-hover:text-gray-800"},"SMS",-1)]))]),t("button",{onClick:ys,class:"w-full flex items-center justify-center space-x-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors group mb-4"},e[154]||(e[154]=[t("svg",{class:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})],-1),t("span",{class:"text-sm font-medium text-purple-700"},"Copy Link",-1)])),e[156]||(e[156]=t("div",{class:"text-center"},[t("button",{class:"text-sm text-gray-500 hover:text-gray-700 transition-colors"}," More sharing options ")],-1))])])])):i("",!0)]}),_:1})],64)}}},Ri=Ws(Li,[["__scopeId","data-v-15288f72"]]);export{Ri as default};
