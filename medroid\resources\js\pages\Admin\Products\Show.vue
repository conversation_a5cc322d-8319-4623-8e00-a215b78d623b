<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { computed, ref, onMounted } from 'vue';

const page = usePage();
const product = computed(() => page.props.product);

// Get current user and determine if this is a provider route
const currentUser = computed(() => page.props.auth?.user);
const isProvider = computed(() => currentUser.value?.role === 'provider');

// Dynamic breadcrumbs and URLs based on user role
const breadcrumbs = computed(() => {
    if (isProvider.value) {
        return [
            { title: 'Dashboard', href: '/dashboard' },
            { title: 'My Products', href: '/provider/products' },
            { title: 'Product Details', href: '#' },
        ];
    } else {
        return [
            { title: 'Dashboard', href: '/dashboard' },
            { title: 'Products', href: '/admin/products' },
            { title: 'Product Details', href: '#' },
        ];
    }
});

const backUrl = computed(() => isProvider.value ? '/provider/products' : '/admin/products');
const editUrl = computed(() => isProvider.value ? `/provider/products/${product.value.id}/edit` : `/admin/products/${product.value.id}/edit`);

const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
};

const getStatusBadgeClass = (isActive) => {
    return isActive
        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
};

const getTypeBadgeClass = (type) => {
    return type === 'physical'
        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
        : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
};

// Reviews functionality
const reviews = ref([]);
const reviewStats = ref({});
const loading = ref(false);
const showReviewForm = ref(false);
const canReview = ref(false);
const reviewForm = ref({
    rating: 5,
    title: '',
    comment: ''
});

const fetchReviews = async () => {
    loading.value = true;
    try {
        const response = await window.axios.get(`/shop/products/${product.value.id}/reviews`);
        reviews.value = response.data.reviews.data;
        reviewStats.value = {
            average_rating: response.data.average_rating,
            total_reviews: response.data.total_reviews,
            rating_distribution: response.data.rating_distribution
        };
    } catch (error) {
        console.error('Error fetching reviews:', error);
    } finally {
        loading.value = false;
    }
};

const checkCanReview = async () => {
    try {
        const response = await window.axios.get(`/shop/products/${product.value.id}/can-review`);
        canReview.value = response.data.can_review;
    } catch (error) {
        console.error('Error checking review permission:', error);
    }
};

const submitReview = async () => {
    try {
        const response = await window.axios.post(`/shop/products/${product.value.id}/reviews`, reviewForm.value);
        reviews.value.unshift(response.data.review);
        showReviewForm.value = false;
        reviewForm.value = { rating: 5, title: '', comment: '' };
        canReview.value = false;
        await fetchReviews(); // Refresh to get updated stats
        alert('Review submitted successfully!');
    } catch (error) {
        console.error('Error submitting review:', error);
        if (error.response?.data?.message) {
            alert(error.response.data.message);
        } else {
            alert('Failed to submit review. Please try again.');
        }
    }
};

const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
        stars.push(i <= rating ? '★' : '☆');
    }
    return stars.join('');
};

onMounted(() => {
    fetchReviews();
    checkCanReview();
});
</script>

<template>
    <Head :title="`Product: ${product.name}`" />

    <AppLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Product Details
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" :href="breadcrumb.href" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <div class="flex space-x-3">
                    <Link :href="editUrl" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Product
                    </Link>
                    <Link :href="backUrl" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Products
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Product Images -->
                            <div>
                                <h3 class="text-lg font-semibold mb-4">Product Images</h3>
                                <div v-if="product.images && product.images.length > 0" class="space-y-4">
                                    <div v-for="image in product.images" :key="image.id" class="relative">
                                        <img
                                            :src="image.full_url || (image.image_path?.startsWith('http') ? image.image_path : `/storage/${image.image_path}`)"
                                            :alt="product.name"
                                            class="w-full h-64 object-cover rounded-lg shadow-md"
                                        >
                                        <span v-if="image.is_primary" class="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs">
                                            Primary
                                        </span>
                                    </div>
                                </div>
                                <div v-else class="flex items-center justify-center h-64 bg-gray-200 dark:bg-gray-700 rounded-lg">
                                    <div class="text-center">
                                        <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500 dark:text-gray-400">No images available</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Product Details -->
                            <div>
                                <h3 class="text-lg font-semibold mb-4">Product Information</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ product.name }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">SKU</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ product.sku }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ product.category?.name || 'N/A' }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Type</label>
                                        <span :class="getTypeBadgeClass(product.type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
                                            {{ product.type }}
                                        </span>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                                        <span :class="getStatusBadgeClass(product.is_active)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
                                            {{ product.is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Price</label>
                                        <div class="mt-1">
                                            <span class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ formatPrice(product.price) }}</span>
                                            <span v-if="product.sale_price" class="text-sm text-gray-500 line-through ml-2">
                                                {{ formatPrice(product.sale_price) }}
                                            </span>
                                        </div>
                                    </div>

                                    <div v-if="product.description">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                                        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">{{ product.description }}</div>
                                    </div>

                                    <div v-if="product.type === 'physical'">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stock Quantity</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ product.stock_quantity || 0 }}</p>
                                    </div>

                                    <div v-if="product.weight">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Weight</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ product.weight }} {{ product.weight_unit || 'kg' }}</p>
                                    </div>

                                    <div v-if="product.dimensions">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dimensions</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ product.dimensions }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Created</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ new Date(product.created_at).toLocaleDateString() }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ new Date(product.updated_at).toLocaleDateString() }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div class="mt-8 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-semibold">Customer Reviews</h3>
                            <button
                                v-if="canReview"
                                @click="showReviewForm = true"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Write a Review
                            </button>
                        </div>

                        <!-- Review Stats -->
                        <div v-if="reviewStats.total_reviews > 0" class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex items-center space-x-4">
                                <div class="text-center">
                                    <div class="text-3xl font-bold">{{ reviewStats.average_rating?.toFixed(1) || '0.0' }}</div>
                                    <div class="text-yellow-400 text-xl">{{ renderStars(Math.round(reviewStats.average_rating || 0)) }}</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ reviewStats.total_reviews }} reviews</div>
                                </div>
                                <div class="flex-1">
                                    <div v-for="(count, rating) in reviewStats.rating_distribution" :key="rating" class="flex items-center space-x-2 mb-1">
                                        <span class="text-sm w-8">{{ rating }}★</span>
                                        <div class="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                            <div
                                                class="bg-yellow-400 h-2 rounded-full"
                                                :style="{ width: reviewStats.total_reviews > 0 ? (count / reviewStats.total_reviews * 100) + '%' : '0%' }"
                                            ></div>
                                        </div>
                                        <span class="text-sm w-8">{{ count }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Review Form -->
                        <div v-if="showReviewForm" class="mb-6 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                            <h4 class="text-lg font-semibold mb-4">Write Your Review</h4>
                            <form @submit.prevent="submitReview" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rating</label>
                                    <div class="flex space-x-1">
                                        <button
                                            v-for="star in 5"
                                            :key="star"
                                            type="button"
                                            @click="reviewForm.rating = star"
                                            class="text-2xl focus:outline-none"
                                            :class="star <= reviewForm.rating ? 'text-yellow-400' : 'text-gray-300'"
                                        >
                                            ★
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title (Optional)</label>
                                    <input
                                        v-model="reviewForm.title"
                                        type="text"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                        placeholder="Brief summary of your review"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Comment</label>
                                    <textarea
                                        v-model="reviewForm.comment"
                                        rows="4"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                        placeholder="Share your experience with this product"
                                    ></textarea>
                                </div>
                                <div class="flex space-x-3">
                                    <button
                                        type="submit"
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                    >
                                        Submit Review
                                    </button>
                                    <button
                                        type="button"
                                        @click="showReviewForm = false"
                                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Reviews List -->
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="reviews.length === 0" class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No reviews yet. Be the first to review this product!</p>
                        </div>

                        <div v-else class="space-y-4">
                            <div v-for="review in reviews" :key="review.id" class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                <div class="flex justify-between items-start mb-2">
                                    <div>
                                        <div class="flex items-center space-x-2">
                                            <span class="font-semibold">{{ review.user_name }}</span>
                                            <span v-if="review.is_verified_purchase" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                                Verified Purchase
                                            </span>
                                        </div>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <span class="text-yellow-400">{{ renderStars(review.rating) }}</span>
                                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ review.formatted_date }}</span>
                                        </div>
                                    </div>
                                </div>
                                <h4 v-if="review.title" class="font-semibold mb-2">{{ review.title }}</h4>
                                <p class="text-gray-700 dark:text-gray-300">{{ review.comment }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
