<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Allow cleartext traffic for local development -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Local development IPs -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">*************</domain>
        <!-- Common local network ranges -->
        <domain includeSubdomains="true">10.0.0.0/8</domain>
        <domain includeSubdomains="true">***********/16</domain>
        <domain includeSubdomains="true">**********/12</domain>
        <!-- Development tunnels -->
        <domain includeSubdomains="true">ngrok.io</domain>
        <domain includeSubdomains="true">ngrok-free.app</domain>
    </domain-config>
    
    <!-- Production domains - Allow both HTTP and HTTPS for Cloudflare tunnel -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">api.medroid.ai</domain>
        <domain includeSubdomains="true">medroid.ai</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>
</network-security-config>
