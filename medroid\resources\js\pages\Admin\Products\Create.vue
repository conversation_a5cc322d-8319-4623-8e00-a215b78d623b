<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import FilePickerButton from '@/components/FilePickerButton.vue';

// Get current user and determine if this is a provider route
const page = usePage();
const currentUser = computed(() => page.props.auth?.user);
const isProvider = computed(() => currentUser.value?.role === 'provider');

// Dynamic breadcrumbs and URLs based on user role
const breadcrumbs = computed(() => {
    if (isProvider.value) {
        return [
            { title: 'Dashboard', href: '/dashboard' },
            { title: 'My Products', href: '/provider/products' },
            { title: 'Create Product', href: '/provider/products/create' },
        ];
    } else {
        return [
            { title: 'Dashboard', href: '/dashboard' },
            { title: 'Products', href: '/admin/products' },
            { title: 'Create Product', href: '/admin/products/create' },
        ];
    }
});

const apiPrefix = computed(() => isProvider.value ? '/provider' : '/admin');
const backUrl = computed(() => isProvider.value ? '/provider/products' : '/admin/products');

const loading = ref(false);
const categories = ref([]);
const form = ref({
    name: '',
    description: '',
    short_description: '',
    type: 'physical',
    category_id: '',
    price: '',
    sale_price: '',
    sku: '',
    stock_quantity: '',
    manage_stock: true,
    weight: '',
    dimensions: '',
    is_featured: false,
    is_active: true,
    digital_files: [],
    download_limit: '',
    download_expiry_days: ''
});

const featuredImageFile = ref(null);
const galleryImageFiles = ref([]);

const errors = ref({});

const fetchCategories = async () => {
    try {
        const response = await window.axios.get(`${apiPrefix.value}/products/create`);
        categories.value = response.data.categories || [];
    } catch (error) {
        console.error('Error fetching categories:', error);
    }
};

// File picker handlers will be handled by the FilePickerButton component

const submitForm = async () => {
    loading.value = true;
    errors.value = {};

    try {
        // Create FormData for file upload
        const formData = new FormData();

        // Add all form fields
        Object.keys(form.value).forEach(key => {
            if (form.value[key] !== null && form.value[key] !== '') {
                if (Array.isArray(form.value[key])) {
                    form.value[key].forEach((item, index) => {
                        formData.append(`${key}[${index}]`, item);
                    });
                } else {
                    // Convert boolean values to 1/0 for Laravel validation
                    if (typeof form.value[key] === 'boolean') {
                        formData.append(key, form.value[key] ? '1' : '0');
                    } else {
                        formData.append(key, form.value[key]);
                    }
                }
            }
        });

        // Ensure boolean fields are always included (even if false)
        const booleanFields = ['manage_stock', 'is_featured', 'is_active'];
        booleanFields.forEach(field => {
            if (!formData.has(field)) {
                formData.append(field, form.value[field] ? '1' : '0');
            }
        });

        // Add featured image file ID
        if (featuredImageFile.value) {
            formData.append('featured_image_id', featuredImageFile.value.id);
        }

        // Add gallery image file IDs
        galleryImageFiles.value.forEach((file, index) => {
            formData.append(`gallery_image_ids[${index}]`, file.id);
        });

        const response = await window.axios.post(`${apiPrefix.value}/save-product`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });

        if (response.data.success) {
            // Redirect to products list
            window.location.href = backUrl.value;
        } else {
            alert('Error creating product: ' + response.data.message);
        }
    } catch (error) {
        if (error.response?.data?.errors) {
            errors.value = error.response.data.errors;
        } else {
            alert('Error creating product: ' + (error.response?.data?.message || error.message));
        }
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchCategories();
});
</script>

<template>
    <Head title="Create Product" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Create Product
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <Link :href="backUrl" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Products
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <form @submit.prevent="submitForm" class="space-y-6">
                            <!-- Basic Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Product Name *</label>
                                    <input 
                                        v-model="form.name"
                                        type="text" 
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">SKU *</label>
                                    <input 
                                        v-model="form.sku"
                                        type="text" 
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <p v-if="errors.sku" class="mt-1 text-sm text-red-600">{{ errors.sku[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category *</label>
                                    <select 
                                        v-model="form.category_id"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                        <option value="">Select Category</option>
                                        <option v-for="category in categories" :key="category.id" :value="category.id">
                                            {{ category.name }}
                                        </option>
                                    </select>
                                    <p v-if="errors.category_id" class="mt-1 text-sm text-red-600">{{ errors.category_id[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Type *</label>
                                    <select 
                                        v-model="form.type"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                        <option value="physical">Physical</option>
                                        <option value="digital">Digital</option>
                                    </select>
                                    <p v-if="errors.type" class="mt-1 text-sm text-red-600">{{ errors.type[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Price *</label>
                                    <input 
                                        v-model="form.price"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <p v-if="errors.price" class="mt-1 text-sm text-red-600">{{ errors.price[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sale Price</label>
                                    <input 
                                        v-model="form.sale_price"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <p v-if="errors.sale_price" class="mt-1 text-sm text-red-600">{{ errors.sale_price[0] }}</p>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description *</label>
                                <textarea 
                                    v-model="form.description"
                                    rows="4"
                                    required
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                ></textarea>
                                <p v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description[0] }}</p>
                            </div>

                            <!-- Short Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Short Description</label>
                                <textarea
                                    v-model="form.short_description"
                                    rows="2"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                ></textarea>
                                <p v-if="errors.short_description" class="mt-1 text-sm text-red-600">{{ errors.short_description[0] }}</p>
                            </div>

                            <!-- Product Images -->
                            <div class="space-y-6">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Product Images</h3>

                                <!-- Featured Image -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Featured Image</label>
                                    <FilePickerButton
                                        v-model="featuredImageFile"
                                        :multiple="false"
                                        accepted-types="image/*"
                                        accepted-types-text="PNG, JPG, GIF up to 10MB"
                                        category="product_images"
                                        button-text="Choose Featured Image"
                                    />
                                    <p v-if="errors.featured_image" class="mt-1 text-sm text-red-600">{{ errors.featured_image[0] }}</p>
                                </div>

                                <!-- Gallery Images -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Gallery Images</label>
                                    <FilePickerButton
                                        v-model="galleryImageFiles"
                                        :multiple="true"
                                        accepted-types="image/*"
                                        accepted-types-text="PNG, JPG, GIF up to 10MB each. You can select multiple images."
                                        category="product_images"
                                        button-text="Choose Gallery Images"
                                    />
                                    <p v-if="errors.gallery_images" class="text-sm text-red-600">{{ errors.gallery_images[0] }}</p>
                                </div>
                            </div>

                            <!-- Physical Product Fields -->
                            <div v-if="form.type === 'physical'" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stock Quantity</label>
                                    <input 
                                        v-model="form.stock_quantity"
                                        type="number" 
                                        min="0"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <p v-if="errors.stock_quantity" class="mt-1 text-sm text-red-600">{{ errors.stock_quantity[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Weight (kg)</label>
                                    <input 
                                        v-model="form.weight"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <p v-if="errors.weight" class="mt-1 text-sm text-red-600">{{ errors.weight[0] }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dimensions</label>
                                    <input 
                                        v-model="form.dimensions"
                                        type="text" 
                                        placeholder="L x W x H"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <p v-if="errors.dimensions" class="mt-1 text-sm text-red-600">{{ errors.dimensions[0] }}</p>
                                </div>
                            </div>

                            <!-- Checkboxes -->
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input 
                                        v-model="form.is_featured"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Featured Product</span>
                                </label>

                                <label class="flex items-center">
                                    <input 
                                        v-model="form.is_active"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Active</span>
                                </label>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end space-x-3">
                                <Link :href="backUrl" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                    Cancel
                                </Link>
                                <button 
                                    type="submit"
                                    :disabled="loading"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                                >
                                    {{ loading ? 'Creating...' : 'Create Product' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
