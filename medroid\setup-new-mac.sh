#!/bin/bash

# 🏥 Medroid.ai - Complete Development Environment Setup for New Mac
# This script sets up everything needed for Medroid development with Cloudflare Tunnel

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
REPO_URL="https://github.com/majetyanupam/medroid_app_backend_v1.0.git"
BRANCH="disocver"
PROJECT_DIR="medroid_app_backend_v1.0"
TUNNEL_NAME="medroid-backend"
DOMAIN="api.medroid.ai"

echo -e "${PURPLE}🏥 Medroid.ai Development Environment Setup${NC}"
echo -e "${BLUE}🚀 Setting up complete development environment on new Mac${NC}"
echo -e "${CYAN}📋 This will install and configure everything you need${NC}\n"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Homebrew
install_homebrew() {
    if ! command_exists brew; then
        echo -e "${YELLOW}📦 Installing Homebrew...${NC}"
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        
        # Add Homebrew to PATH for Apple Silicon Macs
        if [[ $(uname -m) == "arm64" ]]; then
            echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
            eval "$(/opt/homebrew/bin/brew shellenv)"
        fi
        
        echo -e "${GREEN}✅ Homebrew installed successfully${NC}"
    else
        echo -e "${GREEN}✅ Homebrew already installed${NC}"
    fi
}

# Function to install Node.js
install_nodejs() {
    if ! command_exists node; then
        echo -e "${YELLOW}📦 Installing Node.js...${NC}"
        brew install node
        echo -e "${GREEN}✅ Node.js installed successfully${NC}"
    else
        echo -e "${GREEN}✅ Node.js already installed ($(node --version))${NC}"
    fi
}

# Function to install PHP
install_php() {
    if ! command_exists php; then
        echo -e "${YELLOW}📦 Installing PHP...${NC}"
        brew install php
        echo -e "${GREEN}✅ PHP installed successfully${NC}"
    else
        echo -e "${GREEN}✅ PHP already installed ($(php --version | head -n1))${NC}"
    fi
}

# Function to install Composer
install_composer() {
    if ! command_exists composer; then
        echo -e "${YELLOW}📦 Installing Composer...${NC}"
        brew install composer
        echo -e "${GREEN}✅ Composer installed successfully${NC}"
    else
        echo -e "${GREEN}✅ Composer already installed ($(composer --version))${NC}"
    fi
}

# Function to install Git
install_git() {
    if ! command_exists git; then
        echo -e "${YELLOW}📦 Installing Git...${NC}"
        brew install git
        echo -e "${GREEN}✅ Git installed successfully${NC}"
    else
        echo -e "${GREEN}✅ Git already installed ($(git --version))${NC}"
    fi
}

# Function to install Cloudflared
install_cloudflared() {
    if ! command_exists cloudflared; then
        echo -e "${YELLOW}📦 Installing Cloudflared...${NC}"
        brew install cloudflared
        echo -e "${GREEN}✅ Cloudflared installed successfully${NC}"
    else
        echo -e "${GREEN}✅ Cloudflared already installed ($(cloudflared --version))${NC}"
    fi
}

# Function to clone repository
clone_repository() {
    if [ ! -d "$PROJECT_DIR" ]; then
        echo -e "${YELLOW}📥 Cloning Medroid repository...${NC}"
        git clone -b "$BRANCH" "$REPO_URL" "$PROJECT_DIR"
        echo -e "${GREEN}✅ Repository cloned successfully${NC}"
    else
        echo -e "${GREEN}✅ Repository already exists${NC}"
        cd "$PROJECT_DIR"
        git pull origin "$BRANCH"
        echo -e "${GREEN}✅ Repository updated${NC}"
        cd ..
    fi
}

# Function to setup Laravel environment
setup_laravel() {
    echo -e "${YELLOW}🔧 Setting up Laravel environment...${NC}"
    cd "$PROJECT_DIR"
    
    # Install PHP dependencies
    composer install
    
    # Install Node dependencies
    npm install
    
    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ]; then
        cp .env.example .env
        php artisan key:generate
    fi
    
    echo -e "${GREEN}✅ Laravel environment setup complete${NC}"
    cd ..
}

# Function to setup Cloudflare Tunnel
setup_cloudflare_tunnel() {
    echo -e "${YELLOW}🌐 Setting up Cloudflare Tunnel...${NC}"
    
    # Check if already logged in
    if ! cloudflared tunnel list &> /dev/null; then
        echo -e "${CYAN}🔐 Please login to Cloudflare...${NC}"
        echo -e "${YELLOW}A browser window will open. Please complete the login process.${NC}"
        cloudflared tunnel login
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Cloudflare login successful${NC}"
        else
            echo -e "${RED}❌ Cloudflare login failed${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ Already logged in to Cloudflare${NC}"
    fi
    
    # Check if tunnel exists
    if ! cloudflared tunnel list | grep -q "$TUNNEL_NAME"; then
        echo -e "${YELLOW}📝 Creating tunnel: $TUNNEL_NAME${NC}"
        cloudflared tunnel create "$TUNNEL_NAME"
        echo -e "${GREEN}✅ Tunnel created successfully${NC}"
    else
        echo -e "${GREEN}✅ Tunnel already exists${NC}"
    fi
    
    # Setup DNS record
    echo -e "${YELLOW}🌐 Setting up DNS record for $DOMAIN${NC}"
    cloudflared tunnel route dns "$TUNNEL_NAME" "$DOMAIN"
    echo -e "${GREEN}✅ DNS record configured${NC}"
    
    # Get tunnel ID and create config
    TUNNEL_ID=$(cloudflared tunnel list | grep "$TUNNEL_NAME" | awk '{print $1}')
    
    # Create configuration file
    mkdir -p ~/.cloudflared
    cat > ~/.cloudflared/config.yml << EOF
tunnel: $TUNNEL_ID
credentials-file: /Users/<USER>/.cloudflared/$TUNNEL_ID.json

ingress:
  - hostname: $DOMAIN
    service: http://localhost:8000
  - service: http_status:404
EOF
    
    echo -e "${GREEN}✅ Cloudflare Tunnel configuration complete${NC}"
}

# Function to create helpful aliases
create_aliases() {
    echo -e "${YELLOW}🔧 Creating helpful aliases...${NC}"
    
    # Add to shell profile
    SHELL_PROFILE=""
    if [ -f ~/.zshrc ]; then
        SHELL_PROFILE=~/.zshrc
    elif [ -f ~/.bash_profile ]; then
        SHELL_PROFILE=~/.bash_profile
    else
        SHELL_PROFILE=~/.zshrc
        touch "$SHELL_PROFILE"
    fi
    
    # Add Medroid aliases if they don't exist
    if ! grep -q "# Medroid Development Aliases" "$SHELL_PROFILE"; then
        cat >> "$SHELL_PROFILE" << 'EOF'

# Medroid Development Aliases
alias medroid-start="cd ~/medroid_app_backend_v1.0 && cloudflared tunnel run medroid-backend"
alias medroid-dev="cd ~/medroid_app_backend_v1.0 && npm run dev"
alias medroid-serve="cd ~/medroid_app_backend_v1.0 && php artisan serve"
alias medroid-test="cd ~/medroid_app_backend_v1.0 && ./test-medroid-tunnel.sh"
alias medroid-update="cd ~/medroid_app_backend_v1.0 && ./update-medroid-env.sh"
alias medroid-cd="cd ~/medroid_app_backend_v1.0"
EOF
        echo -e "${GREEN}✅ Helpful aliases added to $SHELL_PROFILE${NC}"
    else
        echo -e "${GREEN}✅ Aliases already exist${NC}"
    fi
}

# Main installation process
main() {
    echo -e "${PURPLE}🚀 Starting Medroid.ai Development Environment Setup${NC}\n"
    
    # Install prerequisites
    echo -e "${BLUE}📦 Installing Prerequisites...${NC}"
    install_homebrew
    install_git
    install_nodejs
    install_php
    install_composer
    install_cloudflared
    
    echo -e "\n${BLUE}📥 Setting up Project...${NC}"
    clone_repository
    setup_laravel
    
    echo -e "\n${BLUE}🌐 Setting up Cloudflare Tunnel...${NC}"
    setup_cloudflare_tunnel
    
    echo -e "\n${BLUE}🔧 Final Configuration...${NC}"
    create_aliases
    
    # Final instructions
    echo -e "\n${GREEN}🎉 Setup Complete! Your Medroid development environment is ready!${NC}"
    echo -e "\n${PURPLE}📋 Quick Start Commands:${NC}"
    echo -e "${CYAN}medroid-cd${NC}     - Navigate to project directory"
    echo -e "${CYAN}medroid-start${NC}  - Start Cloudflare tunnel"
    echo -e "${CYAN}medroid-dev${NC}    - Start Vite development server"
    echo -e "${CYAN}medroid-serve${NC}  - Start Laravel server"
    echo -e "${CYAN}medroid-test${NC}   - Test tunnel connectivity"
    
    echo -e "\n${YELLOW}🌐 Your application will be available at: https://$DOMAIN${NC}"
    echo -e "\n${BLUE}💡 To start development:${NC}"
    echo -e "1. Open terminal and run: ${CYAN}medroid-start${NC}"
    echo -e "2. Open another terminal and run: ${CYAN}medroid-dev${NC}"
    echo -e "3. Visit: ${CYAN}https://$DOMAIN${NC}"
    
    echo -e "\n${GREEN}✨ Happy coding! ✨${NC}"
}

# Run main function
main "$@"
