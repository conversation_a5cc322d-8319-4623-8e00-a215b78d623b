import{z as L,c as x,r as y,o as z,d as o,e as l,f as b,u as w,m as J,g as v,i as t,j as W,n as i,l as p,v as g,t as d,F as U,p as M,q as E,s as G,P as F,x as q,y as H}from"./vendor-BhKTHoN5.js";import{_ as K}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{F as S}from"./FilePickerButton-BUMxh5r7.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const O={class:"flex items-center justify-between"},Q={class:"flex mt-2","aria-label":"Breadcrumb"},Y={class:"inline-flex items-center space-x-1 md:space-x-3"},R={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},X={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},Z={class:"py-12"},ee={class:"mx-auto max-w-4xl sm:px-6 lg:px-8"},te={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},re={class:"p-6 text-gray-900 dark:text-gray-100"},ae={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},se={key:0,class:"mt-1 text-sm text-red-600"},le={key:0,class:"mt-1 text-sm text-red-600"},oe=["value"],de={key:0,class:"mt-1 text-sm text-red-600"},ue={key:0,class:"mt-1 text-sm text-red-600"},ie={key:0,class:"mt-1 text-sm text-red-600"},ne={key:0,class:"mt-1 text-sm text-red-600"},me={key:0,class:"mt-1 text-sm text-red-600"},pe={key:0,class:"mt-1 text-sm text-red-600"},ge={class:"space-y-6"},ce={key:0,class:"mt-1 text-sm text-red-600"},ye={key:0,class:"text-sm text-red-600"},be={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},xe={key:0,class:"mt-1 text-sm text-red-600"},ve={key:0,class:"mt-1 text-sm text-red-600"},fe={key:0,class:"mt-1 text-sm text-red-600"},ke={class:"flex items-center space-x-6"},we={class:"flex items-center"},he={class:"flex items-center"},_e={class:"flex justify-end space-x-3"},Pe=["disabled"],Me={__name:"Create",setup(Ve){const N=L(),$=x(()=>{var n;return(n=N.props.auth)==null?void 0:n.user}),h=x(()=>{var n;return((n=$.value)==null?void 0:n.role)==="provider"}),_=x(()=>h.value?[{title:"Dashboard",href:"/dashboard"},{title:"My Products",href:"/provider/products"},{title:"Create Product",href:"/provider/products/create"}]:[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"},{title:"Create Product",href:"/admin/products/create"}]),B=x(()=>h.value?"/provider":"/admin"),P=x(()=>h.value?"/provider/products":"/admin/products"),f=y(!1),D=y([]),a=y({name:"",description:"",short_description:"",type:"physical",category_id:"",price:"",sale_price:"",sku:"",stock_quantity:"",manage_stock:!0,weight:"",dimensions:"",is_featured:!1,is_active:!0,digital_files:[],download_limit:"",download_expiry_days:""}),k=y(null),V=y([]),s=y({}),j=async()=>{try{const n=await window.axios.get(`${B.value}/products/create`);D.value=n.data.categories||[]}catch(n){console.error("Error fetching categories:",n)}},T=async()=>{var n,e,r,c;f.value=!0,s.value={};try{const m=new FormData;Object.keys(a.value).forEach(u=>{a.value[u]!==null&&a.value[u]!==""&&(Array.isArray(a.value[u])?a.value[u].forEach((C,A)=>{m.append(`${u}[${A}]`,C)}):typeof a.value[u]=="boolean"?m.append(u,a.value[u]?"1":"0"):m.append(u,a.value[u]))}),["manage_stock","is_featured","is_active"].forEach(u=>{m.has(u)||m.append(u,a.value[u]?"1":"0")}),k.value&&m.append("featured_image_id",k.value.id),V.value.forEach((u,C)=>{m.append(`gallery_image_ids[${C}]`,u.id)});const I=await window.axios.post(`${B.value}/save-product`,m,{headers:{"Content-Type":"multipart/form-data"}});I.data.success?window.location.href=P.value:alert("Error creating product: "+I.data.message)}catch(m){(e=(n=m.response)==null?void 0:n.data)!=null&&e.errors?s.value=m.response.data.errors:alert("Error creating product: "+(((c=(r=m.response)==null?void 0:r.data)==null?void 0:c.message)||m.message))}finally{f.value=!1}};return z(()=>{j()}),(n,e)=>(l(),o(U,null,[b(w(J),{title:"Create Product"}),b(K,null,{header:v(()=>[t("div",O,[t("div",null,[e[16]||(e[16]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Create Product ",-1)),t("nav",Q,[t("ol",Y,[(l(!0),o(U,null,M(_.value,(r,c)=>(l(),o("li",{key:c,class:"inline-flex items-center"},[c<_.value.length-1?(l(),H(w(F),{key:0,href:r.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:v(()=>[q(d(r.title),1)]),_:2},1032,["href"])):(l(),o("span",R,d(r.title),1)),c<_.value.length-1?(l(),o("svg",X,e[15]||(e[15]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):i("",!0)]))),128))])])]),b(w(F),{href:P.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:v(()=>e[17]||(e[17]=[q(" Back to Products ")])),_:1},8,["href"])])]),default:v(()=>[t("div",Z,[t("div",ee,[t("div",te,[t("div",re,[t("form",{onSubmit:W(T,["prevent"]),class:"space-y-6"},[t("div",ae,[t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Name *",-1)),p(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>a.value.name=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.name]]),s.value.name?(l(),o("p",se,d(s.value.name[0]),1)):i("",!0)]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"SKU *",-1)),p(t("input",{"onUpdate:modelValue":e[1]||(e[1]=r=>a.value.sku=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.sku]]),s.value.sku?(l(),o("p",le,d(s.value.sku[0]),1)):i("",!0)]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Category *",-1)),p(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>a.value.category_id=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[20]||(e[20]=t("option",{value:""},"Select Category",-1)),(l(!0),o(U,null,M(D.value,r=>(l(),o("option",{key:r.id,value:r.id},d(r.name),9,oe))),128))],512),[[E,a.value.category_id]]),s.value.category_id?(l(),o("p",de,d(s.value.category_id[0]),1)):i("",!0)]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Type *",-1)),p(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>a.value.type=r),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[22]||(e[22]=[t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[E,a.value.type]]),s.value.type?(l(),o("p",ue,d(s.value.type[0]),1)):i("",!0)]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Price *",-1)),p(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>a.value.price=r),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.price]]),s.value.price?(l(),o("p",ie,d(s.value.price[0]),1)):i("",!0)]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Sale Price",-1)),p(t("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>a.value.sale_price=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.sale_price]]),s.value.sale_price?(l(),o("p",ne,d(s.value.sale_price[0]),1)):i("",!0)])]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Description *",-1)),p(t("textarea",{"onUpdate:modelValue":e[6]||(e[6]=r=>a.value.description=r),rows:"4",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.description]]),s.value.description?(l(),o("p",me,d(s.value.description[0]),1)):i("",!0)]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Short Description",-1)),p(t("textarea",{"onUpdate:modelValue":e[7]||(e[7]=r=>a.value.short_description=r),rows:"2",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.short_description]]),s.value.short_description?(l(),o("p",pe,d(s.value.short_description[0]),1)):i("",!0)]),t("div",ge,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100"},"Product Images",-1)),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Featured Image",-1)),b(S,{modelValue:k.value,"onUpdate:modelValue":e[8]||(e[8]=r=>k.value=r),multiple:!1,"accepted-types":"image/*","accepted-types-text":"PNG, JPG, GIF up to 10MB",category:"product_images","button-text":"Choose Featured Image"},null,8,["modelValue"]),s.value.featured_image?(l(),o("p",ce,d(s.value.featured_image[0]),1)):i("",!0)]),t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Gallery Images",-1)),b(S,{modelValue:V.value,"onUpdate:modelValue":e[9]||(e[9]=r=>V.value=r),multiple:!0,"accepted-types":"image/*","accepted-types-text":"PNG, JPG, GIF up to 10MB each. You can select multiple images.",category:"product_images","button-text":"Choose Gallery Images"},null,8,["modelValue"]),s.value.gallery_images?(l(),o("p",ye,d(s.value.gallery_images[0]),1)):i("",!0)])]),a.value.type==="physical"?(l(),o("div",be,[t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stock Quantity",-1)),p(t("input",{"onUpdate:modelValue":e[10]||(e[10]=r=>a.value.stock_quantity=r),type:"number",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.stock_quantity]]),s.value.stock_quantity?(l(),o("p",xe,d(s.value.stock_quantity[0]),1)):i("",!0)]),t("div",null,[e[32]||(e[32]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Weight (kg)",-1)),p(t("input",{"onUpdate:modelValue":e[11]||(e[11]=r=>a.value.weight=r),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.weight]]),s.value.weight?(l(),o("p",ve,d(s.value.weight[0]),1)):i("",!0)]),t("div",null,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Dimensions",-1)),p(t("input",{"onUpdate:modelValue":e[12]||(e[12]=r=>a.value.dimensions=r),type:"text",placeholder:"L x W x H",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[g,a.value.dimensions]]),s.value.dimensions?(l(),o("p",fe,d(s.value.dimensions[0]),1)):i("",!0)])])):i("",!0),t("div",ke,[t("label",we,[p(t("input",{"onUpdate:modelValue":e[13]||(e[13]=r=>a.value.is_featured=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[G,a.value.is_featured]]),e[34]||(e[34]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Featured Product",-1))]),t("label",he,[p(t("input",{"onUpdate:modelValue":e[14]||(e[14]=r=>a.value.is_active=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[G,a.value.is_active]]),e[35]||(e[35]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))])]),t("div",_e,[b(w(F),{href:P.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:v(()=>e[36]||(e[36]=[q(" Cancel ")])),_:1},8,["href"]),t("button",{type:"submit",disabled:f.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},d(f.value?"Creating...":"Create Product"),9,Pe)])],32)])])])])]),_:1})],64))}};export{Me as default};
