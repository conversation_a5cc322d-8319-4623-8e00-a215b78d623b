import{z as W,c as y,r as S,B as L,o as z,d as l,e as o,f as h,u as w,m as G,g as x,i as t,n as c,F as _,p as q,j as H,l as g,v as p,t as n,q as F,s as j,P as C,x as D,y as J,W as K}from"./vendor-BhKTHoN5.js";import{_ as Q}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const R={class:"flex items-center justify-between"},X={class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"},Y={class:"flex mt-2","aria-label":"Breadcrumb"},Z={class:"inline-flex items-center space-x-1 md:space-x-3"},ee={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},te={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},re={class:"py-12"},se={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},ae={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},oe={class:"p-6"},le={key:0,class:"mb-6"},de={class:"flex flex-wrap gap-4"},ie={key:0,class:"relative"},ue=["src","alt"],ne=["src","alt"],ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ge={key:0,class:"text-red-500 text-sm"},me={key:0,class:"text-red-500 text-sm"},pe={key:0,class:"text-red-500 text-sm"},be=["value"],fe={key:0,class:"text-red-500 text-sm"},ye={key:0,class:"text-red-500 text-sm"},xe={key:0,class:"text-red-500 text-sm"},ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ke={key:0,class:"text-red-500 text-sm"},he={key:0,class:"text-red-500 text-sm"},we={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},_e={key:0,class:"text-red-500 text-sm"},Pe={key:0,class:"text-red-500 text-sm"},Ue={key:0,class:"text-red-500 text-sm"},Ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},qe={key:0,class:"text-red-500 text-sm"},Ce={key:0,class:"text-red-500 text-sm"},De={class:"flex items-center space-x-6"},Ee={class:"flex items-center"},Se={class:"flex items-center"},Fe={class:"flex items-center justify-end space-x-3"},je=["disabled"],Te={__name:"Edit",props:{product:Object,categories:Array},setup(m){const v=m,B=W(),M=y(()=>{var u;return(u=B.props.auth)==null?void 0:u.user}),P=y(()=>{var u;return((u=M.value)==null?void 0:u.role)==="provider"}),U=y(()=>P.value?[{title:"Dashboard",href:"/dashboard"},{title:"My Products",href:"/provider/products"},{title:"Edit Product",href:"#"}]:[{title:"Dashboard",href:"/dashboard"},{title:"Products",href:"/admin/products"},{title:"Edit Product",href:"#"}]),N=y(()=>P.value?"/provider":"/admin"),V=y(()=>P.value?"/provider/products":"/admin/products"),k=S(!1),r=L({name:"",description:"",short_description:"",type:"physical",category_id:"",price:"",sale_price:"",sku:"",stock_quantity:"",manage_stock:!0,weight:"",dimensions:"",is_featured:!1,is_active:!0,digital_files:[],download_limit:"",download_expiry_days:"",featured_image:null,gallery_images:[]}),a=S({}),E=y(()=>r.type==="digital");z(()=>{v.product&&Object.keys(r).forEach(u=>{v.product[u]!==void 0&&u!=="featured_image"&&u!=="gallery_images"&&(r[u]=v.product[u])})});const $=async()=>{var u,e,b;k.value=!0,a.value={};try{const d=new FormData;Object.keys(r).forEach(i=>{i==="featured_image"&&r[i]?d.append(i,r[i]):i==="gallery_images"&&r[i].length>0?r[i].forEach((A,O)=>{d.append(`gallery_images[${O}]`,A)}):i==="digital_files"&&E.value?d.append(i,JSON.stringify(r[i])):r[i]!==null&&r[i]!==""&&(typeof r[i]=="boolean"?d.append(i,r[i]?"1":"0"):d.append(i,r[i]))}),["manage_stock","is_featured","is_active"].forEach(i=>{d.has(i)||d.append(i,r[i]?"1":"0")}),d.append("_method","PUT"),(await window.axios.post(`${N.value}/save-product/${v.product.id}`,d,{headers:{"Content-Type":"multipart/form-data"}})).data.success&&K.visit(V.value,{onSuccess:()=>{alert("Product updated successfully!")}})}catch(d){((u=d.response)==null?void 0:u.status)===422?a.value=d.response.data.errors||{}:alert("Error updating product: "+(((b=(e=d.response)==null?void 0:e.data)==null?void 0:b.message)||d.message))}finally{k.value=!1}},I=u=>{const e=u.target.files[0];e&&(r.featured_image=e)},T=u=>{const e=Array.from(u.target.files);r.gallery_images=e};return(u,e)=>(o(),l(_,null,[h(w(G),{title:"Edit Product"}),h(Q,null,{header:x(()=>{var b;return[t("div",R,[t("div",null,[t("h2",X," Edit Product: "+n((b=m.product)==null?void 0:b.name),1),t("nav",Y,[t("ol",Z,[(o(!0),l(_,null,q(U.value,(d,f)=>(o(),l("li",{key:f,class:"inline-flex items-center"},[f<U.value.length-1?(o(),J(w(C),{key:0,href:d.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:x(()=>[D(n(d.title),1)]),_:2},1032,["href"])):(o(),l("span",ee,n(d.title),1)),f<U.value.length-1?(o(),l("svg",te,e[13]||(e[13]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):c("",!0)]))),128))])])]),h(w(C),{href:V.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:x(()=>e[14]||(e[14]=[D(" Back to Products ")])),_:1},8,["href"])])]}),default:x(()=>{var b,d,f;return[t("div",re,[t("div",se,[t("div",ae,[t("div",oe,[(b=m.product)!=null&&b.featured_image||(f=(d=m.product)==null?void 0:d.images)!=null&&f.length?(o(),l("div",le,[e[16]||(e[16]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},"Current Images",-1)),t("div",de,[m.product.featured_image?(o(),l("div",ie,[t("img",{src:`/storage/${m.product.featured_image}`,alt:m.product.name,class:"w-24 h-24 object-cover rounded border"},null,8,ue),e[15]||(e[15]=t("span",{class:"absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded"},"Featured",-1))])):c("",!0),(o(!0),l(_,null,q(m.product.images,s=>(o(),l("div",{key:s.id,class:"relative"},[t("img",{src:`/storage/${s.image_path}`,alt:s.alt_text,class:"w-24 h-24 object-cover rounded border"},null,8,ne)]))),128))])])):c("",!0),t("form",{onSubmit:H($,["prevent"]),class:"space-y-6"},[t("div",ce,[t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Name *",-1)),g(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>r.name=s),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.name]]),a.value.name?(o(),l("span",ge,n(a.value.name[0]),1)):c("",!0)]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"SKU *",-1)),g(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>r.sku=s),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.sku]]),a.value.sku?(o(),l("span",me,n(a.value.sku[0]),1)):c("",!0)]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Product Type *",-1)),g(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>r.type=s),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[19]||(e[19]=[t("option",{value:"physical"},"Physical Product",-1),t("option",{value:"digital"},"Digital Product",-1)]),512),[[F,r.type]]),a.value.type?(o(),l("span",pe,n(a.value.type[0]),1)):c("",!0)]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Category *",-1)),g(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>r.category_id=s),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[21]||(e[21]=t("option",{value:""},"Select Category",-1)),(o(!0),l(_,null,q(m.categories,s=>(o(),l("option",{key:s.id,value:s.id},n(s.name),9,be))),128))],512),[[F,r.category_id]]),a.value.category_id?(o(),l("span",fe,n(a.value.category_id[0]),1)):c("",!0)])]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Short Description",-1)),g(t("textarea",{"onUpdate:modelValue":e[4]||(e[4]=s=>r.short_description=s),rows:"2",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.short_description]]),a.value.short_description?(o(),l("span",ye,n(a.value.short_description[0]),1)):c("",!0)]),t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Description *",-1)),g(t("textarea",{"onUpdate:modelValue":e[5]||(e[5]=s=>r.description=s),rows:"4",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.description]]),a.value.description?(o(),l("span",xe,n(a.value.description[0]),1)):c("",!0)]),t("div",ve,[t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Price *",-1)),g(t("input",{"onUpdate:modelValue":e[6]||(e[6]=s=>r.price=s),type:"number",step:"0.01",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.price]]),a.value.price?(o(),l("span",ke,n(a.value.price[0]),1)):c("",!0)]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Sale Price",-1)),g(t("input",{"onUpdate:modelValue":e[7]||(e[7]=s=>r.sale_price=s),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.sale_price]]),a.value.sale_price?(o(),l("span",he,n(a.value.sale_price[0]),1)):c("",!0)])]),E.value?c("",!0):(o(),l("div",we,[t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stock Quantity *",-1)),g(t("input",{"onUpdate:modelValue":e[8]||(e[8]=s=>r.stock_quantity=s),type:"number",min:"0",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.stock_quantity]]),a.value.stock_quantity?(o(),l("span",_e,n(a.value.stock_quantity[0]),1)):c("",!0)]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Weight (kg)",-1)),g(t("input",{"onUpdate:modelValue":e[9]||(e[9]=s=>r.weight=s),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.weight]]),a.value.weight?(o(),l("span",Pe,n(a.value.weight[0]),1)):c("",!0)]),t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Dimensions",-1)),g(t("input",{"onUpdate:modelValue":e[10]||(e[10]=s=>r.dimensions=s),type:"text",placeholder:"L x W x H",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[p,r.dimensions]]),a.value.dimensions?(o(),l("span",Ue,n(a.value.dimensions[0]),1)):c("",!0)])])),t("div",Ve,[t("div",null,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Replace Featured Image",-1)),t("input",{type:"file",accept:"image/*",onChange:I,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,32),a.value.featured_image?(o(),l("span",qe,n(a.value.featured_image[0]),1)):c("",!0)]),t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Add Gallery Images",-1)),t("input",{type:"file",accept:"image/*",multiple:"",onChange:T,class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,32),a.value.gallery_images?(o(),l("span",Ce,n(a.value.gallery_images[0]),1)):c("",!0)])]),t("div",De,[t("label",Ee,[g(t("input",{"onUpdate:modelValue":e[11]||(e[11]=s=>r.is_featured=s),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[j,r.is_featured]]),e[32]||(e[32]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Featured Product",-1))]),t("label",Se,[g(t("input",{"onUpdate:modelValue":e[12]||(e[12]=s=>r.is_active=s),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[j,r.is_active]]),e[33]||(e[33]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Active",-1))])]),t("div",Fe,[h(w(C),{href:V.value,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"},{default:x(()=>e[34]||(e[34]=[D(" Cancel ")])),_:1},8,["href"]),t("button",{type:"submit",disabled:k.value,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"},n(k.value?"Updating...":"Update Product"),9,je)])],32)])])])])]}),_:1})],64))}};export{Te as default};
