<template>
    <AppLayout title="File Picker Test">
        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">File Picker Component Test</h2>
                    
                    <div class="space-y-8">
                        <!-- Single File Selection -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Single File Selection</h3>
                            <FilePickerButton
                                v-model="singleFile"
                                :multiple="false"
                                accepted-types="image/*"
                                accepted-types-text="Images only (PNG, JPG, GIF)"
                                category="product_images"
                                button-text="Choose Single Image"
                            />
                            
                            <div v-if="singleFile" class="mt-4 p-4 bg-gray-50 rounded">
                                <h4 class="font-medium">Selected File:</h4>
                                <pre class="mt-2 text-sm">{{ JSON.stringify(singleFile, null, 2) }}</pre>
                            </div>
                        </div>

                        <!-- Multiple File Selection -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Multiple File Selection</h3>
                            <FilePickerButton
                                v-model="multipleFiles"
                                :multiple="true"
                                accepted-types="*/*"
                                accepted-types-text="All file types supported"
                                category="all"
                                button-text="Choose Multiple Files"
                            />
                            
                            <div v-if="multipleFiles.length > 0" class="mt-4 p-4 bg-gray-50 rounded">
                                <h4 class="font-medium">Selected Files ({{ multipleFiles.length }}):</h4>
                                <pre class="mt-2 text-sm">{{ JSON.stringify(multipleFiles, null, 2) }}</pre>
                            </div>
                        </div>

                        <!-- Document Selection -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Document Selection</h3>
                            <FilePickerButton
                                v-model="documentFiles"
                                :multiple="true"
                                accepted-types=".pdf,.doc,.docx,.txt"
                                accepted-types-text="Documents only (PDF, Word, Text)"
                                category="documents"
                                button-text="Choose Documents"
                            />
                            
                            <div v-if="documentFiles.length > 0" class="mt-4 p-4 bg-gray-50 rounded">
                                <h4 class="font-medium">Selected Documents ({{ documentFiles.length }}):</h4>
                                <pre class="mt-2 text-sm">{{ JSON.stringify(documentFiles, null, 2) }}</pre>
                            </div>
                        </div>

                        <!-- Clear All Button -->
                        <div class="pt-6 border-t">
                            <button 
                                @click="clearAll"
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Clear All Selections
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script>
import { ref } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import FilePickerButton from '@/components/FilePickerButton.vue'

export default {
    name: 'FilePickerTest',
    components: {
        AppLayout,
        FilePickerButton
    },
    setup() {
        const singleFile = ref(null)
        const multipleFiles = ref([])
        const documentFiles = ref([])

        const clearAll = () => {
            singleFile.value = null
            multipleFiles.value = []
            documentFiles.value = []
        }

        return {
            singleFile,
            multipleFiles,
            documentFiles,
            clearAll
        }
    }
}
</script>
