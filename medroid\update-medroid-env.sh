#!/bin/bash

# Script to update .env file for medroid.ai domain
# Usage: ./update-medroid-env.sh [subdomain] (default: api)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
SUBDOMAIN=${1:-"api"}
DOMAIN="medroid.ai"
FULL_DOMAIN="$SUBDOMAIN.$DOMAIN"
HTTPS_URL="https://$FULL_DOMAIN"

echo -e "${PURPLE}🏥 Updating Medroid Environment Configuration${NC}"
echo -e "${BLUE}🌐 Domain: $HTTPS_URL${NC}"

# Backup current .env
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
echo -e "${GREEN}✅ Backed up current .env file${NC}"

# Update APP_URL
sed -i '' "s|APP_URL=.*|APP_URL=$HTTPS_URL|g" .env
echo -e "${GREEN}✅ Updated APP_URL to $HTTPS_URL${NC}"

# Update SESSION_DOMAIN
sed -i '' "s|SESSION_DOMAIN=.*|SESSION_DOMAIN=$FULL_DOMAIN|g" .env
echo -e "${GREEN}✅ Updated SESSION_DOMAIN to $FULL_DOMAIN${NC}"

# Update INSTAGRAM_REDIRECT_URI
sed -i '' "s|INSTAGRAM_REDIRECT_URI=.*|INSTAGRAM_REDIRECT_URI=$HTTPS_URL/auth/instagram/callback|g" .env
echo -e "${GREEN}✅ Updated INSTAGRAM_REDIRECT_URI${NC}"

# Update TIKTOK_REDIRECT_URI if it exists
if grep -q "TIKTOK_REDIRECT_URI" .env; then
    sed -i '' "s|TIKTOK_REDIRECT_URI=.*|TIKTOK_REDIRECT_URI=$HTTPS_URL/api/auth/tiktok/callback|g" .env
    echo -e "${GREEN}✅ Updated TIKTOK_REDIRECT_URI${NC}"
fi

echo -e "\n${GREEN}🎉 Environment updated successfully!${NC}"

# Show current configuration
echo -e "\n${PURPLE}📋 Current Configuration:${NC}"
echo -e "APP_URL: $(grep APP_URL .env)"
echo -e "SESSION_DOMAIN: $(grep SESSION_DOMAIN .env)"
echo -e "INSTAGRAM_REDIRECT_URI: $(grep INSTAGRAM_REDIRECT_URI .env)"

echo -e "\n${YELLOW}📝 Next Steps:${NC}"
echo -e "1. Update Instagram app settings in Facebook Developer Console:"
echo -e "   ${BLUE}Valid OAuth Redirect URIs:${NC} $HTTPS_URL/auth/instagram/callback"
echo -e "   ${BLUE}Webhook URL:${NC} $HTTPS_URL/webhooks/instagram"
echo -e ""
echo -e "2. Test your application at: ${BLUE}$HTTPS_URL${NC}"
echo -e ""
echo -e "3. Run tunnel with: ${BLUE}./setup-medroid-tunnel.sh${NC}"

echo -e "\n${GREEN}🌟 Professional Benefits:${NC}"
echo -e "✅ Custom domain (medroid.ai)"
echo -e "✅ SSL certificate included"
echo -e "✅ Professional appearance"
echo -e "✅ Stable URL for development"
echo -e "✅ Ready for production scaling"
