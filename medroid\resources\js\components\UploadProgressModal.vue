<template>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Uploading Files</h3>
                    <div class="text-sm text-gray-500">{{ progress }}%</div>
                </div>

                <!-- Overall Progress -->
                <div class="mb-6">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Overall Progress</span>
                        <span>{{ progress }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                            :style="{ width: progress + '%' }"
                        ></div>
                    </div>
                </div>

                <!-- Individual File Progress -->
                <div class="space-y-3 max-h-64 overflow-y-auto">
                    <div v-for="(file, index) in files" :key="index" class="border border-gray-200 rounded p-3">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</div>
                                <div class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</div>
                            </div>
                            <div class="ml-2 flex items-center">
                                <div v-if="file.status === 'uploading'" class="text-xs text-blue-600">{{ file.progress }}%</div>
                                <div v-else-if="file.status === 'completed'" class="text-xs text-green-600 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Complete
                                </div>
                                <div v-else class="text-xs text-red-600">Error</div>
                            </div>
                        </div>
                        
                        <!-- Individual Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-1">
                            <div 
                                class="h-1 rounded-full transition-all duration-300"
                                :class="{
                                    'bg-blue-600': file.status === 'uploading',
                                    'bg-green-600': file.status === 'completed',
                                    'bg-red-600': file.status === 'error'
                                }"
                                :style="{ width: file.progress + '%' }"
                            ></div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="mt-6 flex justify-end">
                    <button 
                        v-if="allCompleted"
                        @click="$emit('close')"
                        class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                        Done
                    </button>
                    <div v-else class="flex items-center text-sm text-gray-600">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                        Uploading...
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { computed } from 'vue'

export default {
    name: 'UploadProgressModal',
    props: {
        progress: {
            type: Number,
            required: true
        },
        files: {
            type: Array,
            required: true
        }
    },
    emits: ['close'],
    setup(props) {
        const allCompleted = computed(() => {
            return props.files.every(file => file.status === 'completed')
        })

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes'
            const k = 1024
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        return {
            allCompleted,
            formatFileSize
        }
    }
}
</script>
