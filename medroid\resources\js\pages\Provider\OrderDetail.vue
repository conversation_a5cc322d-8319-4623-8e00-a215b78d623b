<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

const page = usePage();
const order = computed(() => page.props.order);

const showDispatchModal = ref(false);
const shippingCompanies = ref([]);
const dispatchForm = ref({
    tracking_number: '',
    shipping_company: ''
});

const breadcrumbs = computed(() => [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Orders', href: '/provider/orders' },
    { title: `Order ${order.value?.order_number}`, href: '#' },
]);

const fetchShippingCompanies = async () => {
    try {
        const response = await window.axios.get('/provider/shipping-companies');
        shippingCompanies.value = response.data.companies;
    } catch (error) {
        console.error('Error fetching shipping companies:', error);
    }
};

const openDispatchModal = () => {
    showDispatchModal.value = true;
    dispatchForm.value = {
        tracking_number: order.value.tracking_number || '',
        shipping_company: order.value.shipping_company || ''
    };
};

const closeDispatchModal = () => {
    showDispatchModal.value = false;
    dispatchForm.value = {
        tracking_number: '',
        shipping_company: ''
    };
};

const dispatchOrder = async () => {
    try {
        const response = await window.axios.post(`/provider/orders/${order.value.id}/dispatch`, dispatchForm.value);
        
        // Reload the page to show updated order
        window.location.reload();
    } catch (error) {
        console.error('Error dispatching order:', error);
        if (error.response?.data?.message) {
            alert(`Error: ${error.response.data.message}`);
        } else {
            alert('Failed to dispatch order. Please try again.');
        }
    }
};

const getStatusBadgeClass = (status) => {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        'processing': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        'shipped': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
        'delivered': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'refunded': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const getPaymentStatusBadgeClass = (status) => {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        'paid': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'failed': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'refunded': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString();
};

const canDispatch = computed(() => {
    return ['pending', 'processing'].includes(order.value?.status) && order.value?.payment_status === 'paid';
});

onMounted(() => {
    fetchShippingCompanies();
});
</script>

<template>
    <Head :title="`Order ${order.order_number}`" />

    <AppLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Order Details
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" :href="breadcrumb.href" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <div class="flex space-x-3">
                    <button 
                        v-if="canDispatch" 
                        @click="openDispatchModal"
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-shipping-fast mr-2"></i>
                        Dispatch Order
                    </button>
                    <Link href="/provider/orders" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Orders
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Order Summary -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Order Information</h3>
                                <div class="space-y-1">
                                    <p class="text-sm"><span class="font-medium">Order Number:</span> {{ order.order_number }}</p>
                                    <p class="text-sm"><span class="font-medium">Date:</span> {{ formatDate(order.created_at) }}</p>
                                    <p class="text-sm"><span class="font-medium">Total:</span> {{ order.formatted_total }}</p>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Status</h3>
                                <div class="space-y-2">
                                    <div>
                                        <span :class="getStatusBadgeClass(order.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ order.status_label }}
                                        </span>
                                    </div>
                                    <div>
                                        <span :class="getPaymentStatusBadgeClass(order.payment_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ order.payment_status_label }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Customer</h3>
                                <div class="space-y-1">
                                    <p class="text-sm"><span class="font-medium">Name:</span> {{ order.user?.name || 'N/A' }}</p>
                                    <p class="text-sm"><span class="font-medium">Email:</span> {{ order.user?.email || 'N/A' }}</p>
                                </div>
                            </div>
                            <div v-if="order.tracking_number || order.shipping_company">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Shipping</h3>
                                <div class="space-y-1">
                                    <p v-if="order.shipping_company" class="text-sm"><span class="font-medium">Company:</span> {{ order.shipping_company }}</p>
                                    <p v-if="order.tracking_number" class="text-sm"><span class="font-medium">Tracking:</span> {{ order.tracking_number }}</p>
                                    <p v-if="order.shipped_at" class="text-sm"><span class="font-medium">Shipped:</span> {{ formatDate(order.shipped_at) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Order Items</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Product
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            SKU
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Quantity
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Unit Price
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Total
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="item in order.items" :key="item.id">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img
                                                        v-if="item.product?.primary_image"
                                                        :src="item.product.primary_image.startsWith('http') ? item.product.primary_image : `/storage/${item.product.primary_image}`"
                                                        :alt="item.product_name"
                                                        class="h-10 w-10 rounded object-cover"
                                                    >
                                                    <div v-else class="h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                        <i class="fas fa-box text-gray-500 dark:text-gray-400"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ item.product_name }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        {{ item.product_type }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ item.product_sku }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ item.quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ item.formatted_unit_price }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ item.formatted_total_price }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Shipping Address -->
                <div v-if="order.shipping_address" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Shipping Address</h3>
                        <div class="text-sm text-gray-900 dark:text-gray-100">
                            <p>{{ order.shipping_address.name }}</p>
                            <p>{{ order.shipping_address.address_line_1 }}</p>
                            <p v-if="order.shipping_address.address_line_2">{{ order.shipping_address.address_line_2 }}</p>
                            <p>{{ order.shipping_address.city }}, {{ order.shipping_address.state }} {{ order.shipping_address.postal_code }}</p>
                            <p>{{ order.shipping_address.country }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dispatch Modal -->
        <div v-if="showDispatchModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-50" @click="closeDispatchModal">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Dispatch Order {{ order.order_number }}
                    </h3>
                    <form @submit.prevent="dispatchOrder" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Shipping Company</label>
                            <select 
                                v-model="dispatchForm.shipping_company"
                                required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value="">Select shipping company</option>
                                <option v-for="company in shippingCompanies" :key="company" :value="company">
                                    {{ company }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tracking Number</label>
                            <input 
                                v-model="dispatchForm.tracking_number"
                                type="text" 
                                required
                                placeholder="Enter tracking number"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                        </div>
                        <div class="flex space-x-3 pt-4">
                            <button 
                                type="submit"
                                class="flex-1 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Dispatch Order
                            </button>
                            <button 
                                type="button"
                                @click="closeDispatchModal"
                                class="flex-1 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
