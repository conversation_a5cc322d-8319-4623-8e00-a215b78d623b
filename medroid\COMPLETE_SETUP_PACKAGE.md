# 🏥 Medroid.ai - Complete Setup Package

## 🎯 Everything You Need for New Mac Setup

This package contains everything needed to set up the Medroid development environment on any new Mac with professional Cloudflare Tunnel configuration.

---

## 📦 What's Included

### **🤖 Automated Setup**
- `setup-new-mac.sh` - One-command complete setup script
- `verify-setup.sh` - Verification script to check everything is working

### **📚 Documentation**
- `NEW_MAC_SETUP_GUIDE.md` - Detailed manual setup guide
- `QUICK_REFERENCE.md` - Daily commands and troubleshooting
- `CLOUDFLARE_TUNNEL_SETUP.md` - Cloudflare-specific setup
- `MIGRATION_FROM_NGROK.md` - Migration history and benefits

### **🛠️ Development Scripts**
- `start-cloudflare-tunnel.sh` - Start named tunnel
- `start-quick-tunnel.sh` - Start temporary tunnel
- `setup-medroid-tunnel.sh` - Professional tunnel setup
- `update-medroid-env.sh` - Update environment for custom domain
- `test-medroid-tunnel.sh` - Test tunnel connectivity

---

## 🚀 Quick Start (Choose One)

### **Option 1: Super Quick (Recommended)**
```bash
curl -fsSL https://raw.githubusercontent.com/majetyanupam/medroid_app_backend_v1.0/disocver/setup-new-mac.sh | bash
```

### **Option 2: Download and Run**
```bash
# Clone the repository
git clone -b disocver https://github.com/majetyanupam/medroid_app_backend_v1.0.git
cd medroid_app_backend_v1.0

# Run automated setup
./setup-new-mac.sh

# Verify everything is working
./verify-setup.sh
```

### **Option 3: Manual Setup**
Follow the detailed guide in `NEW_MAC_SETUP_GUIDE.md`

---

## 🎮 Daily Workflow

### **Start Development (3 Commands):**
```bash
# Terminal 1: Start tunnel
cloudflared tunnel run medroid-backend

# Terminal 2: Start Laravel
php artisan serve --port=8000

# Terminal 3: Start Vite
npm run dev
```

### **Or Use Aliases (if automated setup was used):**
```bash
medroid-start   # Start tunnel
medroid-serve   # Start Laravel
medroid-dev     # Start Vite
```

### **Your App URL:**
https://api.medroid.ai

---

## 🧪 Testing & Verification

### **Quick Test:**
```bash
./verify-setup.sh
```

### **Test Connectivity:**
```bash
./test-medroid-tunnel.sh
```

### **Manual Tests:**
```bash
curl -I https://api.medroid.ai
open https://api.medroid.ai
```

---

## 🔧 What Gets Installed

### **Prerequisites:**
- ✅ Homebrew (package manager)
- ✅ Node.js (JavaScript runtime)
- ✅ PHP (Laravel backend)
- ✅ Composer (PHP package manager)
- ✅ Git (version control)
- ✅ Cloudflared (tunnel client)

### **Project Setup:**
- ✅ Medroid repository cloned
- ✅ PHP dependencies installed
- ✅ Node.js dependencies installed
- ✅ Environment configured
- ✅ Laravel key generated

### **Cloudflare Configuration:**
- ✅ Cloudflare authentication
- ✅ Named tunnel created (medroid-backend)
- ✅ DNS record configured (api.medroid.ai)
- ✅ Tunnel configuration file
- ✅ Professional domain setup

### **Development Helpers:**
- ✅ Shell aliases for quick commands
- ✅ Testing scripts
- ✅ Environment update scripts
- ✅ Comprehensive documentation

---

## 🌟 Benefits of This Setup

### **Professional Infrastructure:**
- 🏆 **Custom Domain:** api.medroid.ai (not localhost)
- 🔒 **SSL Certificate:** Automatic HTTPS
- 🌍 **Global CDN:** 200+ Cloudflare data centers
- 🛡️ **DDoS Protection:** Enterprise-grade security
- ⚡ **High Performance:** Optimized routing worldwide

### **Developer Experience:**
- 🚀 **One-Command Setup:** Complete environment in minutes
- 🔄 **Stable URLs:** Never changes, no more CSRF issues
- 📱 **Mobile Testing:** Test on real devices easily
- 👥 **Team Ready:** Easy onboarding for new developers
- 🔗 **Webhook Ready:** Perfect for Instagram/social integrations

### **Production-Like:**
- 🏭 **Same Infrastructure:** As production environment
- 📊 **Real Performance:** Actual CDN and SSL behavior
- 🔍 **Accurate Testing:** True-to-production testing
- 📈 **Scalable:** Ready for production deployment

---

## 📱 Instagram Integration

After setup, update your Instagram app settings:

**Facebook Developer Console:** https://developers.facebook.com/apps/***************/

**Update URLs to:**
- **OAuth Redirect:** `https://api.medroid.ai/auth/instagram/callback`
- **Webhook URL:** `https://api.medroid.ai/webhooks/instagram`

---

## 🆘 Troubleshooting

### **If Setup Fails:**
1. Check internet connection
2. Ensure you have admin privileges
3. Run `./verify-setup.sh` to identify issues
4. Check `NEW_MAC_SETUP_GUIDE.md` for manual steps

### **If Services Won't Start:**
```bash
# Kill any conflicting processes
pkill -f cloudflared
pkill -f "php artisan serve"
pkill -f vite

# Restart in order
cloudflared tunnel run medroid-backend &
php artisan serve --port=8000 &
npm run dev
```

### **If Domain Doesn't Work:**
```bash
# Test DNS propagation
dig api.medroid.ai

# Wait 2-5 minutes and test again
./test-medroid-tunnel.sh
```

---

## 📋 System Requirements

- **macOS:** 10.15+ (Catalina or newer)
- **RAM:** 8GB+ recommended
- **Storage:** 5GB+ free space
- **Internet:** Required for Cloudflare Tunnel
- **Admin Access:** Required for Homebrew installation

---

## 🎉 Success Indicators

When setup is complete, you should have:

- ✅ **Working URL:** https://api.medroid.ai loads your app
- ✅ **No CORS Errors:** Frontend loads without issues
- ✅ **CSRF Working:** Login/forms work correctly
- ✅ **Professional Domain:** Custom domain instead of localhost
- ✅ **Global Access:** Accessible from anywhere in the world
- ✅ **SSL Certificate:** Green lock icon in browser
- ✅ **Fast Performance:** Cloudflare CDN acceleration

---

## 🚀 Ready to Code!

Your professional development environment is now ready! You have:

- **Enterprise-grade infrastructure**
- **Professional custom domain**
- **Global performance and security**
- **Team-ready development setup**
- **Production-like testing environment**

**Welcome to professional-grade development! 🌟**

---

*For support, questions, or issues, refer to the documentation files or run `./verify-setup.sh` to diagnose problems.*
