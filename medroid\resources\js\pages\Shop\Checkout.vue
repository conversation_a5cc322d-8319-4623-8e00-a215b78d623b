<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link, router } from '@inertiajs/vue3'
import { ref, computed, onMounted, nextTick } from 'vue'
import axios from 'axios'

const props = defineProps({
    cartItems: Array,
    subtotal: Number,
    taxAmount: Number,
    shippingAmount: Number,
    totalAmount: Number,
    stripeKey: String,
})

const breadcrumbs = [
    { title: 'Shop', href: '/shop' },
    { title: 'Shopping Cart', href: '/shop/cart' },
    { title: 'Checkout', href: '/shop/checkout' },
]

// Form data
const billingAddress = ref({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    address_line_1: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'US',
})

const shippingAddress = ref({
    first_name: '',
    last_name: '',
    address_line_1: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'US',
})

const useShippingAddress = ref(false)
const shippingMethod = ref('standard')

// Payment processing
const processing = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// Stripe elements
let stripe = null
let elements = null
let cardElement = null

// Computed properties
const formattedSubtotal = computed(() => '£' + props.subtotal.toFixed(2))
const formattedTax = computed(() => '£' + props.taxAmount.toFixed(2))
const formattedShipping = computed(() => '£' + props.shippingAmount.toFixed(2))
const formattedTotal = computed(() => '£' + props.totalAmount.toFixed(2))

const cartCount = computed(() => {
    return props.cartItems.reduce((count, item) => count + item.quantity, 0)
})

// Methods
const initializeStripe = async () => {
    try {
        // Load Stripe.js if not already loaded
        if (!window.Stripe) {
            const script = document.createElement('script')
            script.src = 'https://js.stripe.com/v3/'
            script.async = true
            document.head.appendChild(script)

            await new Promise((resolve, reject) => {
                script.onload = resolve
                script.onerror = reject
            })
        }

        // Initialize Stripe
        stripe = window.Stripe(props.stripeKey)
        elements = stripe.elements()

        // Create card element
        cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
        })

        // Mount card element
        await nextTick()
        const cardElementContainer = document.getElementById('card-element')
        if (cardElementContainer) {
            cardElement.mount('#card-element')
        }

        // Handle real-time validation errors from the card Element
        cardElement.on('change', ({ error }) => {
            const displayError = document.getElementById('card-errors')
            if (error) {
                displayError.textContent = error.message
            } else {
                displayError.textContent = ''
            }
        })

    } catch (error) {
        console.error('Error initializing Stripe:', error)
        errorMessage.value = 'Failed to initialize payment system'
    }
}

const processPayment = async () => {
    if (processing.value) return

    processing.value = true
    errorMessage.value = ''
    successMessage.value = ''

    try {
        // Validate form
        if (!billingAddress.value.first_name || !billingAddress.value.last_name || 
            !billingAddress.value.email || !billingAddress.value.phone ||
            !billingAddress.value.address_line_1 || !billingAddress.value.city ||
            !billingAddress.value.state || !billingAddress.value.postal_code) {
            errorMessage.value = 'Please fill in all required billing information'
            processing.value = false
            return
        }

        // Create payment method
        const { error, paymentMethod } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
            billing_details: {
                name: `${billingAddress.value.first_name} ${billingAddress.value.last_name}`,
                email: billingAddress.value.email,
                phone: billingAddress.value.phone,
                address: {
                    line1: billingAddress.value.address_line_1,
                    city: billingAddress.value.city,
                    state: billingAddress.value.state,
                    postal_code: billingAddress.value.postal_code,
                    country: billingAddress.value.country,
                },
            },
        })

        if (error) {
            errorMessage.value = error.message
            processing.value = false
            return
        }

        // Submit order to backend
        const orderData = {
            billing_address: billingAddress.value,
            shipping_address: useShippingAddress.value ? shippingAddress.value : billingAddress.value,
            shipping_method: shippingMethod.value,
            payment_method_id: paymentMethod.id,
        }

        const response = await axios.post('/shop/checkout', orderData)

        if (response.data.success) {
            const paymentIntent = response.data.payment_intent

            // Handle payment confirmation if needed
            if (paymentIntent.status === 'requires_action' || paymentIntent.status === 'requires_source_action') {
                const { error: confirmError } = await stripe.confirmCardPayment(paymentIntent.client_secret)

                if (confirmError) {
                    errorMessage.value = confirmError.message
                    processing.value = false
                    return
                }
            }

            successMessage.value = 'Order placed successfully!'

            // Redirect to order confirmation or orders page
            setTimeout(() => {
                router.visit(`/shop/orders/${response.data.order.order_number}`)
            }, 2000)
        } else {
            errorMessage.value = response.data.message || 'Order failed'
        }

    } catch (error) {
        console.error('Payment error:', error)
        errorMessage.value = error.response?.data?.message || 'Payment failed. Please try again.'
    } finally {
        processing.value = false
    }
}

const copyBillingToShipping = () => {
    if (!useShippingAddress.value) {
        shippingAddress.value = { ...billingAddress.value }
    }
}

// Initialize
onMounted(() => {
    initializeStripe()
})
</script>

<template>
    <Head title="Checkout - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 bg-white border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900">Checkout</h1>
                                <p class="text-gray-600 mt-1">{{ cartCount }} {{ cartCount === 1 ? 'item' : 'items' }} in your order</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-600">Total</p>
                                <p class="text-2xl font-bold text-gray-900">{{ formattedTotal }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Checkout Form -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Billing Address -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Billing Information</h2>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
                                        <input
                                            v-model="billingAddress.first_name"
                                            type="text"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
                                        <input
                                            v-model="billingAddress.last_name"
                                            type="text"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                                        <input
                                            v-model="billingAddress.email"
                                            type="email"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone *</label>
                                        <input
                                            v-model="billingAddress.phone"
                                            type="tel"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                                        <input
                                            v-model="billingAddress.address_line_1"
                                            type="text"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                                        <input
                                            v-model="billingAddress.city"
                                            type="text"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">State *</label>
                                        <input
                                            v-model="billingAddress.state"
                                            type="text"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Postal Code *</label>
                                        <input
                                            v-model="billingAddress.postal_code"
                                            type="text"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                                        <select
                                            v-model="billingAddress.country"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="US">United States</option>
                                            <option value="CA">Canada</option>
                                            <option value="GB">United Kingdom</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
                                
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Card Details</label>
                                    <div id="card-element" class="p-3 border border-gray-300 rounded-lg bg-white">
                                        <!-- Stripe Elements will create form elements here -->
                                    </div>
                                    <div id="card-errors" class="text-red-600 text-sm mt-2"></div>
                                </div>

                                <!-- Error/Success Messages -->
                                <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                                    {{ errorMessage }}
                                </div>

                                <div v-if="successMessage" class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                                    {{ successMessage }}
                                </div>

                                <!-- Place Order Button -->
                                <button
                                    @click="processPayment"
                                    :disabled="processing"
                                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span v-if="processing">Processing...</span>
                                    <span v-else>Place Order - {{ formattedTotal }}</span>
                                </button>

                                <div class="mt-4 text-center">
                                    <p class="text-sm text-gray-500">
                                        Secure checkout powered by Stripe
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg sticky top-6">
                            <div class="p-6">
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                                
                                <!-- Cart Items -->
                                <div class="space-y-3 mb-4">
                                    <div v-for="item in cartItems" :key="item.id" class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <img
                                                :src="item.product.images?.[0]?.url || '/images/placeholder.jpg'"
                                                :alt="item.product.name"
                                                class="w-12 h-12 object-cover rounded-lg"
                                            />
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 truncate">{{ item.product.name }}</p>
                                            <p class="text-sm text-gray-500">Qty: {{ item.quantity }}</p>
                                        </div>
                                        <div class="text-sm font-medium text-gray-900">
                                            ${{ parseFloat(item.total_price).toFixed(2) }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Order Totals -->
                                <div class="border-t border-gray-200 pt-4 space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span class="text-gray-900">{{ formattedSubtotal }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Shipping</span>
                                        <span class="text-gray-900">{{ formattedShipping }}</span>
                                    </div>
                                    <div class="border-t border-gray-200 pt-2">
                                        <div class="flex justify-between text-lg font-semibold">
                                            <span class="text-gray-900">Total</span>
                                            <span class="text-gray-900">{{ formattedTotal }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
