import{_ as D}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{c as n,d as o,e as a,f as c,u as g,m as A,g as u,i as e,t as s,A as b,n as l,F as m,p,P as f,x as v}from"./vendor-BhKTHoN5.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const B={class:"py-12"},T={class:"max-w-7xl mx-auto sm:px-6 lg:px-8"},N={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},U={class:"p-6 bg-white border-b border-gray-200"},V={class:"flex items-center justify-between"},P={class:"text-2xl font-bold text-gray-900"},q={class:"text-gray-600 mt-1"},z={class:"flex items-center space-x-4"},L={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},M={class:"lg:col-span-2"},E={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},I={class:"p-6"},K={class:"space-y-4"},Q={class:"flex-shrink-0"},G=["src","alt"],H={class:"flex-1 min-w-0"},J={class:"text-sm font-medium text-gray-900"},R={class:"text-sm text-gray-500"},W={class:"text-sm text-gray-500"},X={class:"text-sm text-gray-500"},Y={class:"text-right"},Z={class:"text-sm font-medium text-gray-900"},ee={class:"text-sm text-gray-500"},te={key:0,class:"mt-6"},se={class:"space-y-2"},de={class:"text-sm font-medium text-gray-900"},re={class:"text-sm text-gray-500"},oe=["href"],ae={class:"lg:col-span-1 space-y-6"},ie={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},le={class:"p-6"},ne={class:"space-y-2"},ce={class:"flex justify-between text-sm"},ge={class:"text-gray-900"},ue={class:"flex justify-between text-sm"},me={class:"text-gray-900"},he={class:"flex justify-between text-sm"},xe={class:"text-gray-900"},_e={class:"border-t border-gray-200 pt-2"},ye={class:"flex justify-between text-lg font-semibold"},be={class:"text-gray-900"},pe={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},fe={class:"p-6"},ve={key:0,class:"text-sm text-gray-600"},we={class:"font-medium text-gray-900"},ke={key:0},Fe={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Se={class:"p-6"},je={key:0,class:"text-sm text-gray-600"},$e={class:"font-medium text-gray-900"},Oe={key:0},Ce={key:1,class:"mt-2"},De={key:2},Ae={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},Be={class:"p-6"},Te={class:"space-y-3"},ze={__name:"OrderDetail",props:{order:{type:Object,required:!0}},setup(t){const i=t,w=[{title:"Shop",href:"/shop"},{title:"Orders",href:"/shop/orders"},{title:`Order #${i.order.order_number}`,href:`/shop/orders/${i.order.order_number}`}],k=n(()=>"$"+parseFloat(i.order.subtotal||0).toFixed(2)),F=n(()=>"$"+parseFloat(i.order.tax_amount||0).toFixed(2)),S=n(()=>"$"+parseFloat(i.order.shipping_amount||0).toFixed(2)),j=n(()=>"$"+parseFloat(i.order.total_amount||0).toFixed(2)),$=n(()=>{switch(i.order.status){case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";case"shipped":return"bg-purple-100 text-purple-800";case"delivered":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}}),O=n(()=>{switch(i.order.payment_status){case"pending":return"bg-yellow-100 text-yellow-800";case"paid":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";case"refunded":return"bg-gray-100 text-gray-800";default:return"bg-gray-100 text-gray-800"}}),C=h=>new Date(h).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return(h,d)=>(a(),o(m,null,[c(g(A),{title:`Order #${t.order.order_number} - Medroid`},null,8,["title"]),c(D,{breadcrumbs:w},{default:u(()=>[e("div",B,[e("div",T,[e("div",N,[e("div",U,[e("div",V,[e("div",null,[e("h1",P,"Order #"+s(t.order.order_number),1),e("p",q,"Placed on "+s(C(t.order.created_at)),1)]),e("div",z,[e("span",{class:b([$.value,"px-3 py-1 rounded-full text-sm font-medium"])},s(t.order.status.charAt(0).toUpperCase()+t.order.status.slice(1)),3),e("span",{class:b([O.value,"px-3 py-1 rounded-full text-sm font-medium"])},s(t.order.payment_status.charAt(0).toUpperCase()+t.order.payment_status.slice(1)),3)])])])]),e("div",L,[e("div",M,[e("div",E,[e("div",I,[d[1]||(d[1]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Items",-1)),e("div",K,[(a(!0),o(m,null,p(t.order.items,r=>{var x,_,y;return a(),o("div",{key:r.id,class:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"},[e("div",Q,[e("img",{src:((y=(_=(x=r.product)==null?void 0:x.images)==null?void 0:_[0])==null?void 0:y.url)||"/images/placeholder.jpg",alt:r.product_name,class:"w-16 h-16 object-cover rounded-lg"},null,8,G)]),e("div",H,[e("h3",J,s(r.product_name),1),e("p",R,"SKU: "+s(r.product_sku),1),e("p",W,"Type: "+s(r.product_type),1),e("p",X,"Quantity: "+s(r.quantity),1)]),e("div",Y,[e("p",Z,"$"+s(parseFloat(r.total_price).toFixed(2)),1),e("p",ee,"$"+s(parseFloat(r.unit_price).toFixed(2))+" each",1)])])}),128))]),t.order.digital_downloads&&t.order.digital_downloads.length>0?(a(),o("div",te,[d[0]||(d[0]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Digital Downloads",-1)),e("div",se,[(a(!0),o(m,null,p(t.order.digital_downloads,r=>(a(),o("div",{key:r.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[e("div",null,[e("p",de,s(r.file_name),1),e("p",re,s(r.file_size)+" MB",1)]),e("a",{href:`/download/${r.download_token}`,class:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",target:"_blank"}," Download ",8,oe)]))),128))])])):l("",!0)])])]),e("div",ae,[e("div",ie,[e("div",le,[d[6]||(d[6]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1)),e("div",ne,[e("div",ce,[d[2]||(d[2]=e("span",{class:"text-gray-600"},"Subtotal",-1)),e("span",ge,s(k.value),1)]),e("div",ue,[d[3]||(d[3]=e("span",{class:"text-gray-600"},"Tax",-1)),e("span",me,s(F.value),1)]),e("div",he,[d[4]||(d[4]=e("span",{class:"text-gray-600"},"Shipping",-1)),e("span",xe,s(S.value),1)]),e("div",_e,[e("div",ye,[d[5]||(d[5]=e("span",{class:"text-gray-900"},"Total",-1)),e("span",be,s(j.value),1)])])])])]),e("div",pe,[e("div",fe,[d[7]||(d[7]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Shipping Address",-1)),t.order.shipping_address?(a(),o("div",ve,[e("p",we,s(t.order.shipping_address.first_name)+" "+s(t.order.shipping_address.last_name),1),e("p",null,s(t.order.shipping_address.address_line_1),1),t.order.shipping_address.address_line_2?(a(),o("p",ke,s(t.order.shipping_address.address_line_2),1)):l("",!0),e("p",null,s(t.order.shipping_address.city)+", "+s(t.order.shipping_address.state)+" "+s(t.order.shipping_address.postal_code),1),e("p",null,s(t.order.shipping_address.country),1)])):l("",!0)])]),e("div",Fe,[e("div",Se,[d[8]||(d[8]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Billing Address",-1)),t.order.billing_address?(a(),o("div",je,[e("p",$e,s(t.order.billing_address.first_name)+" "+s(t.order.billing_address.last_name),1),e("p",null,s(t.order.billing_address.address_line_1),1),t.order.billing_address.address_line_2?(a(),o("p",Oe,s(t.order.billing_address.address_line_2),1)):l("",!0),e("p",null,s(t.order.billing_address.city)+", "+s(t.order.billing_address.state)+" "+s(t.order.billing_address.postal_code),1),e("p",null,s(t.order.billing_address.country),1),t.order.billing_address.email?(a(),o("p",Ce,s(t.order.billing_address.email),1)):l("",!0),t.order.billing_address.phone?(a(),o("p",De,s(t.order.billing_address.phone),1)):l("",!0)])):l("",!0)])]),e("div",Ae,[e("div",Be,[d[11]||(d[11]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Actions",-1)),e("div",Te,[c(g(f),{href:"/shop/orders",class:"w-full inline-flex justify-center items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},{default:u(()=>d[9]||(d[9]=[v(" Back to Orders ")])),_:1}),c(g(f),{href:"/shop",class:"w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"},{default:u(()=>d[10]||(d[10]=[v(" Continue Shopping ")])),_:1})])])])])])])])]),_:1})],64))}};export{ze as default};
