import{r as i,c as $,o as S,d as a,e as d,f as p,u as x,m as H,g as b,i as e,P as y,t as l,l as g,q as f,v as h,F as w,p as L,n as U,A as D,x as E,a as _}from"./vendor-BhKTHoN5.js";import{_ as N}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const P={class:"py-12"},R={class:"max-w-6xl mx-auto sm:px-6 lg:px-8"},q={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6"},I={class:"p-6 bg-white border-b border-gray-200"},Y={class:"flex items-center justify-between"},G={class:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6"},J={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},K={class:"flex items-center"},O={class:"ml-4"},Q={class:"text-2xl font-bold text-gray-900"},W={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},X={class:"flex items-center"},Z={class:"ml-4"},ee={class:"text-2xl font-bold text-gray-900"},te={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},se={class:"flex items-center"},oe={class:"ml-4"},re={class:"text-2xl font-bold text-gray-900"},le={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},ae={class:"flex items-center"},de={class:"ml-4"},ne={class:"text-2xl font-bold text-gray-900"},ie={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},ue={class:"flex items-center"},ce={class:"ml-4"},me={class:"text-2xl font-bold text-gray-900"},pe={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"},ge={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ve={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},xe={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},be={class:"text-center py-8"},ye={class:"text-gray-600 mb-4"},fe={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200"},he={class:"p-6"},we={class:"space-y-4"},_e={class:"flex items-center space-x-4"},ke={class:"text-2xl"},Ce={class:"font-medium text-gray-900"},Me={key:0,class:"text-sm text-gray-600 mt-1"},Be={class:"text-xs text-gray-500 mt-1"},Fe={class:"text-right"},Ae={class:"text-xs text-gray-500 capitalize"},Te={key:3,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},je={class:"text-center py-8"},Ue={__name:"CreditHistory",setup(Ve){const k=[{title:"Credit History",href:"/credit-history"}],u=i([]),v=i(!1),c=i(null),n=i({balance:0,total_earned:0,total_used:0,referral_earnings:0,admin_credits:0}),r=i({type:"",source:"",start_date:"",end_date:""}),C=async()=>{try{const s=await _.get("/credits-balance");n.value=s.data}catch(s){console.error("Error loading credit balance:",s)}},m=async()=>{v.value=!0,c.value=null;try{const s=new URLSearchParams;r.value.type&&s.append("type",r.value.type),r.value.source&&s.append("source",r.value.source),r.value.start_date&&s.append("start_date",r.value.start_date),r.value.end_date&&s.append("end_date",r.value.end_date);const t=await _.get(`/credits-transactions?${s}`);u.value=t.data.data||[]}catch(s){console.error("Error fetching transaction history:",s),c.value="Failed to load transaction history",u.value=[]}finally{v.value=!1}},M=()=>{m()},B=()=>{r.value={type:"",source:"",start_date:"",end_date:""},m()},F=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),A=(s,t)=>{const o=parseFloat(s).toFixed(2);return t==="earned"?`+$${o}`:`-$${o}`},T=(s,t)=>s==="earned"?t==="admin"?"👨‍💼":t==="referral"?"👥":"💰":t==="appointment"?"🏥":"💸",j=s=>s==="earned"?"text-green-600":"text-red-600",V=s=>({admin:"Admin Credit",referral:"Referral Bonus",appointment:"Appointment Payment",bonus:"Bonus Credit",promotion:"Promotional Credit"})[s]||s,z=$(()=>u.value);return S(()=>{C(),m()}),(s,t)=>(d(),a(w,null,[p(x(H),{title:"Credit History - Medroid"}),p(N,{breadcrumbs:k},{default:b(()=>[e("div",P,[e("div",R,[e("div",q,[e("div",I,[e("div",Y,[t[5]||(t[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Credit History"),e("p",{class:"text-gray-600 mt-1"},"View your credit transactions and balance")],-1)),p(x(y),{href:"/chat",class:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-teal-600 rounded-lg hover:bg-teal-700 transition-colors duration-200"},{default:b(()=>t[4]||(t[4]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1),e("span",null,"Back to Chat",-1)])),_:1})])])]),e("div",G,[e("div",J,[e("div",K,[t[7]||(t[7]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",O,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-600"},"Current Balance",-1)),e("p",Q,"$"+l(n.value.balance.toFixed(2)),1)])])]),e("div",W,[e("div",X,[t[9]||(t[9]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",Z,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Earned",-1)),e("p",ee,"$"+l(n.value.total_earned.toFixed(2)),1)])])]),e("div",te,[e("div",se,[t[11]||(t[11]=e("div",{class:"p-2 bg-red-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"})])],-1)),e("div",oe,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Used",-1)),e("p",re,"$"+l(n.value.total_used.toFixed(2)),1)])])]),e("div",le,[e("div",ae,[t[13]||(t[13]=e("div",{class:"p-2 bg-yellow-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",de,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600"},"Referral Credits",-1)),e("p",ne,"$"+l(n.value.referral_earnings.toFixed(2)),1)])])]),e("div",ie,[e("div",ue,[t[15]||(t[15]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])],-1)),e("div",ce,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600"},"Admin Credits",-1)),e("p",me,"$"+l(n.value.admin_credits.toFixed(2)),1)])])])]),e("div",pe,[t[22]||(t[22]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Filter Transactions",-1)),e("div",ge,[e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Type",-1)),g(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>r.value.type=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500"},t[16]||(t[16]=[e("option",{value:""},"All Types",-1),e("option",{value:"earned"},"Earned",-1),e("option",{value:"used"},"Used",-1)]),512),[[f,r.value.type]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Source",-1)),g(e("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>r.value.source=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500"},t[18]||(t[18]=[e("option",{value:""},"All Sources",-1),e("option",{value:"admin"},"Admin Credit",-1),e("option",{value:"referral"},"Referral",-1),e("option",{value:"appointment"},"Appointment",-1),e("option",{value:"bonus"},"Bonus",-1)]),512),[[f,r.value.source]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Start Date",-1)),g(e("input",{"onUpdate:modelValue":t[2]||(t[2]=o=>r.value.start_date=o),type:"date",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500"},null,512),[[h,r.value.start_date]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"End Date",-1)),g(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>r.value.end_date=o),type:"date",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500"},null,512),[[h,r.value.end_date]])])]),e("div",{class:"flex space-x-3 mt-4"},[e("button",{onClick:M,class:"px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"}," Apply Filters "),e("button",{onClick:B,class:"px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"}," Clear Filters ")])]),v.value?(d(),a("div",ve,t[23]||(t[23]=[e("div",{class:"flex items-center justify-center py-8"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mr-3"}),e("span",{class:"text-gray-600"},"Loading transactions...")],-1)]))):c.value?(d(),a("div",xe,[e("div",be,[t[24]||(t[24]=e("div",{class:"text-red-600 mb-2"},[e("svg",{class:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Error Loading Transactions",-1)),e("p",ye,l(c.value),1),e("button",{onClick:m,class:"px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"}," Try Again ")])])):u.value.length>0?(d(),a("div",fe,[e("div",he,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Transaction History",-1)),e("div",we,[(d(!0),a(w,null,L(z.value,o=>(d(),a("div",{key:o.id,class:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"},[e("div",_e,[e("div",ke,l(T(o.type,o.source)),1),e("div",null,[e("h4",Ce,l(V(o.source)),1),o.description?(d(),a("p",Me,l(o.description),1)):U("",!0),e("p",Be,l(F(o.created_at)),1)])]),e("div",Fe,[e("p",{class:D(["font-semibold",j(o.type)])},l(A(o.amount,o.type)),3),e("p",Ae,l(o.type),1)])]))),128))])])])):(d(),a("div",Te,[e("div",je,[t[28]||(t[28]=e("div",{class:"w-20 h-20 bg-teal-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg"},[e("svg",{class:"w-10 h-10 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t[29]||(t[29]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No transactions yet",-1)),t[30]||(t[30]=e("p",{class:"text-gray-600 mb-6"},"Your credit transactions will appear here once you start earning or using credits.",-1)),p(x(y),{href:"/chat",class:"inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"},{default:b(()=>t[27]||(t[27]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1),E(" Start Chatting ")])),_:1})])]))])])]),_:1})],64))}};export{Ue as default};
