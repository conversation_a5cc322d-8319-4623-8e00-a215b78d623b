<template>
    <AppLayout title="File Manager">
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <!-- Header -->
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-2xl font-bold text-gray-900">File Manager</h2>
                            <div class="flex space-x-4">
                                <!-- Upload Button -->
                                <label class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg cursor-pointer inline-flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    Upload Files
                                    <input type="file" multiple @change="handleFileUpload" class="hidden" ref="fileInput">
                                </label>
                                
                                <!-- View Toggle -->
                                <div class="flex bg-gray-100 rounded-lg p-1">
                                    <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow' : ''" class="px-3 py-1 rounded text-sm">Grid</button>
                                    <button @click="viewMode = 'list'" :class="viewMode === 'list' ? 'bg-white shadow' : ''" class="px-3 py-1 rounded text-sm">List</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters and Search -->
                    <div class="p-6 border-b border-gray-200 bg-gray-50">
                        <div class="flex flex-wrap gap-4 items-center">
                            <!-- Search -->
                            <div class="flex-1 min-w-64">
                                <input 
                                    v-model="searchQuery" 
                                    type="text" 
                                    placeholder="Search files..." 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                            </div>
                            
                            <!-- Category Filter -->
                            <select v-model="selectedCategory" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="all">All Categories</option>
                                <option v-for="category in categories" :key="category.value" :value="category.value">
                                    {{ category.label }}
                                </option>
                            </select>
                            
                            <!-- Type Filter -->
                            <select v-model="selectedType" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="all">All Types</option>
                                <option value="images">Images</option>
                                <option value="documents">Documents</option>
                                <option value="videos">Videos</option>
                            </select>
                            
                            <!-- Sort -->
                            <select v-model="sortBy" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="created_at">Date Created</option>
                                <option value="name">Name</option>
                                <option value="size">Size</option>
                            </select>
                            
                            <select v-model="sortOrder" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="desc">Newest First</option>
                                <option value="asc">Oldest First</option>
                            </select>
                        </div>
                    </div>

                    <!-- File Stats -->
                    <div class="p-6 border-b border-gray-200" v-if="stats">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">{{ stats.total_files }}</div>
                                <div class="text-sm text-gray-600">Total Files</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">{{ formatFileSize(stats.total_size) }}</div>
                                <div class="text-sm text-gray-600">Total Size</div>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600">{{ stats.by_type.images }}</div>
                                <div class="text-sm text-gray-600">Images</div>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <div class="text-2xl font-bold text-orange-600">{{ stats.by_type.documents }}</div>
                                <div class="text-sm text-gray-600">Documents</div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div v-if="loading" class="p-12 text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <p class="mt-2 text-gray-600">Loading files...</p>
                    </div>

                    <!-- Files Grid/List -->
                    <div v-else-if="files.data && files.data.length > 0" class="p-6">
                        <!-- Bulk Actions -->
                        <div v-if="selectedFiles.length > 0" class="mb-4 p-4 bg-blue-50 rounded-lg flex items-center justify-between">
                            <span class="text-blue-800">{{ selectedFiles.length }} file(s) selected</span>
                            <div class="space-x-2">
                                <button @click="bulkDelete" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm">
                                    Delete Selected
                                </button>
                                <button @click="clearSelection" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm">
                                    Clear Selection
                                </button>
                            </div>
                        </div>

                        <!-- Grid View -->
                        <div v-if="viewMode === 'grid'" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            <div 
                                v-for="file in files.data" 
                                :key="file.id" 
                                class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                                @click="selectFile(file)"
                                :class="{ 'ring-2 ring-blue-500': selectedFiles.includes(file.id) }"
                            >
                                <div class="text-center">
                                    <!-- File Icon/Preview -->
                                    <div class="mb-2">
                                        <img v-if="file.is_image" :src="file.url" :alt="file.name" class="w-full h-20 object-cover rounded">
                                        <div v-else class="w-full h-20 bg-gray-100 rounded flex items-center justify-center">
                                            <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="text-xs font-medium text-gray-900 truncate">{{ file.name }}</div>
                                    <div class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- List View -->
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" @change="toggleSelectAll" :checked="allSelected">
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="file in files.data" :key="file.id" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" :value="file.id" v-model="selectedFiles">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <img v-if="file.is_image" :src="file.url" :alt="file.name" class="w-10 h-10 object-cover rounded mr-3">
                                                <div v-else class="w-10 h-10 bg-gray-100 rounded mr-3 flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ file.name }}</div>
                                                    <div class="text-sm text-gray-500">{{ file.mime_type }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                {{ getCategoryLabel(file.category) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatFileSize(file.size) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatDate(file.updated_at) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button @click="downloadFile(file)" class="text-blue-600 hover:text-blue-900">Download</button>
                                                <button @click="editFile(file)" class="text-green-600 hover:text-green-900">Edit</button>
                                                <button @click="deleteFile(file)" class="text-red-600 hover:text-red-900">Delete</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div v-if="files.last_page > 1" class="mt-6 flex justify-between items-center">
                            <div class="text-sm text-gray-700">
                                Showing {{ files.from }} to {{ files.to }} of {{ files.total }} results
                            </div>
                            <div class="flex space-x-2">
                                <button 
                                    v-for="page in paginationPages" 
                                    :key="page"
                                    @click="changePage(page)"
                                    :class="page === files.current_page ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'"
                                    class="px-3 py-2 border border-gray-300 rounded text-sm"
                                >
                                    {{ page }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-else class="p-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No files</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by uploading your first file.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Edit Modal -->
        <FileEditModal 
            v-if="editingFile" 
            :file="editingFile" 
            :categories="categories"
            @close="editingFile = null" 
            @updated="handleFileUpdated"
        />

        <!-- Upload Progress Modal -->
        <UploadProgressModal 
            v-if="uploading" 
            :progress="uploadProgress"
            :files="uploadingFiles"
            @close="uploading = false"
        />
    </AppLayout>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import FileEditModal from '@/components/FileEditModal.vue'
import UploadProgressModal from '@/components/UploadProgressModal.vue'
import axios from 'axios'

export default {
    name: 'FileManager',
    components: {
        AppLayout,
        FileEditModal,
        UploadProgressModal
    },
    setup() {
        // Reactive data
        const files = ref({ data: [], total: 0, current_page: 1, last_page: 1 })
        const categories = ref([])
        const stats = ref(null)
        const loading = ref(false)
        const uploading = ref(false)
        const uploadProgress = ref(0)
        const uploadingFiles = ref([])

        // Filters and search
        const searchQuery = ref('')
        const selectedCategory = ref('all')
        const selectedType = ref('all')
        const sortBy = ref('created_at')
        const sortOrder = ref('desc')
        const viewMode = ref('grid')

        // Selection
        const selectedFiles = ref([])
        const editingFile = ref(null)

        // Computed properties
        const allSelected = computed(() => {
            return files.value.data.length > 0 && selectedFiles.value.length === files.value.data.length
        })

        const paginationPages = computed(() => {
            const pages = []
            const current = files.value.current_page
            const last = files.value.last_page

            // Show first page
            if (current > 3) pages.push(1)
            if (current > 4) pages.push('...')

            // Show pages around current
            for (let i = Math.max(1, current - 2); i <= Math.min(last, current + 2); i++) {
                pages.push(i)
            }

            // Show last page
            if (current < last - 3) pages.push('...')
            if (current < last - 2) pages.push(last)

            return pages
        })

        // Methods
        const loadFiles = async (page = 1) => {
            loading.value = true
            try {
                const params = {
                    page,
                    search: searchQuery.value,
                    category: selectedCategory.value,
                    type: selectedType.value,
                    sort_by: sortBy.value,
                    sort_order: sortOrder.value,
                    per_page: 20
                }

                const response = await axios.get('/web-api/files', { params })
                files.value = response.data
            } catch (error) {
                console.error('Error loading files:', error)
                // Handle error
            } finally {
                loading.value = false
            }
        }

        const loadCategories = async () => {
            try {
                const response = await axios.get('/web-api/files/categories')
                categories.value = response.data
            } catch (error) {
                console.error('Error loading categories:', error)
            }
        }

        const loadStats = async () => {
            try {
                const response = await axios.get('/web-api/files/stats')
                stats.value = response.data
            } catch (error) {
                console.error('Error loading stats:', error)
            }
        }

        const handleFileUpload = async (event) => {
            const selectedFiles = Array.from(event.target.files)
            if (selectedFiles.length === 0) return

            uploading.value = true
            uploadingFiles.value = selectedFiles.map(file => ({
                name: file.name,
                size: file.size,
                progress: 0,
                status: 'uploading'
            }))

            try {
                for (let i = 0; i < selectedFiles.length; i++) {
                    const file = selectedFiles[i]
                    const formData = new FormData()
                    formData.append('file', file)

                    await axios.post('/web-api/files', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        },
                        onUploadProgress: (progressEvent) => {
                            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                            uploadingFiles.value[i].progress = progress
                            uploadProgress.value = Math.round(((i * 100) + progress) / selectedFiles.length)
                        }
                    })

                    uploadingFiles.value[i].status = 'completed'
                }

                // Reload files and stats
                await Promise.all([loadFiles(), loadStats()])

                // Clear file input
                event.target.value = ''

            } catch (error) {
                console.error('Error uploading files:', error)
                // Handle error
            } finally {
                setTimeout(() => {
                    uploading.value = false
                    uploadProgress.value = 0
                    uploadingFiles.value = []
                }, 1000)
            }
        }

        const selectFile = (file) => {
            const index = selectedFiles.value.indexOf(file.id)
            if (index > -1) {
                selectedFiles.value.splice(index, 1)
            } else {
                selectedFiles.value.push(file.id)
            }
        }

        const toggleSelectAll = () => {
            if (allSelected.value) {
                selectedFiles.value = []
            } else {
                selectedFiles.value = files.value.data.map(file => file.id)
            }
        }

        const clearSelection = () => {
            selectedFiles.value = []
        }

        const downloadFile = async (file) => {
            try {
                const response = await axios.get(`/web-api/files/${file.id}/download`, {
                    responseType: 'blob'
                })

                const url = window.URL.createObjectURL(new Blob([response.data]))
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', file.original_name)
                document.body.appendChild(link)
                link.click()
                link.remove()
                window.URL.revokeObjectURL(url)
            } catch (error) {
                console.error('Error downloading file:', error)
            }
        }

        const editFile = (file) => {
            editingFile.value = file
        }

        const deleteFile = async (file) => {
            if (!confirm(`Are you sure you want to delete "${file.name}"?`)) return

            try {
                await axios.delete(`/web-api/files/${file.id}`)
                await Promise.all([loadFiles(), loadStats()])

                // Remove from selection if selected
                const index = selectedFiles.value.indexOf(file.id)
                if (index > -1) {
                    selectedFiles.value.splice(index, 1)
                }
            } catch (error) {
                console.error('Error deleting file:', error)
                if (error.response?.data?.message) {
                    alert(error.response.data.message)
                }
            }
        }

        const bulkDelete = async () => {
            if (selectedFiles.value.length === 0) return
            if (!confirm(`Are you sure you want to delete ${selectedFiles.value.length} file(s)?`)) return

            try {
                await axios.post('/web-api/files/bulk-delete', {
                    file_ids: selectedFiles.value
                })

                await Promise.all([loadFiles(), loadStats()])
                selectedFiles.value = []
            } catch (error) {
                console.error('Error deleting files:', error)
                if (error.response?.data?.message) {
                    alert(error.response.data.message)
                }
            }
        }

        const changePage = (page) => {
            if (page !== '...' && page !== files.value.current_page) {
                loadFiles(page)
            }
        }

        const handleFileUpdated = async (updatedFile) => {
            // Update the file in the list
            const index = files.value.data.findIndex(f => f.id === updatedFile.id)
            if (index > -1) {
                files.value.data[index] = updatedFile
            }

            editingFile.value = null
            await loadStats()
        }

        // Utility functions
        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes'
            const k = 1024
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        const formatDate = (dateString) => {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            })
        }

        const getCategoryLabel = (categoryValue) => {
            const category = categories.value.find(c => c.value === categoryValue)
            return category ? category.label : categoryValue
        }

        // Watchers
        watch([searchQuery, selectedCategory, selectedType, sortBy, sortOrder], () => {
            loadFiles(1)
        }, { debounce: 300 })

        // Lifecycle
        onMounted(async () => {
            await Promise.all([
                loadFiles(),
                loadCategories(),
                loadStats()
            ])
        })

        return {
            // Data
            files,
            categories,
            stats,
            loading,
            uploading,
            uploadProgress,
            uploadingFiles,
            searchQuery,
            selectedCategory,
            selectedType,
            sortBy,
            sortOrder,
            viewMode,
            selectedFiles,
            editingFile,

            // Computed
            allSelected,
            paginationPages,

            // Methods
            handleFileUpload,
            selectFile,
            toggleSelectAll,
            clearSelection,
            downloadFile,
            editFile,
            deleteFile,
            bulkDelete,
            changePage,
            handleFileUpdated,
            formatFileSize,
            formatDate,
            getCategoryLabel
        }
    }
}
</script>
