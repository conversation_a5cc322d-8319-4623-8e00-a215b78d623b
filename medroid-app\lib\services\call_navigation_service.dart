import 'package:flutter/material.dart';
import 'package:medroid_app/screens/incoming_call_screen.dart';
import 'package:medroid_app/screens/agora_video_consultation_screen.dart';

/// Service to handle navigation for incoming video calls
class CallNavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  /// Show incoming call screen overlay
  static void showIncomingCall(Map<String, dynamic> callData) {
    final context = navigatorKey.currentContext;
    if (context == null) {
      debugPrint('❌ No navigator context available for incoming call');
      return;
    }

    debugPrint('📞 Showing incoming call screen');
    debugPrint('Call data: $callData');

    // Show full-screen incoming call overlay
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => 
            IncomingCallScreen(callData: callData),
        transitionDuration: const Duration(milliseconds: 300),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // Slide in from bottom animation
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        opaque: true,
        barrierDismissible: false,
        fullscreenDialog: true,
      ),
    );
  }

  /// Navigate to video consultation after accepting call
  static void navigateToVideoCall({
    required String appointmentId,
    required bool isProvider,
    required String userName,
  }) {
    final context = navigatorKey.currentContext;
    if (context == null) {
      debugPrint('❌ No navigator context available for video call');
      return;
    }

    debugPrint('🎥 Navigating to video consultation');
    debugPrint('Appointment ID: $appointmentId');
    debugPrint('Is Provider: $isProvider');

    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => AgoraVideoConsultationScreen(
          appointmentId: appointmentId,
          isProvider: isProvider,
          userName: userName,
        ),
      ),
    );
  }

  /// Handle notification tap navigation
  static void handleNotificationNavigation(Map<String, dynamic> data) {
    final context = navigatorKey.currentContext;
    if (context == null) {
      debugPrint('❌ No navigator context available for notification navigation');
      return;
    }

    final String type = data['type'] ?? '';
    final String? action = data['action'];

    debugPrint('🎯 Handling notification navigation');
    debugPrint('Type: $type, Action: $action');

    switch (type) {
      case 'video_call':
        if (action == 'accept_call') {
          // Navigate directly to video call
          final appointmentId = data['appointment_id']?.toString() ?? '';
          if (appointmentId.isNotEmpty) {
            navigateToVideoCall(
              appointmentId: appointmentId,
              isProvider: false, // Patient accepting call
              userName: 'Patient',
            );
          }
        } else {
          // Show incoming call screen
          showIncomingCall(data);
        }
        break;
      
      case 'appointment_reminder':
      case 'appointment_booked':
        // Navigate to appointments screen
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/appointments',
          (route) => route.isFirst,
        );
        break;
        
      default:
        // Navigate to main screen
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/main',
          (route) => route.isFirst,
        );
        break;
    }
  }

  /// Dismiss incoming call screen
  static void dismissIncomingCall() {
    final context = navigatorKey.currentContext;
    if (context != null && Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }
}