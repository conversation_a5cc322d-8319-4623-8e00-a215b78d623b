<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class UserManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles if they don't exist
        Role::firstOrCreate(['name' => 'admin']);
        Role::firstOrCreate(['name' => 'provider']);
        Role::firstOrCreate(['name' => 'patient']);
        Role::firstOrCreate(['name' => 'manager']);
    }

    /** @test */
    public function admin_can_view_users_list()
    {
        // Create an admin user
        $admin = User::factory()->create(['role' => 'admin']);
        $admin->assignRole('admin');

        // Create some test users
        $users = User::factory()->count(15)->create();

        $response = $this->actingAs($admin)
            ->getJson('/users-list');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data',
                'current_page',
                'last_page',
                'per_page',
                'total'
            ]);
    }

    /** @test */
    public function users_list_supports_pagination()
    {
        // Create an admin user
        $admin = User::factory()->create(['role' => 'admin']);
        $admin->assignRole('admin');

        // Create 25 test users
        User::factory()->count(25)->create();

        // Test first page with 10 per page
        $response = $this->actingAs($admin)
            ->getJson('/users-list?page=1&per_page=10');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertEquals(1, $data['current_page']);
        $this->assertEquals(10, $data['per_page']);
        $this->assertCount(10, $data['data']);
        $this->assertGreaterThanOrEqual(26, $data['total']); // 25 + admin user

        // Test second page
        $response = $this->actingAs($admin)
            ->getJson('/users-list?page=2&per_page=10');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertEquals(2, $data['current_page']);
    }

    /** @test */
    public function users_list_supports_search()
    {
        // Create an admin user
        $admin = User::factory()->create(['role' => 'admin']);
        $admin->assignRole('admin');

        // Create test users with specific names
        $john = User::factory()->create(['name' => 'John Doe', 'email' => '<EMAIL>']);
        $jane = User::factory()->create(['name' => 'Jane Smith', 'email' => '<EMAIL>']);
        $bob = User::factory()->create(['name' => 'Bob Johnson', 'email' => '<EMAIL>']);

        // Search by name
        $response = $this->actingAs($admin)
            ->getJson('/users-list?search=John');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(2, $data['data']); // John Doe and Bob Johnson
        
        // Search by email
        $response = $this->actingAs($admin)
            ->getJson('/users-list?search=example.com');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(2, $data['data']); // john@example.<NAME_EMAIL>
    }

    /** @test */
    public function users_list_supports_role_filtering()
    {
        // Create an admin user
        $admin = User::factory()->create(['role' => 'admin']);
        $admin->assignRole('admin');

        // Create users with different roles
        $provider = User::factory()->create(['role' => 'provider']);
        $provider->assignRole('provider');
        
        $patient1 = User::factory()->create(['role' => 'patient']);
        $patient1->assignRole('patient');
        
        $patient2 = User::factory()->create(['role' => 'patient']);
        $patient2->assignRole('patient');

        // Filter by provider role
        $response = $this->actingAs($admin)
            ->getJson('/users-list?role=provider');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(1, $data['data']);
        $this->assertEquals('provider', $data['data'][0]['role']);

        // Filter by patient role
        $response = $this->actingAs($admin)
            ->getJson('/users-list?role=patient');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(2, $data['data']);
    }

    /** @test */
    public function users_list_combines_search_and_role_filtering()
    {
        // Create an admin user
        $admin = User::factory()->create(['role' => 'admin']);
        $admin->assignRole('admin');

        // Create test users
        $providerJohn = User::factory()->create(['name' => 'John Provider', 'role' => 'provider']);
        $providerJohn->assignRole('provider');
        
        $patientJohn = User::factory()->create(['name' => 'John Patient', 'role' => 'patient']);
        $patientJohn->assignRole('patient');
        
        $providerJane = User::factory()->create(['name' => 'Jane Provider', 'role' => 'provider']);
        $providerJane->assignRole('provider');

        // Search for "John" with provider role
        $response = $this->actingAs($admin)
            ->getJson('/users-list?search=John&role=provider');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(1, $data['data']);
        $this->assertEquals('John Provider', $data['data'][0]['name']);
        $this->assertEquals('provider', $data['data'][0]['role']);
    }

    /** @test */
    public function non_admin_users_cannot_access_users_list()
    {
        // Create a patient user
        $patient = User::factory()->create(['role' => 'patient']);
        $patient->assignRole('patient');

        $response = $this->actingAs($patient)
            ->getJson('/users-list');

        $response->assertStatus(403);
    }
}
