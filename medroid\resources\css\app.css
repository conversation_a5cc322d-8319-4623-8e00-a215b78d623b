/* Tailwind CSS v4 Configuration */
@import "tailwindcss";

/* Source files for Tailwind to scan */
@source "./resources/**/*.js";
@source "./resources/**/*.ts";
@source "./resources/**/*.vue";
@source "./resources/**/*.html";
@source "./resources/**/*.blade.php";
@source "../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php";
@source "../../storage/framework/views/*.php";

/* Custom variant */
@custom-variant dark (&:is(.dark *));

/* Medroid Color System - Based on Flutter App */
:root {
  /* Primary Colors */
  --color-teal-surge: #17C3B2;
  --color-mint-glow: #8BE9C8;
  --color-midnight-navy: #032B3E;
  --color-cloud-white: #FBFCFB;
  --color-coral-pop: #FF9F6E;
  --color-slate-grey: #6E7A8A;

  /* Background Colors */
  --color-background-light: #F3F9F7;
  --color-surface-light: #FFFFFF;
  --color-surface-dark: #0A3D53;

  /* Text Colors */
  --color-text-primary-light: var(--color-midnight-navy);
  --color-text-secondary-light: var(--color-slate-grey);
  --color-text-primary-dark: #FFFFFF;
  --color-text-secondary-dark: #B0B7C3;

  /* Semantic Colors */
  --color-error: #ED4337;
  --color-success: #4CAF50;
  --color-warning: #FFC107;
  --color-info: #2196F3;
}

/* Custom Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(23, 195, 178, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(23, 195, 178, 0.6);
  }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  50% { border-color: transparent; }
}

/* Utility Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.8s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-typing {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
}

/* Gradient Backgrounds */
.bg-medroid-primary {
  background: linear-gradient(135deg, var(--color-teal-surge) 0%, var(--color-mint-glow) 100%);
}

.bg-medroid-secondary {
  background: linear-gradient(135deg, var(--color-midnight-navy) 0%, #0A4D6A 100%);
}

.bg-medroid-accent {
  background: linear-gradient(135deg, var(--color-coral-pop) 0%, #FFBB91 100%);
}

.bg-medroid-surface {
  background: linear-gradient(135deg, var(--color-background-light) 0%, var(--color-cloud-white) 100%);
}

/* Text Gradients */
.text-medroid-primary {
  background: linear-gradient(135deg, var(--color-teal-surge) 0%, var(--color-mint-glow) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-medroid-accent {
  background: linear-gradient(135deg, var(--color-coral-pop) 0%, #FFBB91 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Theme configuration */
@theme {
  /* Font families */
  --font-sans: Instrument Sans, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  /* Primary colors */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;
  --color-primary-950: #082f49;

  /* Secondary colors */
  --color-secondary-50: #f0fdfa;
  --color-secondary-100: #ccfbf1;
  --color-secondary-200: #99f6e4;
  --color-secondary-300: #5eead4;
  --color-secondary-400: #2dd4bf;
  --color-secondary-500: #14b8a6;
  --color-secondary-600: #0d9488;
  --color-secondary-700: #0f766e;
  --color-secondary-800: #115e59;
  --color-secondary-900: #134e4a;
  --color-secondary-950: #042f2e;

  /* Medroid brand colors */
  --color-medroid-navy: #032B3E;
  --color-medroid-teal: #17C3B2;
  --color-medroid-mint: #8BE9C8;
  --color-medroid-cream: #FBFCFB;
  --color-medroid-sage: #F3F9F7;
  --color-medroid-slate: #6E7A8A;
  --color-medroid-orange: #FF9F6E;
  --color-medroid-border: #E5E7EB;

  /* Semantic colors for UI components */
  --color-destructive: #DC2626;
  --color-destructive-foreground: #FFFFFF;
}

/* Custom styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-medroid-navy text-white hover:bg-medroid-navy/90 focus:ring-2 focus:ring-medroid-navy/50 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-medroid-teal text-white hover:bg-medroid-teal/90 focus:ring-2 focus:ring-medroid-teal/50 focus:ring-offset-2;
  }

  .btn-link {
    @apply text-medroid-teal hover:text-medroid-teal/80 underline;
  }

  .form-control {
    @apply block w-full px-3 py-2 border border-medroid-border rounded-md shadow-sm focus:outline-none focus:ring-medroid-teal focus:border-medroid-teal bg-medroid-cream;
  }

  .form-control.is-invalid {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
  }

  .invalid-feedback {
    @apply text-red-600 text-sm mt-1;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  .card-header {
    @apply bg-gray-50 px-4 py-3 border-b border-gray-200 font-semibold text-gray-700;
  }

  .card-body {
    @apply p-6;
  }

  .alert {
    @apply p-4 mb-4 rounded-md;
  }

  .alert-success {
    @apply bg-green-100 text-green-800;
  }

  .alert-danger {
    @apply bg-red-100 text-red-800;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom components using Tailwind utilities */
.notification-manager .nav-tabs .nav-link {
  @apply cursor-pointer;
}

.notification-manager .table th,
.notification-manager .table td {
  @apply align-middle;
}

.notification-manager .badge {
  @apply capitalize;
}

@import "tw-animate-css";

/* Global Modal and Overlay Fixes */
@layer utilities {
  /* Modal backdrop/overlay classes */
  .modal-backdrop,
  .backdrop,
  .overlay {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  /* Fix problematic white backgrounds in modals */
  .bg-white.bg-opacity-20,
  .bg-white.bg-opacity-30,
  .bg-white.bg-opacity-40,
  .bg-white.bg-opacity-50 {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  /* Common modal background patterns */
  .bg-black.bg-opacity-50,
  .bg-black.bg-opacity-75,
  .bg-gray-900.bg-opacity-50,
  .bg-gray-900.bg-opacity-75 {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  /* Specific modal overlay patterns */
  .fixed.inset-0.bg-black,
  .fixed.inset-0.bg-gray-900,
  .absolute.inset-0.bg-black,
  .absolute.inset-0.bg-gray-900 {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  /* Z-index fixes for modals */
  .modal-overlay {
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  .modal-content {
    z-index: 1001;
  }
  
  /* Force all fixed inset modal overlays to have proper background */
  .fixed.inset-0[class*="bg-"] {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
}

/* Component-specific modal fixes */
@layer components {
  /* Generic modal wrapper */
  .modal-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  /* Override any problematic Tailwind opacity classes */
  .bg-opacity-0 { background-color: transparent !important; }
  .bg-opacity-5 { background-color: rgba(0, 0, 0, 0.05) !important; }
  .bg-opacity-10 { background-color: rgba(0, 0, 0, 0.1) !important; }
  .bg-opacity-20 { background-color: rgba(0, 0, 0, 0.2) !important; }
  .bg-opacity-25 { background-color: rgba(0, 0, 0, 0.25) !important; }
  .bg-opacity-30 { background-color: rgba(0, 0, 0, 0.3) !important; }
  .bg-opacity-40 { background-color: rgba(0, 0, 0, 0.4) !important; }
  .bg-opacity-50 { background-color: rgba(0, 0, 0, 0.5) !important; }
  .bg-opacity-60 { background-color: rgba(0, 0, 0, 0.6) !important; }
  .bg-opacity-70 { background-color: rgba(0, 0, 0, 0.7) !important; }
  .bg-opacity-75 { background-color: rgba(0, 0, 0, 0.75) !important; }
  .bg-opacity-80 { background-color: rgba(0, 0, 0, 0.8) !important; }
  .bg-opacity-90 { background-color: rgba(0, 0, 0, 0.9) !important; }
  .bg-opacity-95 { background-color: rgba(0, 0, 0, 0.95) !important; }
  .bg-opacity-100 { background-color: rgba(0, 0, 0, 1) !important; }
  
  /* Robust catch-all for modal overlays */
  [class*="fixed"][class*="inset-0"][class*="z-"]:not([class*="modal-content"]):not([class*="modal-dialog"]) {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
  
  /* Specific white background fixes */
  .bg-white[class*="bg-opacity"] {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
}
