# Instagram API with Instagram Login - Setup Guide

## Overview

This project implements **Instagram API with Instagram Login** (the new replacement for the deprecated Instagram Basic Display API). This integration allows Business and Creator Instagram accounts to connect and share their health-related content in the Medroid discover feed.

## Key Information

- **App Name**: Medroid AI-IG
- **App ID**: ***************
- **API Type**: Instagram API with Instagram Login
- **Supported Accounts**: Business and Creator accounts only
- **Personal Accounts**: Not supported (Instagram API limitation)

## Hybrid Feed Strategy

### For Business/Creator Account Users:
✅ Can connect their Instagram Business/Creator accounts  
✅ Their health-related Instagram posts get imported into the feed  
✅ Can create local posts in the app  
✅ Can view the mixed feed (their Instagram posts + community posts)

### For Regular Users (No Instagram Connection):
✅ Can view ALL content in the feed (including Instagram posts from business accounts)  
✅ Can create local posts in the app  
✅ Can interact with all posts (like, comment, save)  
❌ Cannot connect their personal Instagram (API limitation)

## Development Setup with ngrok

### 1. Install and Setup ngrok

```bash
# Install ngrok (if not already installed)
brew install ngrok  # macOS
# or download from https://ngrok.com/

# Start ngrok tunnel for your local Laravel server
ngrok http 8000
```

### 2. Update Instagram App Configuration

1. Go to [Facebook Developers Console](https://developers.facebook.com/)
2. Navigate to your app: **Medroid AI-IG** (ID: ***************)
3. Go to **Instagram > Instagram API with Instagram Login**
4. Update **Valid OAuth Redirect URIs** to include:
   ```
   https://YOUR_NGROK_URL.ngrok.io/auth/instagram/callback
   ```

### 3. Update Environment Configuration

Update your `.env` file:

```env
# Replace YOUR_NGROK_URL with your actual ngrok URL
INSTAGRAM_REDIRECT_URI=https://YOUR_NGROK_URL.ngrok.io/auth/instagram/callback
```

## API Endpoints and Scopes

### Authorization URL
```
https://api.instagram.com/oauth/authorize
```

### Required Scopes
```
user_profile,user_media
```

### Token Exchange URL
```
https://api.instagram.com/oauth/access_token
```

### Graph API Base URL
```
https://graph.instagram.com
```

## Implementation Details

### Service Class
- **File**: `app/Services/InstagramService.php`
- **Key Methods**:
  - `getAuthorizationUrl($state)` - Generate auth URL
  - `getAccessToken($code)` - Exchange code for token
  - `getUserProfile($token, $userId)` - Get user profile
  - `connectAccount($user, $code)` - Connect Instagram account
  - `syncAccountContent($account)` - Sync user's media

### Controller
- **File**: `app/Http/Controllers/InstagramAuthController.php`
- **Routes**:
  - `GET /auth/instagram` - Redirect to Instagram auth
  - `GET /auth/instagram/callback` - Handle callback

### Model
- **File**: `app/Models/InstagramAccount.php`
- **Key Fields**:
  - `user_id` - Associated Medroid user
  - `instagram_user_id` - Instagram user ID
  - `username` - Instagram username
  - `account_type` - BUSINESS or CREATOR
  - `access_token` - Long-lived access token
  - `expires_at` - Token expiration (60 days)

## Testing the Integration

### 1. Start Your Development Environment

```bash
# Start Laravel server and Cloudflare tunnel
cd medroid_app_backend_v1.0
./start-cloudflare-tunnel.sh
```

### 2. Update Configuration

Copy the Cloudflare tunnel URL and update:
1. `.env` file with the tunnel URL: `./update-cloudflare-env.sh your-tunnel-url.trycloudflare.com`
2. Facebook Developer Console with the tunnel callback URL

### 3. Test the Flow

1. Login to your Medroid app
2. Go to `/discover` page
3. Click "Connect Instagram"
4. You should be redirected to Instagram authorization
5. Login with a Business or Creator Instagram account
6. Grant permissions
7. You should be redirected back with success message

## Troubleshooting

### "Invalid platform app" Error
- Ensure the Instagram app is configured for "Instagram API with Instagram Login"
- Verify the redirect URI matches exactly (including https/http)
- Check that the app has the correct permissions enabled

### "Only Business and Creator accounts" Error
- This is expected behavior for personal accounts
- The user needs to convert their account to Business or Creator
- Or use a different Instagram account that's already Business/Creator

### Token Exchange Failures
- Check that client_id and client_secret are correct
- Verify the redirect_uri matches exactly
- Ensure the authorization code hasn't expired (use immediately)

## Important Notes

1. **Account Type Validation**: The integration automatically validates that only Business and Creator accounts can connect
2. **Token Longevity**: Instagram API tokens are long-lived (60 days) and don't need frequent refresh
3. **Content Filtering**: Only health-related content is imported to the discover feed
4. **Error Handling**: Comprehensive logging is implemented for debugging
5. **Security**: State parameter validation prevents CSRF attacks

## Next Steps

After successful integration:
1. Test with multiple Business/Creator accounts
2. Verify content sync functionality
3. Test the mixed feed display
4. Implement webhook handling for real-time updates
5. Add content moderation features
