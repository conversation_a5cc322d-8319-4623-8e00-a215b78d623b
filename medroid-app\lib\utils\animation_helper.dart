import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// Enhanced animation utilities for smooth, beautiful interactions
class AnimationHelper {
  
  /// Common animation durations
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration verySlow = Duration(milliseconds: 800);

  /// Common curves
  static const Curve smoothCurve = Curves.easeInOutCubic;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve quickCurve = Curves.easeOutQuart;

  /// Slide animations
  static Widget slideInFromLeft(
    Widget child, {
    Duration duration = normal,
    Curve curve = smoothCurve,
    double offset = 50.0,
  }) {
    return child
        .animate()
        .slideX(
          begin: -offset,
          end: 0,
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration);
  }

  static Widget slideInFromRight(
    Widget child, {
    Duration duration = normal,
    Curve curve = smoothCurve,
    double offset = 50.0,
  }) {
    return child
        .animate()
        .slideX(
          begin: offset,
          end: 0,
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration);
  }

  static Widget slideInFromBottom(
    Widget child, {
    Duration duration = normal,
    Curve curve = smoothCurve,
    double offset = 50.0,
  }) {
    return child
        .animate()
        .slideY(
          begin: offset,
          end: 0,
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration);
  }

  static Widget slideInFromTop(
    Widget child, {
    Duration duration = normal,
    Curve curve = smoothCurve,
    double offset = 50.0,
  }) {
    return child
        .animate()
        .slideY(
          begin: -offset,
          end: 0,
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration);
  }

  /// Scale animations
  static Widget scaleIn(
    Widget child, {
    Duration duration = normal,
    Curve curve = bounceCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return child
        .animate()
        .scale(
          begin: Offset(begin, begin),
          end: Offset(end, end),
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration);
  }

  static Widget pulseScale(
    Widget child, {
    Duration duration = const Duration(milliseconds: 600),
    double scale = 1.1,
    bool repeat = true,
  }) {
    return child
        .animate(onPlay: (controller) => repeat ? controller.repeat(reverse: true) : null)
        .scale(
          end: Offset(scale, scale),
          duration: duration,
          curve: Curves.easeInOut,
        );
  }

  /// Fade animations
  static Widget fadeIn(
    Widget child, {
    Duration duration = normal,
    Curve curve = smoothCurve,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return child
        .animate()
        .fadeIn(
          duration: duration,
          curve: curve,
        );
  }

  /// Rotation animations
  static Widget rotateIn(
    Widget child, {
    Duration duration = normal,
    Curve curve = smoothCurve,
    double begin = -0.5,
    double end = 0.0,
  }) {
    return child
        .animate()
        .rotate(
          begin: begin,
          end: end,
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration);
  }

  static Widget spin(
    Widget child, {
    Duration duration = const Duration(seconds: 2),
    bool repeat = true,
  }) {
    return child
        .animate(onPlay: (controller) => repeat ? controller.repeat() : null)
        .rotate(
          duration: duration,
          curve: Curves.linear,
        );
  }

  /// Shimmer animation
  static Widget shimmer(
    Widget child, {
    Duration duration = const Duration(milliseconds: 1500),
    Color baseColor = const Color(0xFFE0E0E0),
    Color highlightColor = const Color(0xFFF5F5F5),
  }) {
    return child
        .animate(onPlay: (controller) => controller.repeat())
        .shimmer(
          duration: duration,
          color: highlightColor,
        );
  }

  /// Bounce animation
  static Widget bounce(
    Widget child, {
    Duration duration = const Duration(milliseconds: 800),
    bool repeat = false,
  }) {
    return child
        .animate(onPlay: (controller) => repeat ? controller.repeat() : null)
        .moveY(
          begin: 0,
          end: -20,
          duration: duration ~/ 2,
          curve: Curves.easeOut,
        )
        .then()
        .moveY(
          begin: -20,
          end: 0,
          duration: duration ~/ 2,
          curve: Curves.bounceOut,
        );
  }

  /// Flip animation
  static Widget flipIn(
    Widget child, {
    Duration duration = normal,
    Curve curve = smoothCurve,
    Axis direction = Axis.horizontal,
  }) {
    return child
        .animate()
        .flip(
          duration: duration,
          curve: curve,
        )
        .fadeIn(duration: duration);
  }

  /// Staggered list animation
  static Widget staggeredList({
    required List<Widget> children,
    Duration staggerDuration = const Duration(milliseconds: 100),
    Duration itemDuration = normal,
    Curve curve = smoothCurve,
  }) {
    return Column(
      children: children
          .asMap()
          .entries
          .map((entry) {
            final index = entry.key;
            final child = entry.value;
            return child
                .animate()
                .slideY(
                  begin: 30,
                  end: 0,
                  duration: itemDuration,
                  curve: curve,
                  delay: staggerDuration * index,
                )
                .fadeIn(
                  duration: itemDuration,
                  delay: staggerDuration * index,
                );
          })
          .toList(),
    );
  }

  /// Card hover animation
  static Widget hoverCard(
    Widget child, {
    Duration duration = fast,
    double elevation = 8.0,
  }) {
    return child
        .animate()
        .elevation(
          end: elevation,
          duration: duration,
          curve: Curves.easeOut,
        )
        .scale(
          end: const Offset(1.02, 1.02),
          duration: duration,
          curve: Curves.easeOut,
        );
  }

  /// Page transition animations
  static Widget pageSlideTransition(
    Widget child, {
    Duration duration = normal,
    SlideDirection direction = SlideDirection.right,
  }) {
    late Offset begin;
    switch (direction) {
      case SlideDirection.left:
        begin = const Offset(-1.0, 0.0);
        break;
      case SlideDirection.right:
        begin = const Offset(1.0, 0.0);
        break;
      case SlideDirection.up:
        begin = const Offset(0.0, -1.0);
        break;
      case SlideDirection.down:
        begin = const Offset(0.0, 1.0);
        break;
    }

    return child
        .animate()
        .slide(
          begin: begin,
          end: Offset.zero,
          duration: duration,
          curve: Curves.easeInOutCubic,
        );
  }

  /// Loading animation
  static Widget loadingDots({
    int count = 3,
    Duration duration = const Duration(milliseconds: 600),
    Color color = Colors.blue,
    double size = 8.0,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(count, (index) {
        return Container(
          width: size,
          height: size,
          margin: EdgeInsets.symmetric(horizontal: size / 4),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        )
            .animate(onPlay: (controller) => controller.repeat())
            .scale(
              begin: const Offset(0.5, 0.5),
              end: const Offset(1.0, 1.0),
              duration: duration,
              delay: Duration(milliseconds: index * 200),
              curve: Curves.easeInOut,
            )
            .then()
            .scale(
              begin: const Offset(1.0, 1.0),
              end: const Offset(0.5, 0.5),
              duration: duration,
              curve: Curves.easeInOut,
            );
      }),
    );
  }

  /// Attention grabber animation
  static Widget attention(
    Widget child, {
    Duration duration = const Duration(milliseconds: 1000),
    bool repeat = true,
  }) {
    return child
        .animate(onPlay: (controller) => repeat ? controller.repeat() : null)
        .shake(
          duration: duration ~/ 4,
          curve: Curves.easeInOut,
        )
        .then(delay: duration * 3 ~/ 4)
        .scale(
          end: const Offset(1.1, 1.1),
          duration: duration ~/ 8,
          curve: Curves.easeOut,
        )
        .then()
        .scale(
          begin: const Offset(1.1, 1.1),
          end: const Offset(1.0, 1.0),
          duration: duration ~/ 8,
          curve: Curves.easeIn,
        );
  }
}

/// Slide direction enum
enum SlideDirection { left, right, up, down }