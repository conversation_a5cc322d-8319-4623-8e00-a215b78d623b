import '../css/app.css';

import { createInertiaApp, router } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';
import { useFontSize } from './composables/useFontSize';
import axios from 'axios';

// Extend ImportMeta interface for Vite...
declare module 'vite/client' {
    interface ImportMetaEnv {
        readonly VITE_APP_NAME: string;
        [key: string]: string | boolean | undefined;
    }

    interface ImportMeta {
        readonly env: ImportMetaEnv;
        readonly glob: <T>(pattern: string) => Record<string, () => Promise<T>>;
    }
}

// Extend Window interface for axios
declare global {
    interface Window {
        axios: typeof axios;
    }
}

const appName = import.meta.env.VITE_APP_NAME || 'Medroid';

// Configure axios for CSRF and session-based authentication
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.withCredentials = true;

// Set base URL to current domain to avoid hardcoded URLs
axios.defaults.baseURL = window.location.origin;

// Function to get CSRF token from meta tag
const getCsrfToken = () => {
    const csrfToken = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
    return csrfToken ? csrfToken.content : null;
};

// Function to refresh CSRF token
const refreshCsrfToken = async () => {
    try {
        // First try the sanctum csrf-cookie endpoint
        const sanctumResponse = await fetch('/sanctum/csrf-cookie', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
            }
        });

        if (sanctumResponse.ok) {
            // Wait a bit for the cookie to be set
            await new Promise(resolve => setTimeout(resolve, 100));
            let token = getCsrfToken();
            if (token) {
                return token;
            }
        }

        // Fallback: try our custom csrf-token endpoint
        const tokenResponse = await fetch('/csrf-token', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
            }
        });

        if (tokenResponse.ok) {
            const data = await tokenResponse.json();
            if (data.csrf_token) {
                // Update the meta tag with the new token
                const csrfMeta = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
                if (csrfMeta) {
                    csrfMeta.content = data.csrf_token;
                }
                return data.csrf_token;
            }
        }
    } catch (error) {
        console.error('Failed to refresh CSRF token:', error);
    }
    return null;
};

// Set initial CSRF token
const initialCsrfToken = getCsrfToken();
if (initialCsrfToken) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = initialCsrfToken;
}

// Initialize CSRF token at startup
const initializeCsrfToken = async () => {
    let token = getCsrfToken();
    
    if (!token) {
        console.log('Initializing CSRF token...');
        token = await refreshCsrfToken();
        
        if (token) {
            console.log('CSRF token initialized successfully');
        } else {
            console.warn('Failed to initialize CSRF token');
        }
    }
    
    return token;
};

// Add request interceptor to ensure fresh CSRF token on each request
axios.interceptors.request.use(async (config) => {
    // Always get the current CSRF token from the meta tag
    let currentCsrfToken = getCsrfToken();

    // If no token is found, try to refresh it
    if (!currentCsrfToken) {
        console.warn('No CSRF token found, attempting to refresh...');
        currentCsrfToken = await refreshCsrfToken();
    }

    if (currentCsrfToken) {
        config.headers['X-CSRF-TOKEN'] = currentCsrfToken;
    } else {
        console.error('Unable to obtain CSRF token');
    }

    // Ensure proper headers for all requests
    config.headers['Accept'] = config.headers['Accept'] || 'application/json';

    // Only set Content-Type to application/json if it's not already set and it's not FormData
    if (!config.headers['Content-Type'] && !(config.data instanceof FormData)) {
        config.headers['Content-Type'] = 'application/json';
    }

    return config;
}, (error) => {
    return Promise.reject(error);
});

// Add response interceptor to handle CSRF token mismatch and other auth issues
axios.interceptors.response.use((response) => {
    return response;
}, async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 419 && !originalRequest._retry) {
        // CSRF token mismatch - try to refresh token and retry once
        console.warn('CSRF token mismatch detected, attempting to refresh and retry...');
        originalRequest._retry = true;

        try {
            const newToken = await refreshCsrfToken();
            if (newToken) {
                // Update both the request and global defaults
                originalRequest.headers['X-CSRF-TOKEN'] = newToken;
                axios.defaults.headers.common['X-CSRF-TOKEN'] = newToken;
                
                // Wait a bit to ensure token is properly set
                await new Promise(resolve => setTimeout(resolve, 50));
                
                return axios(originalRequest);
            } else {
                console.error('Failed to refresh CSRF token, reloading page...');
                // Don't reload immediately, give user a chance to retry
                setTimeout(() => {
                    if (window.confirm('Session expired. Reload page to continue?')) {
                        window.location.reload();
                    }
                }, 1000);
            }
        } catch (tokenError) {
            console.error('Error during token refresh:', tokenError);
        }
    }

    // Handle 500 errors with retry logic (for transient database issues, but not for logout)
    if (error.response?.status === 500 && !originalRequest._serverRetry && !originalRequest.url?.includes('/logout')) {
        console.warn('Server error detected, attempting retry...');
        originalRequest._serverRetry = true;
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        try {
            return axios(originalRequest);
        } catch (retryError) {
            console.error('Server error retry failed:', retryError);
            // Let the original error bubble up after retry failure
        }
    }
    
    // Special handling for logout requests - don't interfere
    if (originalRequest.url?.includes('/logout')) {
        console.log('Logout request - not intercepting');
        return Promise.reject(error);
    }

    // Handle other authentication errors
    if (error.response?.status === 401) {
        // Don't redirect to login if we're in the process of logging out
        if (!originalRequest.url?.includes('/logout')) {
            console.warn('Authentication failed, redirecting to login...');
            window.location.href = '/login';
        }
    }

    return Promise.reject(error);
});

// Make axios available globally for Vue components
window.axios = axios;

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./pages/${name}.vue`, import.meta.glob<DefineComponent>('./pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
}).then(() => {
    // Global error handler for Inertia requests
    router.on('error', (error: any) => {
        console.log('Inertia request error:', error);

        // Handle logout-related errors gracefully
        if (error.response?.url?.includes('/logout') || error.response?.config?.url?.includes('/logout')) {
            console.log('Logout error handled gracefully, redirecting to home');
            // Force redirect to home page on logout errors
            window.location.href = '/';
            return;
        }

        // Handle CSRF errors on navigation
        if (error.response?.status === 419) {
            console.warn('CSRF error on Inertia request, reloading page');
            window.location.reload();
            return;
        }
    });
}).catch(error => {
    console.error('Error initializing Inertia app:', error);
});

// This will set light / dark mode on page load...
initializeTheme();

// Initialize font size system
const { applyFontSizeToDocument } = useFontSize();
applyFontSizeToDocument();

// Clear any problematic service workers on app load
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
        for(const registration of registrations) {
            // Only unregister service workers that might be causing issues
            if (registration.scope.includes('datadog') ||
                registration.scope.includes('sw.js')) {
                registration.unregister();
            }
        }
    }).catch(function(error) {
        // Service worker cleanup failed silently
    });
}

// Initialize CSRF token on app startup for better reliability
initializeCsrfToken().catch(error => {
    console.warn('Failed to initialize CSRF token on startup:', error);
});
