<?php

namespace App\Http\Controllers;

use App\Models\SocialContent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Services\FileUploadService;

class SocialFeedController extends Controller
{
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);

            // Get mixed content from all sources and users (Instagram + Local)
            $query = SocialContent::with(['user', 'comments'])
                ->where('filtered_status', 'approved');

            // Include ALL sources: internal posts + Instagram content from ALL users
            $query->whereIn('source', ['internal', 'instagram']);

            // Additional health-related filtering for extra safety
            // This ensures only health-related content is shown even if some non-health content slipped through
            $query->where(function($q) {
                $q->where(function($subQ) {
                    // Posts with health topics
                    $subQ->whereNotNull('health_topics')
                         ->where('health_topics', '!=', '[]')
                         ->where('health_topics', '!=', 'null');
                })->orWhere(function($subQ) {
                    // Posts with health-related keywords in caption
                    $healthKeywords = [
                        'health', 'wellness', 'fitness', 'meditation', 'yoga', 'mindfulness', 
                        'selfcare', 'mentalhealth', 'nutrition', 'workout', 'exercise', 
                        'healing', 'therapy', 'medical', 'motivation', 'inspiration', 
                        'wellbeing', 'balance', 'peace', 'calm', 'spiritual'
                    ];
                    
                    foreach ($healthKeywords as $keyword) {
                        $subQ->orWhere('caption', 'like', "%{$keyword}%");
                    }
                });
            });

            if ($request->has('content_type')) {
                $query->where('content_type', $request->content_type);
            }

            // Instagram-like feed algorithm: Recent posts with some randomization
            $sortBy = $request->get('sort_by', 'mixed');

            if ($sortBy === 'latest') {
                // Pure chronological order
                $query->orderBy('published_at', 'desc')
                      ->orderBy('created_at', 'desc');
            } elseif ($sortBy === 'relevance') {
                // Relevance-based
                $query->orderBy('relevance_score', 'desc')
                      ->orderBy('published_at', 'desc');
            } else {
                // Mixed algorithm (Instagram-like) - Randomly mix Instagram + Local content
                $recentCutoff = now()->subDays(7);

                // Enhanced mixing algorithm that ensures good distribution
                $query->orderByRaw("
                    CASE
                        WHEN published_at >= ? THEN
                            -- Recent posts get higher priority with randomization
                            (relevance_score * 0.6 + RAND() * 0.4) *
                            -- Slight boost for Instagram content but not too much
                            (CASE WHEN source = 'instagram' THEN 1.05 ELSE 1.0 END) *
                            -- Boost for posts with engagement
                            (1 + LEAST(COALESCE(JSON_EXTRACT(engagement_metrics, '$.likes'), 0) / 100, 0.5))
                        ELSE
                            -- Older posts rely more on relevance and engagement
                            (relevance_score * 0.7 + RAND() * 0.2) *
                            (1 + LEAST(COALESCE(JSON_EXTRACT(engagement_metrics, '$.likes'), 0) / 200, 0.3))
                    END DESC,
                    -- Secondary sort by recency with small random factor
                    (UNIX_TIMESTAMP(published_at) + RAND() * 3600) DESC
                ", [$recentCutoff]);
            }

            $content = $query->paginate($limit, ['*'], 'page', $page);

            // Add user-specific interaction data and clean up orphaned posts
            $filteredItems = collect();
            
            $content->each(function ($item) use ($user, &$filteredItems) {
                try {
                    $item->liked = $item->likes()->where('user_id', $user->id)->exists();
                    $item->saved = $item->saves()->where('user_id', $user->id)->exists();

                    // Add user permissions for this post
                    $item->can_edit = $item->user_id === $user->id;
                    $item->can_delete = $item->user_id === $user->id;

                    // Update engagement metrics with actual comments count
                    $engagementMetrics = $item->engagement_metrics ?? [];
                    $engagementMetrics['comments'] = $item->comments->count();
                    $item->engagement_metrics = $engagementMetrics;

                    // Handle user information for different post types
                    if ($item->source === 'internal' && !$item->user) {
                        // For internal posts without a user relationship, add the current user's information
                        $item->user = [
                            'id' => $user->id,
                            'name' => $user->name,
                            'avatar' => $user->profile_image ?? null
                        ];
                        $filteredItems->push($item);
                    } else if ($item->source === 'instagram') {
                        // For Instagram posts, check if they have proper user association
                        if (!$item->user || !$item->user_id) {
                            // Instagram post without proper user - this shouldn't happen after disconnect cleanup
                            \Log::warning('Found orphaned Instagram post, removing it', ['post_id' => $item->id]);
                            $item->delete();
                            return; // Skip this post
                        }
                        
                        // Check if the Instagram account is still active for this user
                        $activeAccount = \App\Models\InstagramAccount::where('user_id', $item->user_id)
                            ->where('is_active', true)
                            ->exists();
                            
                        if (!$activeAccount) {
                            // Instagram account was disconnected, remove this post
                            \Log::info('Removing Instagram post from disconnected account', ['post_id' => $item->id]);
                            $item->delete();
                            return; // Skip this post
                        }
                        
                        // Format the user data consistently for active Instagram posts
                        $item->user = [
                            'id' => $item->user->id,
                            'name' => $item->user->name,
                            'avatar' => $item->user->profile_image ?? null
                        ];
                        $filteredItems->push($item);
                    } else if ($item->user) {
                        // Format the user data consistently for other sources
                        $item->user = [
                            'id' => $item->user->id,
                            'name' => $item->user->name,
                            'avatar' => $item->user->profile_image ?? null
                        ];
                        $filteredItems->push($item);
                    } else {
                        // Post without any user association - shouldn't happen
                        \Log::warning('Found post without user association, skipping', ['post_id' => $item->id, 'source' => $item->source]);
                        return; // Skip this post
                    }
                } catch (\Exception $e) {
                    // If there's an issue with a specific item, skip it and log the error
                    \Log::error('Error processing feed item: ' . $e->getMessage(), ['post_id' => $item->id ?? 'unknown']);
                }
            });
            
            // Update the content collection with filtered items
            $content->setCollection($filteredItems);

            return response()->json($content);
        } catch (\Exception $e) {
            \Log::error('Error in feed index: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading feed',
                'error' => $e->getMessage(),
                'data' => [],
                'current_page' => 1,
                'last_page' => 1,
                'next_page_url' => null,
                'prev_page_url' => null,
                'total' => 0
            ], 500);
        }
    }

    public function topics()
    {
        // Get most popular health topics from internal posts only
        $topics = SocialContent::where('filtered_status', 'approved')
            ->where('source', 'internal') // Only consider internal posts
            ->get()
            ->pluck('health_topics')
            ->flatten()
            ->countBy()
            ->sortDesc()
            ->take(20)
            ->keys();

        return response()->json(['topics' => $topics]);
    }

    public function saveContent(Request $request, $contentId)
    {
        $user = $request->user();
        $content = SocialContent::findOrFail($contentId);

        // Toggle save status
        if ($content->saves()->where('user_id', $user->id)->exists()) {
            $content->saves()->detach($user->id);
            $saved = false;
        } else {
            $content->saves()->attach($user->id);
            $saved = true;
        }

        // Get save count and update engagement metrics
        $saveCount = $content->saves()->count();
        $engagementMetrics = $content->engagement_metrics;

        if ($content->source === 'instagram') {
            // For Instagram posts, preserve original Instagram saves and add local saves
            if (!isset($engagementMetrics['original_instagram_saves'])) {
                $engagementMetrics['original_instagram_saves'] = $engagementMetrics['saves'] ?? 0;
            }
            $totalSaves = ($engagementMetrics['original_instagram_saves'] ?? 0) + $saveCount;
            $engagementMetrics['saves'] = $totalSaves;
        } else {
            // For local posts, just use the local save count
            $engagementMetrics['saves'] = $saveCount;
        }

        $content->engagement_metrics = $engagementMetrics;
        $content->save();

        return response()->json([
            'message' => $saved ? 'Content saved' : 'Content unsaved',
            'saved' => $saved,
            'save_count' => $engagementMetrics['saves'],
            'local_save_count' => $saveCount,
            'original_instagram_saves' => $engagementMetrics['original_instagram_saves'] ?? 0
        ]);
    }

    public function likeContent(Request $request, $contentId)
    {
        $user = $request->user();
        $content = SocialContent::findOrFail($contentId);

        // Toggle like status
        if ($content->likes()->where('user_id', $user->id)->exists()) {
            $content->likes()->detach($user->id);
            $liked = false;
        } else {
            $content->likes()->attach($user->id);
            $liked = true;
        }

        // Get local likes count
        $localLikeCount = $content->likes()->count();

        // Handle engagement metrics differently for Instagram vs local posts
        $engagementMetrics = $content->engagement_metrics;

        if ($content->source === 'instagram') {
            // For Instagram posts, preserve original Instagram likes and only update local count
            // Don't overwrite the engagement_metrics['likes'] as it contains Instagram data
            $totalLikes = ($engagementMetrics['original_instagram_likes'] ?? $engagementMetrics['likes'] ?? 0) + $localLikeCount;

            // Store original Instagram count if not already stored
            if (!isset($engagementMetrics['original_instagram_likes'])) {
                $engagementMetrics['original_instagram_likes'] = $engagementMetrics['likes'] ?? 0;
            }

            // Update total likes (Instagram + local)
            $engagementMetrics['likes'] = $totalLikes;
        } else {
            // For local posts, just use the local like count
            $engagementMetrics['likes'] = $localLikeCount;
        }

        $content->engagement_metrics = $engagementMetrics;
        $content->save();

        return response()->json([
            'message' => $liked ? 'Content liked' : 'Content unliked',
            'liked' => $liked,
            'like_count' => $engagementMetrics['likes'], // Total count (Instagram + local)
            'local_like_count' => $localLikeCount, // Just local likes
            'original_instagram_likes' => $engagementMetrics['original_instagram_likes'] ?? 0
        ]);
    }

    /**
     * Create a new social post with media.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, FileUploadService $fileUploadService)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'caption' => 'required|string|max:1000',
            'hashtags' => 'nullable|json',
            'source' => 'required|string|in:internal',
            'media' => 'nullable|file|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $user = $request->user();

            // Parse hashtags from JSON
            $hashtags = $request->hashtags ? json_decode($request->hashtags, true) : [];

            // Default values
            $mediaUrl = null;
            $contentType = 'text';

            // Handle media upload if provided using file management system
            $uploadedFile = null;
            if ($request->hasFile('media')) {
                $uploadedFile = $fileUploadService->uploadFile(
                    $request->file('media'),
                    'general', // Use general category for social media
                    'Social Media Post by ' . $user->name,
                    'Social media post image',
                    true // Make social media posts public
                );

                $mediaUrl = $uploadedFile->url;
                $contentType = 'image';

                \Illuminate\Support\Facades\Log::info('Social media file uploaded via file management system', [
                    'file_id' => $uploadedFile->id,
                    'file_url' => $uploadedFile->url
                ]);
            }

            // Create the social content
            $content = SocialContent::create([
                'source' => 'internal',
                'source_id' => (string) Str::uuid(),
                'content_type' => $contentType,
                'media_url' => $mediaUrl,
                'caption' => $request->caption,
                'health_topics' => $hashtags, // Store hashtags in health_topics field for now
                'relevance_score' => 1.0, // Default high relevance for user-created content
                'engagement_metrics' => [
                    'likes' => 0,
                    'shares' => 0,
                    'saves' => 0,
                    'comments' => 0,
                ],
                'filtered_status' => 'approved', // Auto-approve user content for now
                'user_id' => $user->id, // Store the user ID with the post
            ]);

            // Track file usage for social content if media was uploaded
            if ($uploadedFile) {
                \App\Models\FileUsage::create([
                    'file_id' => $uploadedFile->id,
                    'usable_type' => get_class($content),
                    'usable_id' => $content->id,
                    'usage_type' => 'social_media',
                ]);
            }

            return response()->json([
                'message' => 'Post created successfully',
                'post' => $content
            ], 201);

        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error creating post: ' . $e->getMessage());

            return response()->json([
                'message' => 'Failed to create post: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search posts by hashtags and keywords
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function search(Request $request)
    {
        try {
            $query = $request->get('q', '');

            if (empty($query) || strlen($query) < 2) {
                return response()->json([
                    'posts' => [],
                    'message' => 'Query too short'
                ]);
            }

            $searchQuery = SocialContent::with(['user', 'comments'])
                ->where('filtered_status', 'approved')
                ->whereIn('source', ['internal', 'instagram']);

            // Search in caption and hashtags
            $searchQuery->where(function($q) use ($query) {
                $q->where('caption', 'like', "%{$query}%")
                  ->orWhereJsonContains('health_topics', $query)
                  ->orWhere(function($subQ) use ($query) {
                      // Search for hashtags (with or without #)
                      $hashtag = str_starts_with($query, '#') ? $query : "#{$query}";
                      $subQ->whereJsonContains('health_topics', $hashtag);
                  });
            });

            $posts = $searchQuery->orderBy('created_at', 'desc')
                ->limit(20)
                ->get();

            // Transform posts for frontend and filter out problematic ones
            $transformedPosts = $posts->filter(function ($post) {
                // Filter out Instagram posts from disconnected accounts
                if ($post->source === 'instagram') {
                    if (!$post->user || !$post->user_id) {
                        \Log::warning('Found orphaned Instagram post in search, removing it', ['post_id' => $post->id]);
                        $post->delete();
                        return false;
                    }
                    
                    $activeAccount = \App\Models\InstagramAccount::where('user_id', $post->user_id)
                        ->where('is_active', true)
                        ->exists();
                        
                    if (!$activeAccount) {
                        \Log::info('Removing Instagram post from disconnected account in search', ['post_id' => $post->id]);
                        $post->delete();
                        return false;
                    }
                }
                
                return true;
            })->map(function ($post) use ($request) {
                $user = $request->user();

                return [
                    'id' => $post->id,
                    'source' => $post->source,
                    'source_id' => $post->source_id,
                    'content_type' => $post->content_type,
                    'media_url' => $post->media_url,
                    'thumbnail_url' => $post->thumbnail_url,
                    'video_url' => $post->video_url,
                    'caption' => $post->caption,
                    'health_topics' => $post->health_topics,
                    'relevance_score' => $post->relevance_score,
                    'engagement_metrics' => $post->engagement_metrics,
                    'filtered_status' => $post->filtered_status,
                    'created_at' => $post->created_at,
                    'user' => [
                        'id' => $post->user->id ?? null,
                        'name' => $post->user->name ?? 'Unknown User',
                        'profile_image' => $post->user->profile_image ?? null,
                    ],
                    'liked' => $user ? $post->likes()->where('user_id', $user->id)->exists() : false,
                    'saved' => false, // TODO: Implement saved posts
                    'can_edit' => $user && $post->user_id === $user->id,
                    'can_delete' => $user && $post->user_id === $user->id,
                ];
            });

            return response()->json([
                'posts' => $transformedPosts,
                'query' => $query,
                'count' => $transformedPosts->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Search failed: ' . $e->getMessage(),
                'posts' => []
            ], 500);
        }
    }

    /**
     * Report content
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $contentId
     * @return \Illuminate\Http\Response
     */
    public function reportContent(Request $request, $contentId)
    {
        try {
            $user = $request->user();
            $content = SocialContent::findOrFail($contentId);

            // Create a report record (you might want to create a reports table)
            // For now, we'll just log it
            Log::info('Content reported', [
                'content_id' => $contentId,
                'user_id' => $user->id,
                'reason' => $request->get('reason', 'inappropriate_content'),
                'timestamp' => now()
            ]);

            return response()->json([
                'message' => 'Content reported successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to report content'
            ], 500);
        }
    }

    /**
     * Get comments for content
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $contentId
     * @return \Illuminate\Http\Response
     */
    public function getComments(Request $request, $contentId)
    {
        try {
            $content = SocialContent::findOrFail($contentId);
            $comments = $content->comments()
                ->with('user')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'comments' => $comments->map(function ($comment) {
                    return [
                        'id' => $comment->id,
                        'content' => $comment->content,
                        'created_at' => $comment->created_at,
                        'user' => [
                            'id' => $comment->user->id,
                            'name' => $comment->user->name,
                            'profile_image' => $comment->user->profile_image,
                        ]
                    ];
                })
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load comments',
                'comments' => []
            ], 500);
        }
    }

    /**
     * Add comment to content
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $contentId
     * @return \Illuminate\Http\Response
     */
    public function addComment(Request $request, $contentId)
    {
        try {
            $user = $request->user();
            $content = SocialContent::findOrFail($contentId);

            $validator = Validator::make($request->all(), [
                'content' => 'required|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $comment = $content->comments()->create([
                'user_id' => $user->id,
                'content' => $request->content
            ]);

            // Update comment count
            $metrics = $content->engagement_metrics;
            $metrics['comments'] = ($metrics['comments'] ?? 0) + 1;
            $content->update(['engagement_metrics' => $metrics]);

            return response()->json([
                'comment' => [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'created_at' => $comment->created_at,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'profile_image' => $user->profile_image,
                    ]
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to add comment'
            ], 500);
        }
    }

    /**
     * Delete a social post.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $contentId
     * @return \Illuminate\Http\Response
     */
    public function deleteContent(Request $request, $contentId)
    {
        try {
            $user = $request->user();
            $content = SocialContent::findOrFail($contentId);

            // Check if the user is the owner of the post
            if ($content->user_id !== $user->id) {
                return response()->json([
                    'message' => 'You are not authorized to delete this post'
                ], 403);
            }

            // If the post has a media file, delete it from storage
            if ($content->media_url && $content->source === 'internal') {
                // Extract the filename from the URL
                $path = parse_url($content->media_url, PHP_URL_PATH);
                if ($path) {
                    $relativePath = str_replace('/storage/', '', $path);
                    if (Storage::disk('public')->exists($relativePath)) {
                        Storage::disk('public')->delete($relativePath);
                    }
                }
            }

            // Delete the post
            $content->delete();

            return response()->json([
                'message' => 'Post deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error deleting post: ' . $e->getMessage());

            return response()->json([
                'message' => 'Failed to delete post: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get saved posts for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getSavedPosts(Request $request)
    {
        try {
            $user = $request->user();

            // Get saved posts for the user
            $savedPosts = SocialContent::whereHas('saves', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->with(['user', 'saves', 'likes'])
            ->orderBy('created_at', 'desc')
            ->get();

            // Transform the data to include user interaction status
            $transformedPosts = $savedPosts->map(function ($post) use ($user) {
                return [
                    'id' => $post->id,
                    'caption' => $post->caption,
                    'media_url' => $post->media_url,
                    'thumbnail_url' => $post->thumbnail_url,
                    'video_url' => $post->video_url,
                    'content_type' => $post->content_type,
                    'created_at' => $post->created_at,
                    'engagement_metrics' => $post->engagement_metrics,
                    'user' => [
                        'id' => $post->user->id,
                        'name' => $post->user->name,
                        'profile_image' => $post->user->profile_image,
                    ],
                    'liked' => $post->likes->contains('user_id', $user->id),
                    'saved' => true, // All posts in this list are saved
                ];
            });

            return response()->json([
                'posts' => $transformedPosts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to load saved posts'
            ], 500);
        }
    }

    /**
     * Get a specific post for sharing
     */
    public function getPost(Request $request, $contentId)
    {
        try {
            $post = SocialContent::with(['user', 'comments'])
                ->where('id', $contentId)
                ->where('filtered_status', 'approved')
                ->first();

            if (!$post) {
                return response()->json([
                    'success' => false,
                    'message' => 'Post not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $post
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get post', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to load post'
            ], 500);
        }
    }

    /**
     * Track post shares
     */
    public function shareContent(Request $request, $contentId)
    {
        try {
            $user = $request->user();
            $platform = $request->input('platform', 'unknown'); // whatsapp, linkedin, email, sms, copy

            $post = SocialContent::find($contentId);
            if (!$post) {
                return response()->json([
                    'success' => false,
                    'message' => 'Post not found'
                ], 404);
            }

            // Update engagement metrics
            $engagementMetrics = $post->engagement_metrics ?? [];
            $engagementMetrics['shares'] = ($engagementMetrics['shares'] ?? 0) + 1;
            $engagementMetrics['share_platforms'] = $engagementMetrics['share_platforms'] ?? [];
            $engagementMetrics['share_platforms'][$platform] = ($engagementMetrics['share_platforms'][$platform] ?? 0) + 1;

            $post->update(['engagement_metrics' => $engagementMetrics]);

            // Log the share activity
            Log::info('Post shared', [
                'post_id' => $contentId,
                'user_id' => $user->id,
                'platform' => $platform,
                'post_source' => $post->source
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Share tracked successfully',
                'shares' => $engagementMetrics['shares']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to track share', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track share'
            ], 500);
        }
    }
}