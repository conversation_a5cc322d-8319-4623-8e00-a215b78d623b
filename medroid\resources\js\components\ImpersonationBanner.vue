<script setup>
import { ref, onMounted } from 'vue';

const impersonationData = ref(null);
const loading = ref(false);

const checkImpersonationStatus = async () => {
    try {
        const response = await window.axios.get('/impersonation-status');
        impersonationData.value = response.data;
    } catch (error) {
        // Silently handle errors - impersonation status is not critical
        // Only log if it's not a 404 or authentication error
        if (error.response?.status !== 404 && error.response?.status !== 401 && error.response?.status !== 403) {
            console.error('Error checking impersonation status:', error);
        }
        impersonationData.value = null;
    }
};

const stopImpersonation = async () => {
    if (confirm('Are you sure you want to stop impersonating and return to your account?')) {
        loading.value = true;
        try {
            const response = await window.axios.post('/stop-impersonation');
            if (response.data.redirect_url) {
                window.location.href = response.data.redirect_url;
            }
        } catch (error) {
            console.error('Error stopping impersonation:', error);
            alert('Failed to stop impersonation. Please try again.');
        } finally {
            loading.value = false;
        }
    }
};

onMounted(() => {
    checkImpersonationStatus();
});
</script>

<template>
    <div 
        v-if="impersonationData?.is_impersonating" 
        class="bg-yellow-500 text-white px-4 py-2 text-sm font-medium"
    >
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <div class="flex items-center space-x-2">
                <i class="fas fa-user-secret"></i>
                <span>
                    You are impersonating 
                    <strong>{{ impersonationData.impersonated.name }}</strong> 
                    ({{ impersonationData.impersonated.email }})
                </span>
            </div>
            <button 
                @click="stopImpersonation"
                :disabled="loading"
                class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs font-medium disabled:opacity-50"
            >
                <i v-if="loading" class="fas fa-spinner fa-spin mr-1"></i>
                Switch Back
            </button>
        </div>
    </div>
</template>
