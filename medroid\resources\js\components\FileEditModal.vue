<template>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
            <div class="mt-3">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Edit File</h3>
                    <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- File Preview -->
                <div class="mb-4 text-center">
                    <img v-if="file.is_image" :src="file.url" :alt="file.name" class="max-w-full h-32 object-cover rounded mx-auto">
                    <div v-else class="w-full h-32 bg-gray-100 rounded flex items-center justify-center">
                        <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="updateFile">
                    <!-- Name -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">File Name</label>
                        <input 
                            v-model="form.name" 
                            type="text" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>

                    <!-- Category -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select 
                            v-model="form.category" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option v-for="category in categories" :key="category.value" :value="category.value">
                                {{ category.label }}
                            </option>
                        </select>
                    </div>

                    <!-- Description -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea 
                            v-model="form.description" 
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Optional description..."
                        ></textarea>
                    </div>

                    <!-- Public Access -->
                    <div class="mb-6">
                        <label class="flex items-center">
                            <input 
                                v-model="form.is_public" 
                                type="checkbox"
                                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            >
                            <span class="ml-2 text-sm text-gray-700">Make this file publicly accessible</span>
                        </label>
                    </div>

                    <!-- File Info -->
                    <div class="mb-6 p-3 bg-gray-50 rounded">
                        <div class="text-sm text-gray-600 space-y-1">
                            <div><strong>Original Name:</strong> {{ file.original_name }}</div>
                            <div><strong>Size:</strong> {{ formatFileSize(file.size) }}</div>
                            <div><strong>Type:</strong> {{ file.mime_type }}</div>
                            <div><strong>Created:</strong> {{ formatDate(file.created_at) }}</div>
                            <div v-if="file.download_count > 0"><strong>Downloads:</strong> {{ file.download_count }}</div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-end space-x-3">
                        <button 
                            type="button" 
                            @click="$emit('close')"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            Cancel
                        </button>
                        <button 
                            type="submit" 
                            :disabled="updating"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            {{ updating ? 'Updating...' : 'Update File' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import axios from 'axios'

export default {
    name: 'FileEditModal',
    props: {
        file: {
            type: Object,
            required: true
        },
        categories: {
            type: Array,
            required: true
        }
    },
    emits: ['close', 'updated'],
    setup(props, { emit }) {
        const updating = ref(false)
        
        const form = reactive({
            name: '',
            category: '',
            description: '',
            is_public: false
        })

        const updateFile = async () => {
            updating.value = true
            try {
                const response = await axios.put(`/web-api/files/${props.file.id}`, form)
                emit('updated', response.data.file)
            } catch (error) {
                console.error('Error updating file:', error)
                if (error.response?.data?.message) {
                    alert(error.response.data.message)
                }
            } finally {
                updating.value = false
            }
        }

        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 Bytes'
            const k = 1024
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        const formatDate = (dateString) => {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            })
        }

        onMounted(() => {
            // Initialize form with file data
            form.name = props.file.name
            form.category = props.file.category
            form.description = props.file.description || ''
            form.is_public = props.file.is_public
        })

        return {
            form,
            updating,
            updateFile,
            formatFileSize,
            formatDate
        }
    }
}
</script>
