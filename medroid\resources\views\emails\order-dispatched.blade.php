<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Dispatched - {{ $order->order_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4F46E5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .tracking-info {
            background-color: #EBF8FF;
            border: 1px solid #BEE3F8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tracking-number {
            font-size: 18px;
            font-weight: bold;
            color: #2B6CB0;
            font-family: monospace;
            background-color: white;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            margin: 10px 0;
        }
        .order-items {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .item {
            border-bottom: 1px solid #e5e5e5;
            padding: 10px 0;
        }
        .item:last-child {
            border-bottom: none;
        }
        .button {
            display: inline-block;
            background-color: #4F46E5;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e5e5;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📦 Your Order Has Been Dispatched!</h1>
        <p>Order #{{ $order->order_number }}</p>
    </div>

    <div class="content">
        <p>Hi {{ $customerName }},</p>

        <p>Great news! Your order has been dispatched and is on its way to you.</p>

        <div class="tracking-info">
            <h3>📍 Tracking Information</h3>
            @if($shippingCompany)
                <p><strong>Shipping Carrier:</strong> {{ $shippingCompany }}</p>
            @endif
            @if($trackingNumber)
                <p><strong>Tracking Number:</strong></p>
                <div class="tracking-number">{{ $trackingNumber }}</div>
                <p style="font-size: 14px; color: #666;">
                    You can use this tracking number to monitor your package's progress on the carrier's website.
                </p>
            @endif
            @if($order->shipped_at)
                <p><strong>Dispatch Date:</strong> {{ $order->shipped_at->format('F j, Y') }}</p>
            @endif
        </div>

        <div class="order-items">
            <h3>📋 Order Summary</h3>
            @foreach($order->items as $item)
                <div class="item">
                    <strong>{{ $item->product_name }}</strong><br>
                    <span style="color: #666;">SKU: {{ $item->product_sku }}</span><br>
                    <span style="color: #666;">Quantity: {{ $item->quantity }} × {{ $item->formatted_unit_price }}</span>
                </div>
            @endforeach
            
            <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #e5e5e5;">
                <p style="text-align: right; font-size: 18px; font-weight: bold;">
                    Total: {{ $order->formatted_total }}
                </p>
            </div>
        </div>

        @if($order->shipping_address)
            <div style="background-color: white; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3>🏠 Shipping Address</h3>
                <p>
                    {{ $order->shipping_address['name'] ?? ($order->shipping_address['first_name'] . ' ' . $order->shipping_address['last_name']) }}<br>
                    {{ $order->shipping_address['address_line_1'] }}<br>
                    @if(!empty($order->shipping_address['address_line_2']))
                        {{ $order->shipping_address['address_line_2'] }}<br>
                    @endif
                    {{ $order->shipping_address['city'] }}, {{ $order->shipping_address['state'] }} {{ $order->shipping_address['postal_code'] }}<br>
                    {{ $order->shipping_address['country'] }}
                </p>
            </div>
        @endif

        <div style="text-align: center;">
            <a href="{{ $orderUrl }}" class="button">Track Your Order</a>
        </div>

        <p>If you have any questions about your order, please don't hesitate to contact our customer support team.</p>

        <p>Thank you for your business!</p>

        <p>Best regards,<br>
        The Medroid Team</p>
    </div>

    <div class="footer">
        <p>This email was sent regarding your order #{{ $order->order_number }}</p>
        <p>© {{ date('Y') }} Medroid. All rights reserved.</p>
    </div>
</body>
</html>
