import{r as b,w as ce,o as de,d as n,e as o,i as e,j as B,t as l,n as N,l as v,v as _,q as re,F as k,p as A,a as V,c as me,f as oe,u as ae,m as xe,g as j,y as ne,x as u,A as ie,P as ye}from"./vendor-BhKTHoN5.js";import{_ as pe}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const ve={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},fe={class:"flex min-h-full items-center justify-center p-4"},be={class:"bg-white px-6 py-4 border-b border-gray-200"},he={class:"flex items-center justify-between"},_e={class:"text-lg font-semibold text-gray-900"},ke={class:"space-y-6"},we={class:"bg-gray-50 p-4 rounded-lg"},Ce={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ne={key:0},Ae=["required"],Ee={class:"bg-gray-50 p-4 rounded-lg"},Pe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},$e=["value"],Me={class:"bg-gray-50 p-4 rounded-lg"},De={class:"space-y-4"},Ie={class:"flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200"},Se=["disabled"],Ue={__name:"PatientModal",props:{patient:Object,isEdit:Boolean},emits:["close","saved"],setup(E,{emit:P}){const w=E,f=P,p=b(!1),a=b([]),g=b({name:"",email:"",phone_number:"",password:"",date_of_birth:"",gender:"",emergency_contact:"",clinic_id:"",medical_history:"",current_medications:""}),$=async()=>{p.value=!0;try{w.isEdit?await V.put(`/update-patient/${w.patient.id}`,g.value):await V.post("/save-patient",g.value),f("saved")}catch(c){console.error("Error saving patient:",c),alert("Error saving patient. Please try again.")}finally{p.value=!1}},M=async()=>{try{const c=await V.get("/clinics-list");a.value=c.data.data||c.data.clinics||[]}catch(c){console.error("Error fetching clinics:",c)}};return ce(()=>w.patient,c=>{var s,i,x;c&&w.isEdit&&Object.assign(g.value,{name:((s=c.user)==null?void 0:s.name)||"",email:((i=c.user)==null?void 0:i.email)||"",phone_number:((x=c.user)==null?void 0:x.phone_number)||"",date_of_birth:c.date_of_birth||"",gender:c.gender||"",emergency_contact:c.emergency_contact||"",clinic_id:c.clinic_id||"",medical_history:c.medical_history||"",current_medications:c.current_medications||""})},{immediate:!0}),de(()=>{M()}),(c,s)=>(o(),n("div",ve,[e("div",{class:"fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:s[0]||(s[0]=i=>c.$emit("close"))}),e("div",fe,[e("div",{class:"relative w-full max-w-2xl transform overflow-hidden rounded-lg bg-white shadow-xl transition-all",onClick:s[13]||(s[13]=B(()=>{},["stop"]))},[e("div",be,[e("div",he,[e("h3",_e,l(E.isEdit?"Edit Patient":"Create New Patient"),1),e("button",{onClick:s[1]||(s[1]=i=>c.$emit("close")),class:"text-gray-400 hover:text-gray-600 transition-colors"},s[14]||(s[14]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),e("form",{onSubmit:B($,["prevent"]),class:"px-6 py-4"},[e("div",ke,[e("div",we,[s[19]||(s[19]=e("h4",{class:"text-md font-semibold text-gray-900 mb-4"},"User Information",-1)),e("div",Ce,[e("div",null,[s[15]||(s[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Full Name *",-1)),v(e("input",{"onUpdate:modelValue":s[2]||(s[2]=i=>g.value.name=i),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter full name"},null,512),[[_,g.value.name]])]),e("div",null,[s[16]||(s[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email *",-1)),v(e("input",{"onUpdate:modelValue":s[3]||(s[3]=i=>g.value.email=i),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter email address"},null,512),[[_,g.value.email]])]),e("div",null,[s[17]||(s[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Phone Number",-1)),v(e("input",{"onUpdate:modelValue":s[4]||(s[4]=i=>g.value.phone_number=i),type:"tel",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter phone number"},null,512),[[_,g.value.phone_number]])]),E.isEdit?N("",!0):(o(),n("div",Ne,[s[18]||(s[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Password *",-1)),v(e("input",{"onUpdate:modelValue":s[5]||(s[5]=i=>g.value.password=i),type:"password",required:!E.isEdit,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter password"},null,8,Ae),[[_,g.value.password]])]))])]),e("div",Ee,[s[26]||(s[26]=e("h4",{class:"text-md font-semibold text-gray-900 mb-4"},"Patient Information",-1)),e("div",Pe,[e("div",null,[s[20]||(s[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Date of Birth",-1)),v(e("input",{"onUpdate:modelValue":s[6]||(s[6]=i=>g.value.date_of_birth=i),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[_,g.value.date_of_birth]])]),e("div",null,[s[22]||(s[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Gender",-1)),v(e("select",{"onUpdate:modelValue":s[7]||(s[7]=i=>g.value.gender=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},s[21]||(s[21]=[e("option",{value:""},"Select Gender",-1),e("option",{value:"male"},"Male",-1),e("option",{value:"female"},"Female",-1),e("option",{value:"other"},"Other",-1)]),512),[[re,g.value.gender]])]),e("div",null,[s[23]||(s[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Emergency Contact",-1)),v(e("input",{"onUpdate:modelValue":s[8]||(s[8]=i=>g.value.emergency_contact=i),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Emergency contact name and phone"},null,512),[[_,g.value.emergency_contact]])]),e("div",null,[s[25]||(s[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Clinic",-1)),v(e("select",{"onUpdate:modelValue":s[9]||(s[9]=i=>g.value.clinic_id=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[s[24]||(s[24]=e("option",{value:""},"Select Clinic",-1)),(o(!0),n(k,null,A(a.value,i=>(o(),n("option",{key:i.id,value:i.id},l(i.name),9,$e))),128))],512),[[re,g.value.clinic_id]])])])]),e("div",Me,[s[29]||(s[29]=e("h4",{class:"text-md font-semibold text-gray-900 mb-4"},"Medical Information",-1)),e("div",De,[e("div",null,[s[27]||(s[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Medical History",-1)),v(e("textarea",{"onUpdate:modelValue":s[10]||(s[10]=i=>g.value.medical_history=i),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter medical history, conditions, allergies, etc."},null,512),[[_,g.value.medical_history]])]),e("div",null,[s[28]||(s[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Current Medications",-1)),v(e("textarea",{"onUpdate:modelValue":s[11]||(s[11]=i=>g.value.current_medications=i),rows:"2",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"List current medications"},null,512),[[_,g.value.current_medications]])])])])]),e("div",Ie,[e("button",{type:"button",onClick:s[12]||(s[12]=i=>c.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"}," Cancel "),e("button",{type:"submit",disabled:p.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"},l(p.value?"Saving...":E.isEdit?"Update Patient":"Create Patient"),9,Se)])],32)])])]))}},Ve={class:"flex items-center justify-between"},je={class:"flex mt-2","aria-label":"Breadcrumb"},Be={class:"inline-flex items-center space-x-1 md:space-x-3"},Le={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},Fe={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},qe={class:"py-12"},ze={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},Oe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6"},Ge={class:"p-6"},Je={class:"flex flex-col sm:flex-row gap-4"},Te={class:"flex-1"},We={class:"relative"},Ye={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},He={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},Qe={class:"p-6"},Re={class:"flex items-center"},Ke={class:"ml-4"},Xe={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},Ze={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},et={class:"p-6"},tt={class:"flex items-center"},st={class:"ml-4"},lt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},rt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ot={class:"p-6"},at={class:"flex items-center"},nt={class:"ml-4"},it={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},dt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ut={class:"p-6"},gt={class:"flex items-center"},ct={class:"ml-4"},mt={class:"text-2xl font-semibold text-gray-900 dark:text-gray-100"},xt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},yt={class:"p-6 text-gray-900 dark:text-gray-100"},pt={key:0,class:"text-center py-8"},vt={key:1,class:"text-center py-8"},ft={key:2,class:"overflow-x-auto"},bt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ht={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},_t={class:"px-6 py-4 whitespace-nowrap"},kt={class:"flex items-center"},wt={class:"ml-4"},Ct={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Nt={class:"text-sm text-gray-500 dark:text-gray-400"},At={class:"text-xs text-gray-400"},Et={class:"px-6 py-4 whitespace-nowrap"},Pt={class:"text-sm text-gray-900 dark:text-gray-100"},$t={class:"text-sm text-gray-500 dark:text-gray-400"},Mt={class:"px-6 py-4 whitespace-nowrap"},Dt={class:"text-sm text-gray-900 dark:text-gray-100"},It={class:"text-sm text-gray-500 dark:text-gray-400"},St={class:"text-xs text-gray-400"},Ut={class:"px-6 py-4 whitespace-nowrap"},Vt={class:"text-sm text-gray-900 dark:text-gray-100"},jt={class:"text-sm text-gray-500 dark:text-gray-400 capitalize"},Bt={class:"text-xs text-gray-400"},Lt={class:"px-6 py-4 whitespace-nowrap"},Ft={class:"text-sm text-gray-900 dark:text-gray-100"},qt={class:"text-xs text-gray-500 dark:text-gray-400"},zt={class:"px-6 py-4"},Ot={class:"text-xs space-y-1"},Gt={key:0,class:"flex items-center"},Jt={class:"text-red-600"},Tt={key:1,class:"flex items-center"},Wt={class:"text-blue-600"},Yt={key:2,class:"flex items-center"},Ht={class:"text-orange-600"},Qt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Rt=["onClick"],Kt={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Xt={class:"flex min-h-full items-center justify-center p-4"},Zt={class:"p-6"},es={class:"flex justify-between items-center mb-4"},ts={class:"text-lg font-medium text-gray-900 dark:text-gray-100"},ss={key:0,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ls={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},rs={class:"space-y-2 text-sm"},os={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},as={class:"space-y-2 text-sm"},ns={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},is={class:"space-y-2 text-sm"},ds={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},us={class:"space-y-2 text-sm"},gs={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"},cs={class:"space-y-3 text-sm"},ms={key:0,class:"mt-1"},xs={key:1,class:"text-gray-500"},ys={key:0,class:"mt-1"},ps={key:1,class:"text-gray-500"},vs={key:0,class:"mt-1"},fs={key:1,class:"text-gray-500"},bs={key:0,class:"mt-1"},hs={key:1,class:"text-gray-500"},_s={class:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg md:col-span-2"},ks={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},ws={key:0,class:"mt-1"},Cs={key:1,class:"text-gray-500"},Ns={class:"ml-2"},Is={__name:"Patients",setup(E){const P=[{title:"Dashboard",href:"/dashboard"},{title:"Patients",href:"/patients"}],w=b(!1),f=b([]),p=b(""),a=b(null),g=b(!1),$=b(!1),M=b(!1),c=async()=>{w.value=!0;try{const d=await window.axios.get("/patients-list");f.value=d.data.data||d.data||[]}catch(d){console.error("Error fetching patients:",d),f.value=[]}finally{w.value=!1}},s=d=>{if(!d)return"N/A";const t=new Date,m=new Date(d);let y=t.getFullYear()-m.getFullYear();const C=t.getMonth()-m.getMonth();return(C<0||C===0&&t.getDate()<m.getDate())&&y--,y},i=d=>d?new Date(d).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"N/A",x=d=>{if(!d)return[];if(typeof d=="string")try{return JSON.parse(d)}catch{return[]}return Array.isArray(d)?d:[]},h=d=>{if(!d)return{};if(typeof d=="string")try{return JSON.parse(d)}catch{return{}}return typeof d=="object"?d:{}},ue=d=>{a.value=d,g.value=!0},U=()=>{g.value=!1,a.value=null},L=()=>{a.value=null,$.value=!0},F=()=>{$.value=!1,M.value=!1,a.value=null},ge=()=>{F(),c()},q=me(()=>p.value?f.value.filter(d=>{var t,m,y,C,D,I;return((m=(t=d.user)==null?void 0:t.name)==null?void 0:m.toLowerCase().includes(p.value.toLowerCase()))||((C=(y=d.user)==null?void 0:y.email)==null?void 0:C.toLowerCase().includes(p.value.toLowerCase()))||((I=(D=d.user)==null?void 0:D.phone_number)==null?void 0:I.includes(p.value))}):f.value);return de(()=>{c()}),(d,t)=>(o(),n(k,null,[oe(ae(xe),{title:"Patient Management"}),oe(pe,null,{header:j(()=>[e("div",Ve,[e("div",null,[t[3]||(t[3]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Patient Management ",-1)),e("nav",je,[e("ol",Be,[(o(),n(k,null,A(P,(m,y)=>e("li",{key:y,class:"inline-flex items-center"},[y<P.length-1?(o(),ne(ae(ye),{key:0,href:m.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:j(()=>[u(l(m.title),1)]),_:2},1032,["href"])):(o(),n("span",Le,l(m.title),1)),y<P.length-1?(o(),n("svg",Fe,t[2]||(t[2]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):N("",!0)])),64))])])]),e("button",{onClick:L,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"}," Add Patient ")])]),default:j(()=>{var m,y,C,D,I,z,O,G,J,T,W,Y,H,Q;return[e("div",qe,[e("div",ze,[e("div",Oe,[e("div",Ge,[e("div",Je,[e("div",Te,[e("div",We,[t[4]||(t[4]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("i",{class:"fas fa-search text-gray-400"})],-1)),v(e("input",{"onUpdate:modelValue":t[0]||(t[0]=r=>p.value=r),type:"text",placeholder:"Search patients by name, email, or phone...",class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[_,p.value]])])]),e("button",{onClick:L,class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},t[5]||(t[5]=[e("i",{class:"fas fa-plus mr-2"},null,-1),u("Add Patient ")]))])])]),e("div",Ye,[e("div",He,[e("div",Qe,[e("div",Re,[t[7]||(t[7]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-users text-2xl text-green-500"})],-1)),e("div",Ke,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Total Patients",-1)),e("p",Xe,l(f.value.length),1)])])])]),e("div",Ze,[e("div",et,[e("div",tt,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-user-check text-2xl text-blue-500"})],-1)),e("div",st,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Active Patients",-1)),e("p",lt,l(f.value.filter(r=>{var S;return(S=r.user)==null?void 0:S.is_active}).length),1)])])])]),e("div",rt,[e("div",ot,[e("div",at,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-shield-alt text-2xl text-purple-500"})],-1)),e("div",nt,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"With Insurance",-1)),e("p",it,l(f.value.filter(r=>r.insurance_provider).length),1)])])])]),e("div",dt,[e("div",ut,[e("div",gt,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("i",{class:"fas fa-exclamation-triangle text-2xl text-orange-500"})],-1)),e("div",ct,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"With Allergies",-1)),e("p",mt,l(f.value.filter(r=>x(r.allergies).length>0).length),1)])])])])]),e("div",xt,[e("div",yt,[w.value?(o(),n("div",pt,t[14]||(t[14]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):q.value.length===0?(o(),n("div",vt,t[15]||(t[15]=[e("i",{class:"fas fa-users text-4xl text-gray-400 mb-4"},null,-1),e("p",{class:"text-gray-500"},"No patients found",-1)]))):(o(),n("div",ft,[e("table",bt,[t[23]||(t[23]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Patient "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Clinic "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contact "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Age/Gender "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Insurance "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Medical Info "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),e("tbody",ht,[(o(!0),n(k,null,A(q.value,r=>{var S,R,K,X,Z,ee,te,se,le;return o(),n("tr",{key:r.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",_t,[e("div",kt,[t[16]||(t[16]=e("div",{class:"flex-shrink-0 h-10 w-10"},[e("div",{class:"h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center"},[e("i",{class:"fas fa-user text-green-600 dark:text-green-400"})])],-1)),e("div",wt,[e("div",Ct,l(((S=r.user)==null?void 0:S.name)||"No Name"),1),e("div",Nt," ID: "+l(r.id||"No ID"),1),e("div",At," User ID: "+l(r.user_id||"No User ID"),1)])])]),e("td",Et,[e("div",Pt,l(((R=r.clinic)==null?void 0:R.name)||"No Clinic"),1),e("div",$t,l(((K=r.clinic)==null?void 0:K.city)||"N/A"),1)]),e("td",Mt,[e("div",Dt,l(((X=r.user)==null?void 0:X.email)||"No Email"),1),e("div",It,l(((Z=r.user)==null?void 0:Z.phone_number)||"No Phone"),1),e("div",St,[e("span",{class:ie((ee=r.user)!=null&&ee.is_active?"text-green-600":"text-red-600")},l((te=r.user)!=null&&te.is_active?"Active":"Inactive"),3)])]),e("td",Ut,[e("div",Vt,l(s(r.date_of_birth))+" years",1),e("div",jt,l(r.gender||"N/A"),1),e("div",Bt,l(i(r.date_of_birth)),1)]),e("td",Lt,[e("div",Ft,l(r.insurance_provider||"None"),1),e("div",qt,l(r.insurance_policy_number||"N/A"),1)]),e("td",zt,[e("div",Ot,[x(r.allergies).length>0?(o(),n("div",Gt,[t[17]||(t[17]=e("i",{class:"fas fa-exclamation-triangle text-red-500 mr-1"},null,-1)),e("span",Jt,l(x(r.allergies).length)+" allergies",1)])):N("",!0),x(r.medications).length>0?(o(),n("div",Tt,[t[18]||(t[18]=e("i",{class:"fas fa-pills text-blue-500 mr-1"},null,-1)),e("span",Wt,l(x(r.medications).length)+" medications",1)])):N("",!0),((se=h(r.health_history).chronic_conditions)==null?void 0:se.length)>0?(o(),n("div",Yt,[t[19]||(t[19]=e("i",{class:"fas fa-heartbeat text-orange-500 mr-1"},null,-1)),e("span",Ht,l((le=h(r.health_history).chronic_conditions)==null?void 0:le.length)+" conditions",1)])):N("",!0)])]),e("td",Qt,[e("button",{onClick:As=>ue(r),class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},t[20]||(t[20]=[e("i",{class:"fas fa-eye mr-1"},null,-1),u("View ")]),8,Rt),t[21]||(t[21]=e("button",{class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},[e("i",{class:"fas fa-edit mr-1"}),u("Edit ")],-1)),t[22]||(t[22]=e("button",{class:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"},[e("i",{class:"fas fa-calendar mr-1"}),u("Schedule ")],-1))])])}),128))])])]))])])])]),g.value?(o(),n("div",Kt,[e("div",{class:"fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:U}),e("div",Xt,[e("div",{class:"relative w-full max-w-4xl transform overflow-hidden rounded-lg bg-white shadow-xl transition-all",onClick:t[1]||(t[1]=B(()=>{},["stop"]))},[e("div",Zt,[e("div",es,[e("h3",ts," Patient Details - "+l((y=(m=a.value)==null?void 0:m.user)==null?void 0:y.name),1),e("button",{onClick:U,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[24]||(t[24]=[e("i",{class:"fas fa-times text-xl"},null,-1)]))]),a.value?(o(),n("div",ss,[e("div",ls,[t[32]||(t[32]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-user mr-2"}),u("Personal Information ")],-1)),e("div",rs,[e("div",null,[t[25]||(t[25]=e("strong",null,"Name:",-1)),u(" "+l((C=a.value.user)==null?void 0:C.name),1)]),e("div",null,[t[26]||(t[26]=e("strong",null,"Email:",-1)),u(" "+l((D=a.value.user)==null?void 0:D.email),1)]),e("div",null,[t[27]||(t[27]=e("strong",null,"Phone:",-1)),u(" "+l(((I=a.value.user)==null?void 0:I.phone_number)||"N/A"),1)]),e("div",null,[t[28]||(t[28]=e("strong",null,"Date of Birth:",-1)),u(" "+l(i(a.value.date_of_birth)),1)]),e("div",null,[t[29]||(t[29]=e("strong",null,"Age:",-1)),u(" "+l(s(a.value.date_of_birth))+" years",1)]),e("div",null,[t[30]||(t[30]=e("strong",null,"Gender:",-1)),u(" "+l(a.value.gender||"N/A"),1)]),e("div",null,[t[31]||(t[31]=e("strong",null,"Status:",-1)),e("span",{class:ie((z=a.value.user)!=null&&z.is_active?"text-green-600":"text-red-600")},l((O=a.value.user)!=null&&O.is_active?"Active":"Inactive"),3)])])]),e("div",os,[t[37]||(t[37]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-clinic-medical mr-2"}),u("Clinic Information ")],-1)),e("div",as,[e("div",null,[t[33]||(t[33]=e("strong",null,"Clinic Name:",-1)),u(" "+l(((G=a.value.clinic)==null?void 0:G.name)||"No Clinic Assigned"),1)]),e("div",null,[t[34]||(t[34]=e("strong",null,"Clinic Address:",-1)),u(" "+l(((J=a.value.clinic)==null?void 0:J.full_address)||"N/A"),1)]),e("div",null,[t[35]||(t[35]=e("strong",null,"Clinic Phone:",-1)),u(" "+l(((T=a.value.clinic)==null?void 0:T.phone)||"N/A"),1)]),e("div",null,[t[36]||(t[36]=e("strong",null,"Clinic Email:",-1)),u(" "+l(((W=a.value.clinic)==null?void 0:W.email)||"N/A"),1)])])]),e("div",ns,[t[41]||(t[41]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-shield-alt mr-2"}),u("Insurance Information ")],-1)),e("div",is,[e("div",null,[t[38]||(t[38]=e("strong",null,"Provider:",-1)),u(" "+l(a.value.insurance_provider||"None"),1)]),e("div",null,[t[39]||(t[39]=e("strong",null,"Policy Number:",-1)),u(" "+l(a.value.insurance_policy_number||"N/A"),1)]),e("div",null,[t[40]||(t[40]=e("strong",null,"Expiry Date:",-1)),u(" "+l(i(a.value.insurance_expiry_date)),1)])])]),e("div",ds,[t[45]||(t[45]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-phone mr-2"}),u("Emergency Contact ")],-1)),e("div",us,[e("div",null,[t[42]||(t[42]=e("strong",null,"Name:",-1)),u(" "+l(a.value.emergency_contact_name||"N/A"),1)]),e("div",null,[t[43]||(t[43]=e("strong",null,"Phone:",-1)),u(" "+l(a.value.emergency_contact_phone||"N/A"),1)]),e("div",null,[t[44]||(t[44]=e("strong",null,"Relationship:",-1)),u(" "+l(a.value.emergency_contact_relationship||"N/A"),1)])])]),e("div",gs,[t[50]||(t[50]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-heartbeat mr-2"}),u("Medical Information ")],-1)),e("div",cs,[e("div",null,[t[46]||(t[46]=e("strong",{class:"text-red-600"},"Allergies:",-1)),x(a.value.allergies).length>0?(o(),n("div",ms,[(o(!0),n(k,null,A(x(a.value.allergies),r=>(o(),n("span",{key:r,class:"inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded mr-1 mb-1"},l(r),1))),128))])):(o(),n("span",xs,"None known"))]),e("div",null,[t[47]||(t[47]=e("strong",{class:"text-blue-600"},"Current Medications:",-1)),x(a.value.medications).length>0?(o(),n("div",ys,[(o(!0),n(k,null,A(x(a.value.medications),r=>(o(),n("div",{key:r,class:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mb-1 inline-block mr-1"},l(r),1))),128))])):(o(),n("span",ps,"None"))]),e("div",null,[t[48]||(t[48]=e("strong",{class:"text-orange-600"},"Chronic Conditions:",-1)),((Y=h(a.value.health_history).chronic_conditions)==null?void 0:Y.length)>0?(o(),n("div",vs,[(o(!0),n(k,null,A(h(a.value.health_history).chronic_conditions,r=>(o(),n("span",{key:r,class:"inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded mr-1 mb-1"},l(r),1))),128))])):(o(),n("span",fs,"None"))]),e("div",null,[t[49]||(t[49]=e("strong",{class:"text-purple-600"},"Previous Surgeries:",-1)),((H=h(a.value.health_history).surgeries)==null?void 0:H.length)>0?(o(),n("div",bs,[(o(!0),n(k,null,A(h(a.value.health_history).surgeries,r=>(o(),n("div",{key:r,class:"bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded mb-1 inline-block mr-1"},l(r),1))),128))])):(o(),n("span",hs,"None"))])])]),e("div",_s,[t[53]||(t[53]=e("h4",{class:"text-md font-semibold text-gray-900 dark:text-gray-100 mb-3"},[e("i",{class:"fas fa-calendar mr-2"}),u("Appointment Preferences ")],-1)),e("div",ks,[e("div",null,[t[51]||(t[51]=e("strong",null,"Preferred Days:",-1)),((Q=h(a.value.appointment_preferences).preferred_days)==null?void 0:Q.length)>0?(o(),n("div",ws,[(o(!0),n(k,null,A(h(a.value.appointment_preferences).preferred_days,r=>(o(),n("span",{key:r,class:"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-1 mb-1"},l(r),1))),128))])):(o(),n("span",Cs,"No preference"))]),e("div",null,[t[52]||(t[52]=e("strong",null,"Preferred Time:",-1)),e("span",Ns,l(h(a.value.appointment_preferences).preferred_time||"No preference"),1)])])])])):N("",!0),e("div",{class:"flex justify-end mt-6 space-x-3"},[e("button",{onClick:U,class:"bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Close "),t[54]||(t[54]=e("button",{class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},[e("i",{class:"fas fa-edit mr-2"}),u("Edit Patient ")],-1)),t[55]||(t[55]=e("button",{class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"},[e("i",{class:"fas fa-calendar-plus mr-2"}),u("Schedule Appointment ")],-1))])])])])])):N("",!0),$.value||M.value?(o(),ne(Ue,{key:1,patient:a.value,isEdit:M.value,onClose:F,onSaved:ge},null,8,["patient","isEdit"])):N("",!0)]}),_:1})],64))}};export{Is as default};
