import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:medroid_app/models/social_post.dart';
import 'package:medroid_app/models/story.dart';
import 'package:medroid_app/models/comment.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/comment_service.dart';
import 'package:medroid_app/services/story_service.dart';
import 'package:medroid_app/screens/create_post_screen_new.dart';
import 'package:medroid_app/screens/create_story_screen.dart';
import 'package:medroid_app/screens/story_viewer_screen.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/widgets/story_item.dart';
import 'package:medroid_app/widgets/platform_aware_network_image.dart';
import 'package:medroid_app/screens/profile_screen_new.dart';

class InstagramFeedScreen extends StatefulWidget {
  final SocialPost? initialPost;
  final List<SocialPost>? posts;
  final Function(SocialPost)? onPostUpdated;

  const InstagramFeedScreen({
    Key? key,
    this.initialPost,
    this.posts,
    this.onPostUpdated,
  }) : super(key: key);

  @override
  State<InstagramFeedScreen> createState() => _InstagramFeedScreenState();
}

class _InstagramFeedScreenState extends State<InstagramFeedScreen>
    with TickerProviderStateMixin {
  // Core state
  final List<SocialPost> _posts = [];
  final PageController _pageController = PageController();
  final ScrollController _headerScrollController = ScrollController();
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _hasMorePosts = true;
  int _currentPage = 1;
  int _currentPostIndex = 0;

  // Header and stories removed

  // Services
  late CommentService _commentService;
  late StoryService _storyService;
  late ApiService _apiService;
  late AuthService _authService;

  // Removed stories functionality

  // Video playback state
  final Map<String, VideoPlayerController> _videoControllers = {};
  final Map<String, bool> _videoStates = {};
  String? _currentPlayingVideo;
  bool _globalMuteState = true;

  // UI state
  final Map<String, bool> _showComments = {};
  final Map<String, List<Comment>> _commentsData = {};


  @override
  void initState() {
    super.initState();

    // Header animation removed

    // Initialize services
    _authService = RepositoryProvider.of<AuthService>(context);
    _apiService = RepositoryProvider.of<ApiService>(context);
    _commentService = CommentService(_authService);
    _storyService = StoryService(_authService);

    // Load initial data
    _initializeData();

    // Listen to page changes
    _pageController.addListener(_onPageChanged);

    // Header removed
  }

  @override
  void dispose() {
    _pageController.dispose();
    _headerScrollController.dispose();

    // Dispose video controllers
    for (var controller in _videoControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  // Initialize all data
  Future<void> _initializeData() async {
    // If posts are provided, use them instead of loading from API
    if (widget.posts != null && widget.posts!.isNotEmpty) {
      setState(() {
        _posts.clear();
        _posts.addAll(widget.posts!);
        _isLoading = false;

        // Set initial post index if provided
        if (widget.initialPost != null) {
          final initialIndex =
              _posts.indexWhere((p) => p.id == widget.initialPost!.id);
          if (initialIndex != -1) {
            _currentPostIndex = initialIndex;
            // Navigate to the initial post
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_pageController.hasClients) {
                _pageController.animateToPage(
                  initialIndex,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              }
            });
          }
        }
      });

      // Initialize video controllers for the provided posts
      _initializeVideoControllers();

      // Stories removed
    } else {
      // Load data from API as usual
      await _loadFeed();
    }
  }

  // Handle page changes for infinite scroll and video management
  void _onPageChanged() {
    if (!_pageController.hasClients) return;

    final currentPage = _pageController.page?.round() ?? 0;
    if (currentPage != _currentPostIndex) {
      setState(() {
        _currentPostIndex = currentPage;
      });

      // Pause previous video and play current one
      _manageVideoPlayback(currentPage);

      // Load more posts when near the end
      if (currentPage >= _posts.length - 3 && !_isLoading && _hasMorePosts) {
        _loadMorePosts();
      }
    }
  }

  // Manage video playback based on current post with smooth transitions
  void _manageVideoPlayback(int currentIndex) {
    // Pause all videos first with smooth transition
    for (var entry in _videoControllers.entries) {
      final postId = entry.key;
      final controller = entry.value;

      if (controller.value.isPlaying) {
        controller.pause();
        setState(() {
          _videoStates[postId] = false;
        });
      }
    }

    // Reset current playing video
    setState(() {
      _currentPlayingVideo = null;
    });

    // Play current video if it exists with smooth transition
    if (currentIndex < _posts.length) {
      final currentPost = _posts[currentIndex];
      if (_isVideoContent(currentPost)) {
        final controller = _videoControllers[currentPost.id];
        if (controller != null && controller.value.isInitialized) {
          // Small delay for smooth transition
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted && _currentPostIndex == currentIndex) {
              controller.play();
              controller.setLooping(true);
              controller.setVolume(_globalMuteState ? 0.0 : 1.0);

              setState(() {
                _currentPlayingVideo = currentPost.id;
                _videoStates[currentPost.id] = true;
              });
            }
          });
        } else if (controller == null) {
          // Initialize video controller if it doesn't exist
          _initializeVideoController(currentPost);
        }
      }
    }

    // Preload adjacent videos for smoother scrolling
    _preloadAdjacentVideos(currentIndex);
  }

  // Preload videos for adjacent posts to ensure smooth scrolling
  void _preloadAdjacentVideos(int currentIndex) {
    final indicesToPreload = [
      currentIndex - 1, // Previous post
      currentIndex + 1, // Next post
    ];

    for (var index in indicesToPreload) {
      if (index >= 0 && index < _posts.length) {
        final post = _posts[index];
        if (_isVideoContent(post) && !_videoControllers.containsKey(post.id)) {
          _initializeVideoController(post);
        }
      }
    }
  }

  // Header toggle removed

  // Load feed with pagination
  Future<void> _loadFeed() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      debugPrint('🔄 Loading feed - page: $_currentPage');

      final result = await _apiService.getFeed(
        page: _currentPage,
      );

      if (!mounted) return;

      debugPrint(
          '📦 Received ${result.posts.length} posts from API (page $_currentPage)');

      setState(() {
        if (_currentPage == 1) {
          _posts.clear();
        }
        _posts.addAll(result.posts);
        _hasMorePosts = result.hasMore;
        _isLoading = false;
      });

      // Initialize video controllers for new posts
      _initializeVideoControllers();

      debugPrint('✅ Feed loaded successfully - total posts: ${_posts.length}');
    } catch (e) {
      if (!mounted) return;
      debugPrint('❌ Error loading feed: $e');
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  // Load more posts
  Future<void> _loadMorePosts() async {
    if (_isLoading || !_hasMorePosts) return;

    setState(() {
      _isLoading = true;
      _currentPage++;
    });

    try {
      await _loadFeed();
    } catch (e) {
      setState(() {
        _currentPage--;
        _isLoading = false;
      });
    }
  }

  // Initialize video controllers for posts
  void _initializeVideoControllers() {
    for (var post in _posts) {
      if (_isVideoContent(post) && !_videoControllers.containsKey(post.id)) {
        _initializeVideoController(post);
      }
    }
  }

  // Initialize a single video controller with better error handling
  void _initializeVideoController(SocialPost post) async {
    try {
      // Validate video URL first
      final videoUrl = post.videoUrl != null && post.videoUrl!.isNotEmpty
          ? _apiService.getFormattedMediaUrl(post.videoUrl!)
          : _apiService.getFormattedMediaUrl(post.mediaUrl);

      if (videoUrl.isEmpty || videoUrl == 'null') {
        debugPrint('❌ Invalid video URL for post ${post.id}');
        return;
      }

      debugPrint('🎥 Initializing video for post ${post.id}: $videoUrl');

      final controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      _videoControllers[post.id] = controller;
      _videoStates[post.id] = false;

      // Add timeout to prevent hanging
      controller.initialize().timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          debugPrint('⏰ Video initialization timeout for post ${post.id}');
          throw Exception('Video loading timeout');
        },
      ).then((_) {
        if (mounted) {
          debugPrint('✅ Video initialized successfully for post ${post.id}');
          controller.setVolume(_globalMuteState ? 0.0 : 1.0);
          controller.setLooping(true);

          // Auto-play if it's the current post
          if (_posts.indexOf(post) == _currentPostIndex) {
            controller.play();
            setState(() {
              _currentPlayingVideo = post.id;
              _videoStates[post.id] = true;
            });
          }
        }
      }).catchError((error) {
        debugPrint('❌ Video initialization failed for post ${post.id}: $error');
        if (mounted) {
          _videoControllers.remove(post.id);
          setState(() {});
        }

        // Don't show error to user for video failures, just log them
        // The UI will show a fallback image instead
      });
    } catch (e) {
      debugPrint('❌ Video player creation failed for post ${post.id}: $e');
      _videoControllers.remove(post.id);
    }
  }

  // Stories functionality removed

  // Post interactions
  Future<void> _toggleLikePost(SocialPost post) async {
    try {
      final result = await _apiService.likeContent(post.id);
      if (mounted) {
        setState(() {
          final index = _posts.indexWhere((p) => p.id == post.id);
          if (index != -1) {
            _posts[index] = _posts[index].copyWith(
              isLiked: result.liked,
              engagementMetrics: {
                ..._posts[index].engagementMetrics,
                'likes': result.likeCount
              },
            );

            // Notify parent screen of the update
            widget.onPostUpdated?.call(_posts[index]);
          }
        });
      }
    } catch (e) {
      _showNotification('Error: ${e.toString()}');
    }
  }

  Future<void> _toggleSavePost(SocialPost post) async {
    try {
      final result = await _apiService.saveContent(post.id);
      if (mounted) {
        setState(() {
          final index = _posts.indexWhere((p) => p.id == post.id);
          if (index != -1) {
            _posts[index] = _posts[index].copyWith(
              isSaved: result.saved,
              engagementMetrics: {
                ..._posts[index].engagementMetrics,
                'saves': result.saveCount
              },
            );

            // Notify parent screen of the update
            widget.onPostUpdated?.call(_posts[index]);
          }
        });
      }
    } catch (e) {
      _showNotification('Error: ${e.toString()}');
    }
  }

  // Toggle comments
  Future<void> _toggleComments(String postId) async {
    setState(() {
      _showComments[postId] = !(_showComments[postId] ?? false);
    });

    if (_showComments[postId] == true && _commentsData[postId] == null) {
      await _loadComments(postId);
    }
  }

  // Load comments
  Future<void> _loadComments(String postId) async {
    try {
      final comments = await _commentService.getComments(postId);
      if (mounted) {
        setState(() {
          _commentsData[postId] = comments;
        });
      }
    } catch (e) {
      _showNotification('Failed to load comments: ${e.toString()}');
    }
  }

  // Add comment
  Future<void> _addComment(String postId, String content) async {
    try {
      final comment = await _commentService.postComment(postId, content);
      if (mounted) {
        setState(() {
          _commentsData[postId] ??= [];
          _commentsData[postId]!.insert(0, comment);

          // Update post comment count
          final postIndex = _posts.indexWhere((p) => p.id == postId);
          if (postIndex != -1) {
            final currentCount =
                _posts[postIndex].engagementMetrics['comments'] ?? 0;
            _posts[postIndex].engagementMetrics['comments'] = currentCount + 1;
          }
        });
      }
      _showNotification('Comment added successfully');
    } catch (e) {
      _showNotification('Failed to add comment: ${e.toString()}');
    }
  }

  // Toggle video play/pause
  void _toggleVideoPlay(String postId) {
    final controller = _videoControllers[postId];
    if (controller == null) return;

    setState(() {
      if (_videoStates[postId] == true) {
        controller.pause();
        _videoStates[postId] = false;
        if (_currentPlayingVideo == postId) {
          _currentPlayingVideo = null;
        }
      } else {
        // Pause all other videos
        for (var otherId in _videoControllers.keys) {
          if (otherId != postId) {
            _videoControllers[otherId]?.pause();
            _videoStates[otherId] = false;
          }
        }

        controller.play();
        _videoStates[postId] = true;
        _currentPlayingVideo = postId;
      }
    });
  }

  // Toggle global mute state
  void _toggleMute() {
    setState(() {
      _globalMuteState = !_globalMuteState;
      for (var controller in _videoControllers.values) {
        controller.setVolume(_globalMuteState ? 0.0 : 1.0);
      }
    });
  }

  // Story functionality removed

  // Notification system
  void _showNotification(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Check if post contains video content
  bool _isVideoContent(SocialPost post) {
    final url = post.mediaUrl.toLowerCase();
    final hasVideoExtension = url.contains('.mp4') ||
        url.contains('.webm') ||
        url.contains('.mov') ||
        url.contains('.avi') ||
        url.contains('.mkv') ||
        url.contains('.m4v');

    final hasImageExtension = url.contains('.jpg') ||
        url.contains('.jpeg') ||
        url.contains('.png') ||
        url.contains('.gif') ||
        url.contains('.webp') ||
        url.contains('.bmp');

    if (hasImageExtension) {
      return false;
    }

    if (hasVideoExtension) {
      return true;
    }

    return post.contentType.toLowerCase().contains('video');
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          _handleBackNavigation();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: GestureDetector(
          // Add horizontal swipe gesture to go back
          onHorizontalDragEnd: (details) {
            if (details.primaryVelocity != null &&
                details.primaryVelocity! > 300) {
              // Swipe right to go back
              _navigateBack();
            }
          },
          child: Stack(
            children: [
              // Main feed content
              _buildMainFeed(),

              // Back button overlay
              _buildBackButton(),

              // Loading overlay
              if (_isLoading && _posts.isEmpty) _buildLoadingOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  // Handle back navigation
  void _handleBackNavigation() {
    // Pause any playing videos before going back
    _pauseAllVideos();

    // Clean up video controllers to free memory
    _cleanupVideoControllers();
  }

  // Pause all playing videos
  void _pauseAllVideos() {
    for (var controller in _videoControllers.values) {
      if (controller.value.isPlaying) {
        controller.pause();
      }
    }
    setState(() {
      _currentPlayingVideo = null;
      _videoStates.updateAll((key, value) => false);
    });
  }

  // Clean up video controllers for memory management
  void _cleanupVideoControllers() {
    // Only dispose controllers that are not currently visible
    final currentPost = _posts.isNotEmpty && _currentPostIndex < _posts.length
        ? _posts[_currentPostIndex]
        : null;

    final controllersToRemove = <String>[];

    for (var entry in _videoControllers.entries) {
      final postId = entry.key;
      final controller = entry.value;

      // Keep the current post's controller and adjacent ones for smooth scrolling
      if (currentPost != null &&
          (postId == currentPost.id ||
              _isAdjacentPost(postId, currentPost.id))) {
        continue;
      }

      // Dispose and remove other controllers
      controller.dispose();
      controllersToRemove.add(postId);
    }

    for (var postId in controllersToRemove) {
      _videoControllers.remove(postId);
      _videoStates.remove(postId);
    }
  }

  // Check if a post is adjacent to the current post
  bool _isAdjacentPost(String postId, String currentPostId) {
    final currentIndex = _posts.indexWhere((p) => p.id == currentPostId);
    final postIndex = _posts.indexWhere((p) => p.id == postId);

    if (currentIndex == -1 || postIndex == -1) return false;

    // Consider posts within 1 position as adjacent
    return (postIndex - currentIndex).abs() <= 1;
  }

  // Navigate back with optional result
  void _navigateBack([Map<String, dynamic>? result]) {
    _handleBackNavigation();
    Navigator.of(context).pop(result);
  }

  // Build back button overlay
  Widget _buildBackButton() {
    return Positioned(
      top: 50,
      left: 16,
      child: SafeArea(
        child: GestureDetector(
          onTap: _navigateBack,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.arrow_back_ios_new,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainFeed() {
    if (_hasError) {
      return _buildErrorState();
    }

    if (_posts.isEmpty && !_isLoading) {
      return _buildEmptyState();
    }

    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: _posts.length + (_hasMorePosts ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _posts.length) {
          return _buildLoadingPost();
        }
        return _buildFullScreenPost(_posts[index], index);
      },
    );
  }

  // Header and stories sections removed

  Widget _buildFullScreenPost(SocialPost post, int index) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // Media content
          Center(
            child: _buildPostMedia(post),
          ),

          // Mute button for videos (top right)
          if (_isVideoContent(post))
            Positioned(
              top: 50,
              right: 16,
              child: SafeArea(
                child: IconButton(
                  icon: Icon(
                    _globalMuteState ? Icons.volume_off : Icons.volume_up,
                    color: Colors.white,
                    size: 28,
                  ),
                  onPressed: _toggleMute,
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.black.withOpacity(0.5),
                    padding: const EdgeInsets.all(8),
                  ),
                ),
              ),
            ),

          // Right side actions
          _buildRightSideActions(post),

          // Bottom info
          _buildBottomInfo(post),

          // Comments overlay
          if (_showComments[post.id] == true) _buildCommentsOverlay(post),
        ],
      ),
    );
  }

  Widget _buildPostMedia(SocialPost post) {
    if (post.mediaUrl.isEmpty || post.mediaUrl == 'null') {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[900],
        child: const Center(
          child: Text(
            'No media available',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    final isVideo = _isVideoContent(post);

    if (isVideo) {
      return _buildVideoPlayer(post);
    } else {
      return Container(
        width: double.infinity,
        height: double.infinity,
        child: PlatformAwareNetworkImage(
          imageUrl: post.mediaUrl,
          fit: BoxFit.cover, // Changed to cover for full screen
          placeholder: (context, url) => Container(
            color: Colors.grey[900],
            child: const Center(
              child: CircularProgressIndicator(color: Colors.white),
            ),
          ),
          errorWidget: (context, url, error) {
            return Container(
              color: Colors.grey[900],
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 48, color: Colors.white),
                    SizedBox(height: 8),
                    Text(
                      'Image could not be loaded',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    }
  }

  Widget _buildVideoPlayer(SocialPost post) {
    final controller = _videoControllers[post.id];
    if (controller == null) {
      return Container(
        color: Colors.grey[900],
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    if (!controller.value.isInitialized) {
      return Container(
        color: Colors.grey[900],
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    final isPlaying = _videoStates[post.id] ?? false;

    return GestureDetector(
      onTap: () => _toggleVideoPlay(post.id),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            // Video player - full screen
            Positioned.fill(
              child: FittedBox(
                fit: BoxFit.cover, // Changed to cover for full screen
                child: SizedBox(
                  width: controller.value.size.width,
                  height: controller.value.size.height,
                  child: VideoPlayer(controller),
                ),
              ),
            ),

            // Play button overlay when paused
            if (!isPlaying)
              Center(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 48,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Post overlay removed - now using bottom info section

  Widget _buildRightSideActions(SocialPost post) {
    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        children: [
          // Like
          _buildActionButton(
            icon: post.isLiked ? Icons.favorite : Icons.favorite_border,
            color: post.isLiked ? Colors.red : Colors.white,
            count: post.likeCount,
            onTap: () => _toggleLikePost(post),
          ),
          const SizedBox(height: 20),

          // Comment
          _buildActionButton(
            icon: Icons.chat_bubble_outline,
            color: Colors.white,
            count: post.engagementMetrics['comments'] ?? 0,
            onTap: () => _toggleComments(post.id),
          ),
          const SizedBox(height: 20),

          // Share
          _buildActionButton(
            icon: Icons.send_outlined,
            color: Colors.white,
            count: post.shareCount,
            onTap: () {
              // Handle share
            },
          ),
          const SizedBox(height: 20),

          // Save
          _buildActionButton(
            icon: post.isSaved ? Icons.bookmark : Icons.bookmark_border,
            color: post.isSaved ? Colors.yellow : Colors.white,
            onTap: () => _toggleSavePost(post),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    int? count,
    required VoidCallback onTap,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
        ),
        if (count != null && count > 0)
          Text(
            count > 999
                ? '${(count / 1000).toStringAsFixed(1)}k'
                : count.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
      ],
    );
  }

  Widget _buildBottomInfo(SocialPost post) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 80,
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 40, 16, 20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [
              Colors.black.withOpacity(0.9),
              Colors.black.withOpacity(0.7),
              Colors.black.withOpacity(0.3),
              Colors.transparent,
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Username and time (Instagram style)
            Row(
              children: [
                // Avatar
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.2),
                  ),
                  child: post.userAvatar != null && post.userAvatar!.isNotEmpty
                      ? ClipOval(
                          child: PlatformAwareNetworkImage(
                            imageUrl: post.userAvatar!,
                            fit: BoxFit.cover,
                            width: 32,
                            height: 32,
                            errorWidget: (context, url, error) {
                              return const Icon(Icons.person,
                                  color: Colors.white, size: 18);
                            },
                          ),
                        )
                      : const Icon(Icons.person, color: Colors.white, size: 18),
                ),
                const SizedBox(width: 12),

                // Username and time
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.userName ?? 'Anonymous User',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        _formatTimeAgo(post.createdAt),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Caption
            if (post.caption.isNotEmpty)
              Text(
                post.caption,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

            // Health topics
            if (post.healthTopics.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: post.healthTopics.take(3).map((topic) {
                  return Text(
                    '#$topic',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsOverlay(SocialPost post) {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withOpacity(0.8),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Text(
                      'Comments',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () {
                        setState(() {
                          _showComments[post.id] = false;
                        });
                      },
                    ),
                  ],
                ),
              ),

              // Comments list
              Expanded(
                child: _buildCommentsList(post),
              ),

              // Add comment
              Container(
                constraints: const BoxConstraints(
                  maxHeight: 120, // Limit the height of the comment input
                ),
                child: _buildAddCommentSection(post),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentsList(SocialPost post) {
    final comments = _commentsData[post.id] ?? [];

    if (comments.isEmpty) {
      return const Center(
        child: Text(
          'No comments yet',
          style: TextStyle(color: Colors.white70),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: comments.length,
      itemBuilder: (context, index) {
        final comment = comments[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.2),
                ),
                child: comment.userAvatar != null &&
                        comment.userAvatar!.isNotEmpty
                    ? ClipOval(
                        child: PlatformAwareNetworkImage(
                          imageUrl: comment.userAvatar!,
                          fit: BoxFit.cover,
                          width: 32,
                          height: 32,
                          errorWidget: (context, url, error) {
                            return const Icon(Icons.person,
                                color: Colors.white, size: 20);
                          },
                        ),
                      )
                    : const Icon(Icons.person, color: Colors.white, size: 20),
              ),

              const SizedBox(width: 12),

              // Comment content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment.userName ?? 'Anonymous',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      comment.content,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatTimeAgo(comment.createdAt),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAddCommentSection(SocialPost post) {
    final TextEditingController commentController = TextEditingController();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        border: Border(
          top: BorderSide(color: Colors.white.withOpacity(0.1)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: commentController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Add a comment...',
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: Colors.white),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              maxLines: 2,
              minLines: 1,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.send, color: Colors.white),
            onPressed: () {
              if (commentController.text.trim().isNotEmpty) {
                _addComment(post.id, commentController.text.trim());
                commentController.clear();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingPost() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: CircularProgressIndicator(color: Colors.white),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            Text(
              'Error: $_errorMessage',
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _currentPage = 1;
                });
                _loadFeed();
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.feed_outlined,
              size: 64,
              color: Colors.white,
            ),
            const SizedBox(height: 24),
            const Text(
              'No posts yet',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Be the first to share health insights with the community',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CreatePostScreenNew(),
                  ),
                ).then((_) => _loadFeed());
              },
              icon: const Icon(Icons.add, size: 16),
              label: const Text('Create First Post'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.coralPop,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: CircularProgressIndicator(color: Colors.white),
      ),
    );
  }

  // Format time ago
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}

// Extension to add copyWith method to SocialPost
extension SocialPostExtension on SocialPost {
  SocialPost copyWith({
    String? id,
    String? source,
    String? sourceId,
    String? contentType,
    String? mediaUrl,
    String? caption,
    List<String>? healthTopics,
    double? relevanceScore,
    String? filteredStatus,
    DateTime? createdAt,
    bool? isLiked,
    bool? isSaved,
    String? userName,
    String? userAvatar,
    Map<String, dynamic>? engagementMetrics,
  }) {
    return SocialPost(
      id: id ?? this.id,
      source: source ?? this.source,
      sourceId: sourceId ?? this.sourceId,
      contentType: contentType ?? this.contentType,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      caption: caption ?? this.caption,
      healthTopics: healthTopics ?? this.healthTopics,
      relevanceScore: relevanceScore ?? this.relevanceScore,
      filteredStatus: filteredStatus ?? this.filteredStatus,
      createdAt: createdAt ?? this.createdAt,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      engagementMetrics: engagementMetrics ?? this.engagementMetrics,
    );
  }
}
