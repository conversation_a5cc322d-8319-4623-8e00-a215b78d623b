#!/bin/bash

# Medroid Status Checker
# Shows the status of all Medroid services

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/tunnel.pid"
LARAVEL_PID_FILE="$SCRIPT_DIR/laravel.pid"
DOMAIN="api.medroid.ai"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}🏥 Medroid Service Status${NC}"
echo -e "${BLUE}================================${NC}"

# Check Laravel server
echo -e "\n${BLUE}📋 Laravel Server:${NC}"
if [[ -f "$LARAVEL_PID_FILE" ]]; then
    laravel_pid=$(cat "$LARAVEL_PID_FILE")
    if kill -0 "$laravel_pid" 2>/dev/null; then
        echo -e "${GREEN}✅ Running (PID: $laravel_pid)${NC}"
        if curl -s http://localhost:8000 >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Responding on port 8000${NC}"
        else
            echo -e "${RED}❌ Not responding on port 8000${NC}"
        fi
    else
        echo -e "${RED}❌ Not running (stale PID file)${NC}"
    fi
else
    echo -e "${RED}❌ Not running${NC}"
fi

# Check tunnel
echo -e "\n${BLUE}🌐 Cloudflare Tunnel:${NC}"
if [[ -f "$PID_FILE" ]]; then
    tunnel_pid=$(cat "$PID_FILE")
    if kill -0 "$tunnel_pid" 2>/dev/null; then
        echo -e "${GREEN}✅ Running (PID: $tunnel_pid)${NC}"
    else
        echo -e "${RED}❌ Not running (stale PID file)${NC}"
    fi
else
    echo -e "${RED}❌ Not running${NC}"
fi

# Check domain connectivity
echo -e "\n${BLUE}🔗 Domain Connectivity:${NC}"
if curl -s -I "https://$DOMAIN" --connect-timeout 10 >/dev/null 2>&1; then
    echo -e "${GREEN}✅ https://$DOMAIN is reachable${NC}"
    
    # Check API health
    if curl -s "https://$DOMAIN/api/health" --connect-timeout 5 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ API health endpoint responding${NC}"
    else
        echo -e "${YELLOW}⚠️  API health endpoint not responding${NC}"
    fi
else
    echo -e "${RED}❌ https://$DOMAIN is not reachable${NC}"
fi

# Show recent logs
echo -e "\n${BLUE}📝 Recent Logs (last 5 lines):${NC}"
if [[ -f "$SCRIPT_DIR/medroid.log" ]]; then
    tail -5 "$SCRIPT_DIR/medroid.log"
else
    echo -e "${YELLOW}No log file found${NC}"
fi

echo -e "\n${BLUE}================================${NC}"
