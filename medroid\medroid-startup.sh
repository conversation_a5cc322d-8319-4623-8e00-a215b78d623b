#!/bin/bash

# Medroid Professional Startup Manager
# Provides stable, persistent tunnel connection and service management
# Features: Auto-restart, health monitoring, process management, logging

set -e

# Configuration
DOMAIN="api.medroid.ai"
TUNNEL_NAME="medroid-backend"
LOCAL_PORT="8000"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/medroid.log"
PID_FILE="$SCRIPT_DIR/tunnel.pid"
LARAVEL_PID_FILE="$SCRIPT_DIR/laravel.pid"
CONFIG_FILE="$HOME/.cloudflared/config.yml"
MAX_RETRIES=5
RETRY_DELAY=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function with timestamps
log() {
    echo -e "${CYAN}$(date '+%Y-%m-%d %H:%M:%S')${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $(date '+%Y-%m-%d %H:%M:%S') $1${NC}" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $(date '+%Y-%m-%d %H:%M:%S') $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $(date '+%Y-%m-%d %H:%M:%S') $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}ℹ️  $(date '+%Y-%m-%d %H:%M:%S') $1${NC}" | tee -a "$LOG_FILE"
}

# Cleanup function
cleanup() {
    log "🧹 Cleaning up processes..."
    if [[ -f "$PID_FILE" ]]; then
        local tunnel_pid=$(cat "$PID_FILE")
        if kill -0 "$tunnel_pid" 2>/dev/null; then
            kill "$tunnel_pid" 2>/dev/null || true
            success "Tunnel process stopped"
        fi
        rm -f "$PID_FILE"
    fi
    
    # Clean up any orphaned cloudflared processes
    pkill -f "cloudflared tunnel run" 2>/dev/null || true
    
    log "👋 Medroid Startup Manager stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM EXIT

# Check if script is already running
check_running() {
    if [[ -f "$PID_FILE" ]]; then
        local old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            error "Startup manager is already running (PID: $old_pid)"
            error "Use './medroid-stop.sh' to stop it first"
            exit 1
        else
            warning "Removing stale PID file"
            rm -f "$PID_FILE"
        fi
    fi
}

# Health check function
health_check() {
    local retries=0
    while [[ $retries -lt 3 ]]; do
        if curl -s -I "https://$DOMAIN/api/health" --connect-timeout 5 | head -n 1 | grep -q "200"; then
            return 0
        fi
        ((retries++))
        sleep 2
    done
    return 1
}

# Laravel server management
manage_laravel() {
    log "🔧 Managing Laravel server..."
    
    # Check if Laravel is already running
    if [[ -f "$LARAVEL_PID_FILE" ]]; then
        local laravel_pid=$(cat "$LARAVEL_PID_FILE")
        if kill -0 "$laravel_pid" 2>/dev/null; then
            success "Laravel server already running (PID: $laravel_pid)"
            return 0
        else
            warning "Removing stale Laravel PID file"
            rm -f "$LARAVEL_PID_FILE"
        fi
    fi
    
    # Check if port is in use
    if lsof -i :$LOCAL_PORT >/dev/null 2>&1; then
        local existing_pid=$(lsof -t -i :$LOCAL_PORT)
        warning "Port $LOCAL_PORT is in use by PID: $existing_pid"
        if ps -p "$existing_pid" -o comm= | grep -q php; then
            success "Laravel server already running on port $LOCAL_PORT"
            echo "$existing_pid" > "$LARAVEL_PID_FILE"
            return 0
        else
            error "Port $LOCAL_PORT is occupied by non-Laravel process"
            return 1
        fi
    fi
    
    # Start Laravel server
    cd "$SCRIPT_DIR"
    nohup php artisan serve --host=0.0.0.0 --port=$LOCAL_PORT > laravel.log 2>&1 &
    local laravel_pid=$!
    echo "$laravel_pid" > "$LARAVEL_PID_FILE"
    
    # Wait for Laravel to start
    local attempts=0
    while [[ $attempts -lt 10 ]]; do
        if curl -s http://localhost:$LOCAL_PORT >/dev/null 2>&1; then
            success "Laravel server started successfully (PID: $laravel_pid)"
            return 0
        fi
        sleep 1
        ((attempts++))
    done
    
    error "Failed to start Laravel server"
    return 1
}

# Tunnel management with auto-restart
manage_tunnel() {
    local retries=0
    
    while [[ $retries -lt $MAX_RETRIES ]]; do
        log "🚀 Starting tunnel (attempt $((retries + 1))/$MAX_RETRIES)..."
        
        # Start tunnel in background
        cloudflared tunnel run "$TUNNEL_NAME" &
        local tunnel_pid=$!
        echo "$tunnel_pid" > "$PID_FILE"
        
        # Wait a bit for tunnel to establish
        sleep 5
        
        # Check if tunnel is still running
        if kill -0 "$tunnel_pid" 2>/dev/null; then
            success "Tunnel started successfully (PID: $tunnel_pid)"
            
            # Wait for tunnel to be healthy
            local health_attempts=0
            while [[ $health_attempts -lt 12 ]]; do  # 60 seconds total
                if health_check; then
                    success "Tunnel is healthy and responding"
                    info "🌐 Application available at: https://$DOMAIN"
                    info "📱 Mobile API endpoint: https://$DOMAIN/api/"
                    
                    # Wait for tunnel process to exit
                    wait "$tunnel_pid"
                    local exit_code=$?
                    
                    if [[ $exit_code -eq 0 ]]; then
                        log "Tunnel exited normally"
                        break
                    else
                        warning "Tunnel exited with code: $exit_code"
                    fi
                else
                    ((health_attempts++))
                    sleep 5
                fi
            done
            
            if [[ $health_attempts -eq 12 ]]; then
                warning "Tunnel started but health check failed"
            fi
        else
            error "Tunnel failed to start"
        fi
        
        ((retries++))
        if [[ $retries -lt $MAX_RETRIES ]]; then
            warning "Retrying in $RETRY_DELAY seconds..."
            sleep $RETRY_DELAY
        fi
    done
    
    error "Failed to start tunnel after $MAX_RETRIES attempts"
    return 1
}

# Main execution
main() {
    echo -e "${PURPLE}🏥 Medroid Professional Startup Manager${NC}"
    echo -e "${CYAN}🌐 Domain: $DOMAIN${NC}"
    echo -e "${CYAN}📅 Started: $(date)${NC}"
    
    # Initialize log file
    echo "=== Medroid Startup Manager - $(date) ===" >> "$LOG_FILE"
    
    # Check if already running
    check_running
    
    # Verify prerequisites
    log "🔍 Checking prerequisites..."
    
    if ! command -v cloudflared &> /dev/null; then
        error "Cloudflared is not installed. Please install it first."
        exit 1
    fi
    
    if ! cloudflared tunnel list &> /dev/null; then
        error "Cloudflare authentication failed. Please run 'cloudflared tunnel login' first."
        exit 1
    fi
    
    success "Prerequisites verified"
    
    # Manage Laravel server
    if ! manage_laravel; then
        error "Failed to start Laravel server"
        exit 1
    fi
    
    # Verify tunnel exists
    if ! cloudflared tunnel info "$TUNNEL_NAME" &> /dev/null; then
        error "Tunnel '$TUNNEL_NAME' does not exist. Please create it first."
        exit 1
    fi
    
    success "Tunnel '$TUNNEL_NAME' verified"
    
    # Start tunnel with auto-restart
    manage_tunnel
}

# Run main function
main "$@"
