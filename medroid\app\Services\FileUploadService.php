<?php

namespace App\Services;

use App\Models\File;
use App\Models\FileUsage;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadService
{
    /**
     * Upload a file and save it to the file management system
     */
    public function uploadFile(
        UploadedFile $uploadedFile,
        string $category = 'general',
        string $name = null,
        string $description = null,
        bool $isPublic = false,
        int $userId = null,
        int $clinicId = null
    ): File {
        $user = $userId ? \App\Models\User::find($userId) : Auth::user();
        
        if (!$user) {
            throw new \Exception('User not found for file upload');
        }

        // Validate file type
        if (!$this->isAllowedFileType($uploadedFile)) {
            throw new \Exception('File type not allowed');
        }

        // Check file size limits
        $maxSize = $this->getMaxFileSize($user);
        if ($uploadedFile->getSize() > $maxSize) {
            throw new \Exception('File size exceeds the limit for your account type');
        }

        // Sanitize filename
        $originalName = $this->sanitizeFilename($uploadedFile->getClientOriginalName());
        
        // Generate unique filename
        $extension = $uploadedFile->getClientOriginalExtension();
        $filename = Str::uuid() . '.' . $extension;
        
        // Determine category if not provided
        if ($category === 'general') {
            $category = $this->determineCategoryFromMimeType($uploadedFile->getMimeType());
        }

        // Generate organized file path based on user role and clinic
        $filePath = $this->generateOrganizedFilePath($user, $category, $clinicId);

        // Store file in organized directory structure
        $path = $uploadedFile->storeAs(
            $filePath,
            $filename,
            'public'
        );

        // Basic security scan
        $fullPath = storage_path('app/public/' . $path);
        if (!$this->scanFileForMalware($fullPath)) {
            Storage::disk('public')->delete($path);
            throw new \Exception('File failed security scan');
        }

        // Create file record
        $file = File::create([
            'user_id' => $user->id,
            'name' => $name ?: $originalName,
            'original_name' => $originalName,
            'path' => $path,
            'disk' => 'public',
            'mime_type' => $uploadedFile->getMimeType(),
            'size' => $uploadedFile->getSize(),
            'category' => $category,
            'extension' => $extension,
            'description' => $description,
            'is_public' => $isPublic,
        ]);

        return $file;
    }

    /**
     * Upload and attach file to a model
     */
    public function uploadAndAttachFile(
        UploadedFile $uploadedFile,
        $model,
        string $usageType,
        string $category = 'general',
        string $name = null,
        string $description = null,
        bool $isPublic = false,
        int $clinicId = null
    ): File {
        // Upload the file
        $file = $this->uploadFile($uploadedFile, $category, $name, $description, $isPublic, null, $clinicId);

        // Attach to model
        FileUsage::create([
            'file_id' => $file->id,
            'usable_type' => get_class($model),
            'usable_id' => $model->id,
            'usage_type' => $usageType,
        ]);

        return $file;
    }

    /**
     * Replace old file upload with new file management system
     * This method helps migrate existing upload functionality
     */
    public function replaceDirectUpload(
        UploadedFile $uploadedFile,
        string $category,
        string $usageType = null,
        $model = null
    ): array {
        // Upload to file management system
        $file = $this->uploadFile($uploadedFile, $category);

        // If model and usage type provided, attach the file
        if ($model && $usageType) {
            FileUsage::create([
                'file_id' => $file->id,
                'usable_type' => get_class($model),
                'usable_id' => $model->id,
                'usage_type' => $usageType,
            ]);
        }

        return [
            'file' => $file,
            'url' => $file->url,
            'path' => $file->path,
            'id' => $file->id,
        ];
    }

    /**
     * Get file URL from file ID
     */
    public function getFileUrl(int $fileId): ?string
    {
        $file = File::find($fileId);
        return $file ? $file->url : null;
    }

    /**
     * Delete old file and upload new one
     */
    public function replaceFile(
        UploadedFile $newFile,
        int $oldFileId,
        string $category = null
    ): File {
        // Get old file
        $oldFile = File::find($oldFileId);
        
        // Upload new file
        $category = $category ?: ($oldFile ? $oldFile->category : 'general');
        $newFileRecord = $this->uploadFile($newFile, $category);

        // If old file exists, transfer its usages to new file
        if ($oldFile) {
            FileUsage::where('file_id', $oldFileId)->update(['file_id' => $newFileRecord->id]);
            $oldFile->delete();
        }

        return $newFileRecord;
    }

    /**
     * Check if file type is allowed
     */
    private function isAllowedFileType(UploadedFile $file): bool
    {
        $allowedMimeTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
            'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'video/mp4', 'video/mpeg', 'video/quicktime', 'video/webm',
            'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4',
            'application/zip', 'text/plain', 'text/csv'
        ];

        $mimeType = $file->getMimeType();
        $extension = strtolower($file->getClientOriginalExtension());

        if (!in_array($mimeType, $allowedMimeTypes)) {
            return false;
        }

        $allowedExtensions = [
            'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
            'pdf', 'doc', 'docx', 'txt', 'csv',
            'mp4', 'mpeg', 'mov', 'webm',
            'mp3', 'wav', 'ogg', 'm4a', 'zip'
        ];

        return in_array($extension, $allowedExtensions);
    }

    /**
     * Get max file size based on user role
     */
    private function getMaxFileSize($user): int
    {
        if ($user->hasRole('admin')) {
            return 100 * 1024 * 1024; // 100MB
        } elseif ($user->hasRole('provider')) {
            return 50 * 1024 * 1024; // 50MB
        } else {
            return 10 * 1024 * 1024; // 10MB
        }
    }

    /**
     * Sanitize filename
     */
    private function sanitizeFilename(string $filename): string
    {
        $filename = basename($filename);
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $filename);
        
        if (empty($filename) || strlen($filename) > 255) {
            $filename = 'file_' . time();
        }

        return $filename;
    }

    /**
     * Determine category from MIME type
     */
    private function determineCategoryFromMimeType(string $mimeType): string
    {
        if (Str::startsWith($mimeType, 'image/')) {
            return 'general';
        } elseif (Str::startsWith($mimeType, 'video/')) {
            return 'videos';
        } elseif (Str::startsWith($mimeType, 'audio/')) {
            return 'audio';
        } elseif (in_array($mimeType, [
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        ])) {
            return 'documents';
        }

        return 'general';
    }

    /**
     * Basic malware scan
     */
    private function scanFileForMalware(string $filePath): bool
    {
        $fileSize = filesize($filePath);
        if ($fileSize > 100 * 1024 * 1024) { // 100MB
            return false;
        }

        return true;
    }

    /**
     * Generate organized file path based on user role, clinic, and category
     */
    private function generateOrganizedFilePath($user, string $category, int $clinicId = null): string
    {
        $basePath = 'files';

        // Determine user's primary role
        $userRole = $user->getPrimaryRole();

        // Get clinic ID if not provided
        if (!$clinicId && $user->primaryClinic()) {
            $clinicId = $user->primaryClinic()->id;
        }

        switch ($userRole) {
            case 'admin':
                // Admin files go in admin folder
                return $basePath . '/admin/' . $this->getAdminSubfolder($category) . '/' . $user->id;

            case 'provider':
                // Provider files go in clinic/providers folder
                if ($clinicId) {
                    return $basePath . '/clinics/' . $clinicId . '/providers/' . $user->id . '/' . $category;
                } else {
                    // Fallback for providers without clinic
                    return $basePath . '/general/providers/' . $user->id . '/' . $category;
                }

            case 'patient':
                // Patient files go in clinic/patients folder
                if ($clinicId) {
                    return $basePath . '/clinics/' . $clinicId . '/patients/' . $user->id . '/' . $category;
                } else {
                    // Fallback for patients without clinic
                    return $basePath . '/general/patients/' . $user->id . '/' . $category;
                }

            default:
                // Default to general folder for unknown roles
                return $basePath . '/general/' . $category . '/' . $user->id;
        }
    }

    /**
     * Get admin subfolder based on category
     */
    private function getAdminSubfolder(string $category): string
    {
        $adminFolders = [
            'system' => ['general', 'documents'],
            'templates' => ['templates', 'imports'],
            'reports' => ['reports', 'analytics'],
            'backups' => ['backups', 'exports']
        ];

        foreach ($adminFolders as $folder => $categories) {
            if (in_array($category, $categories)) {
                return $folder;
            }
        }

        return 'system'; // Default admin subfolder
    }

    /**
     * Get clinic-specific file path for shared clinic files
     */
    public function getClinicSharedPath(int $clinicId, string $category): string
    {
        return 'files/clinics/' . $clinicId . '/shared/' . $category;
    }

    /**
     * Get product file path for clinic products
     */
    public function getClinicProductPath(int $clinicId, string $category = 'images'): string
    {
        return 'files/clinics/' . $clinicId . '/products/' . $category;
    }
}
