import{z as q,c,r as p,o as K,d as l,e as a,f as m,u as f,m as Q,g as v,i as t,l as S,v as H,F as _,p as $,t as n,q as L,A as N,x as y,n as h,y as T,P as w}from"./vendor-BhKTHoN5.js";import{_ as J}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import{_ as O}from"./BulkImportModal-DkR4MIpW.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const W={class:"flex items-center justify-between"},X={class:"flex mt-2","aria-label":"Breadcrumb"},Y={class:"inline-flex items-center space-x-1 md:space-x-3"},Z={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},ee={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},te={key:0,class:"flex space-x-3"},se={class:"py-12"},re={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},ae={class:"mb-6 bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},le={class:"p-6"},oe={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},de=["value"],ne={class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},ie={class:"p-6 text-gray-900 dark:text-gray-100"},ue={key:0,class:"text-center py-8"},ce={key:1,class:"text-center py-8"},pe={key:2,class:"overflow-x-auto"},ge={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},xe={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ve={class:"px-6 py-4 whitespace-nowrap"},ye={class:"flex items-center"},me={class:"flex-shrink-0 h-10 w-10"},fe=["src","alt"],he={key:1,class:"h-10 w-10 rounded bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},ke={class:"ml-4"},be={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},_e={class:"text-sm text-gray-500 dark:text-gray-400"},we={class:"px-6 py-4 whitespace-nowrap"},Pe={class:"text-sm text-gray-900 dark:text-gray-100"},Ce={class:"px-6 py-4 whitespace-nowrap"},Be={class:"px-6 py-4 whitespace-nowrap"},Ae={class:"text-sm text-gray-900 dark:text-gray-100"},Se={key:0,class:"text-xs text-gray-500 line-through ml-1"},$e={class:"px-6 py-4 whitespace-nowrap"},Ie={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Me={key:1,class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"},Fe={__name:"Products",setup(Ue){const D=q(),d=c(()=>{var r;return(r=D.props.auth)==null?void 0:r.user}),P=[{title:"Dashboard",href:"/dashboard"},{title:"My Products",href:"/provider/products"}],C=p(!1),i=p([]),I=p([]),u=p(""),g=p("all"),x=p("all"),B=p(!1),A=async()=>{var r;C.value=!0;try{const e=new URLSearchParams;u.value&&e.append("search",u.value),g.value!=="all"&&e.append("category",g.value),x.value!=="all"&&e.append("type",x.value),console.log("Fetching products from:",`/provider/products-list?${e.toString()}`);const s=await window.axios.get(`/provider/products-list?${e.toString()}`);console.log("Products response:",s.data),console.log("Products structure:",s.data.products),s.data.products&&s.data.products.data?i.value=s.data.products.data||[]:Array.isArray(s.data.products)?i.value=s.data.products:i.value=[],s.data.categories&&(I.value=s.data.categories),console.log("Products loaded:",i.value.length),console.log("Actual products:",i.value)}catch(e){console.error("Error fetching products:",e),console.error("Error response:",(r=e.response)==null?void 0:r.data),i.value=[]}finally{C.value=!1}},E=r=>r?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",F=r=>r==="digital"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300":"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",M=r=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(r),U=c(()=>i.value.filter(r=>{const e=!u.value||r.name.toLowerCase().includes(u.value.toLowerCase())||r.sku.toLowerCase().includes(u.value.toLowerCase()),s=g.value==="all"||r.category_id==g.value,o=x.value==="all"||r.type===x.value;return e&&s&&o})),k=c(()=>{var r,e,s;return((e=(r=d.value)==null?void 0:r.roles)==null?void 0:e.some(o=>o.name==="admin"))||((s=d.value)==null?void 0:s.role)==="admin"||!1}),b=c(()=>{var r,e,s;return((e=(r=d.value)==null?void 0:r.roles)==null?void 0:e.some(o=>o.name==="provider"))||((s=d.value)==null?void 0:s.role)==="provider"||!1}),j=c(()=>{var e,s,o,V;console.log("User data:",d.value),console.log("User role:",(e=d.value)==null?void 0:e.role),console.log("User roles array:",(s=d.value)==null?void 0:s.roles),console.log("Is Admin:",k.value),console.log("Is Provider:",b.value);const r=k.value||b.value||((V=(o=d.value)==null?void 0:o.user_permissions)==null?void 0:V.includes("create products"))||!1;return console.log("Can create products:",r),r}),z=c(()=>{var r,e;return k.value||b.value||((e=(r=d.value)==null?void 0:r.user_permissions)==null?void 0:e.includes("edit products"))||!1}),G=c(()=>{var r,e;return k.value||b.value||((e=(r=d.value)==null?void 0:r.user_permissions)==null?void 0:e.includes("delete products"))||!1}),R=r=>{alert(`Successfully imported ${r.imported_count} products!`),A()};return K(()=>{A()}),(r,e)=>(a(),l(_,null,[m(f(Q),{title:"My Products"}),m(J,null,{header:v(()=>[t("div",W,[t("div",null,[e[6]||(e[6]=t("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," My Products ",-1)),t("nav",X,[t("ol",Y,[(a(),l(_,null,$(P,(s,o)=>t("li",{key:o,class:"inline-flex items-center"},[o<P.length-1?(a(),T(f(w),{key:0,href:s.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},{default:v(()=>[y(n(s.title),1)]),_:2},1032,["href"])):(a(),l("span",Z,n(s.title),1)),o<P.length-1?(a(),l("svg",ee,e[5]||(e[5]=[t("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):h("",!0)])),64))])])]),j.value?(a(),l("div",te,[t("button",{onClick:e[0]||(e[0]=s=>B.value=!0),class:"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"},e[7]||(e[7]=[t("i",{class:"fas fa-upload mr-2"},null,-1),y(" Bulk Import ")])),m(f(w),{href:"/provider/products/create",class:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"},{default:v(()=>e[8]||(e[8]=[t("i",{class:"fas fa-plus mr-2"},null,-1),y(" Add Product ")])),_:1})])):h("",!0)])]),default:v(()=>[t("div",se,[t("div",re,[t("div",ae,[t("div",le,[t("div",oe,[t("div",null,[S(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>u.value=s),type:"text",placeholder:"Search products...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},null,512),[[H,u.value]])]),t("div",null,[S(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>g.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},[e[9]||(e[9]=t("option",{value:"all"},"All Categories",-1)),(a(!0),l(_,null,$(I.value,s=>(a(),l("option",{key:s.id,value:s.id},n(s.name),9,de))),128))],512),[[L,g.value]])]),t("div",null,[S(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>x.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"},e[10]||(e[10]=[t("option",{value:"all"},"All Types",-1),t("option",{value:"physical"},"Physical",-1),t("option",{value:"digital"},"Digital",-1)]),512),[[L,x.value]])]),t("div",null,[t("button",{onClick:A,class:"w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"}," Refresh ")])])])]),t("div",ne,[t("div",ie,[C.value?(a(),l("div",ue,e[11]||(e[11]=[t("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):U.value.length===0?(a(),l("div",ce,e[12]||(e[12]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"No products found.",-1)]))):(a(),l("div",pe,[t("table",ge,[e[16]||(e[16]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Product "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Category "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Type "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Price "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Actions ")])],-1)),t("tbody",xe,[(a(!0),l(_,null,$(U.value,s=>{var o;return a(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",ve,[t("div",ye,[t("div",me,[s.primary_image?(a(),l("img",{key:0,src:`/storage/${s.primary_image}`,alt:s.name,class:"h-10 w-10 rounded object-cover"},null,8,fe)):(a(),l("div",he,e[13]||(e[13]=[t("i",{class:"fas fa-box text-gray-500 dark:text-gray-400"},null,-1)])))]),t("div",ke,[t("div",be,n(s.name),1),t("div",_e," SKU: "+n(s.sku),1)])])]),t("td",we,[t("span",Pe,n(((o=s.category)==null?void 0:o.name)||"N/A"),1)]),t("td",Ce,[t("span",{class:N([F(s.type),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(s.type),3)]),t("td",Be,[t("div",Ae,[y(n(M(s.price))+" ",1),s.sale_price?(a(),l("span",Se,n(M(s.sale_price)),1)):h("",!0)])]),t("td",$e,[t("span",{class:N([E(s.is_active),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(s.is_active?"Active":"Inactive"),3)]),t("td",Ie,[m(f(w),{href:`/provider/products/${s.id}`,class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"},{default:v(()=>e[14]||(e[14]=[y(" View ")])),_:2},1032,["href"]),z.value?(a(),T(f(w),{key:0,href:`/provider/products/${s.id}/edit`,class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"},{default:v(()=>e[15]||(e[15]=[y(" Edit ")])),_:2},1032,["href"])):h("",!0),G.value?(a(),l("button",Me," Delete ")):h("",!0)])])}),128))])])]))])])])]),m(O,{"is-open":B.value,onClose:e[4]||(e[4]=s=>B.value=!1),onImported:R},null,8,["is-open"])]),_:1})],64))}};export{Fe as default};
