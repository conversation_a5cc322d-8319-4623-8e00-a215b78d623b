<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import { ref, reactive, computed, onMounted } from 'vue';

const props = defineProps({
    product: Object,
    categories: Array,
});

// Get current user and determine if this is a provider route
const page = usePage();
const currentUser = computed(() => page.props.auth?.user);
const isProvider = computed(() => currentUser.value?.role === 'provider');

// Dynamic breadcrumbs and URLs based on user role
const breadcrumbs = computed(() => {
    if (isProvider.value) {
        return [
            { title: 'Dashboard', href: '/dashboard' },
            { title: 'My Products', href: '/provider/products' },
            { title: 'Edit Product', href: '#' },
        ];
    } else {
        return [
            { title: 'Dashboard', href: '/dashboard' },
            { title: 'Products', href: '/admin/products' },
            { title: 'Edit Product', href: '#' },
        ];
    }
});

const apiPrefix = computed(() => isProvider.value ? '/provider' : '/admin');
const backUrl = computed(() => isProvider.value ? '/provider/products' : '/admin/products');

const loading = ref(false);
const form = reactive({
    name: '',
    description: '',
    short_description: '',
    type: 'physical',
    category_id: '',
    price: '',
    sale_price: '',
    sku: '',
    stock_quantity: '',
    manage_stock: true,
    weight: '',
    dimensions: '',
    is_featured: false,
    is_active: true,
    digital_files: [],
    download_limit: '',
    download_expiry_days: '',
    featured_image: null,
    gallery_images: []
});

const errors = ref({});

const isDigital = computed(() => form.type === 'digital');

// Initialize form with product data
onMounted(() => {
    if (props.product) {
        Object.keys(form).forEach(key => {
            if (props.product[key] !== undefined && key !== 'featured_image' && key !== 'gallery_images') {
                form[key] = props.product[key];
            }
        });
    }
});

const handleSubmit = async () => {
    loading.value = true;
    errors.value = {};

    try {
        const formData = new FormData();
        
        // Add all form fields
        Object.keys(form).forEach(key => {
            if (key === 'featured_image' && form[key]) {
                formData.append(key, form[key]);
            } else if (key === 'gallery_images' && form[key].length > 0) {
                form[key].forEach((file, index) => {
                    formData.append(`gallery_images[${index}]`, file);
                });
            } else if (key === 'digital_files' && isDigital.value) {
                formData.append(key, JSON.stringify(form[key]));
            } else if (form[key] !== null && form[key] !== '') {
                // Convert boolean values to 1/0 for Laravel validation
                if (typeof form[key] === 'boolean') {
                    formData.append(key, form[key] ? '1' : '0');
                } else {
                    formData.append(key, form[key]);
                }
            }
        });

        // Ensure boolean fields are always included (even if false)
        const booleanFields = ['manage_stock', 'is_featured', 'is_active'];
        booleanFields.forEach(field => {
            if (!formData.has(field)) {
                formData.append(field, form[field] ? '1' : '0');
            }
        });

        // Add _method field for Laravel to recognize this as a PUT request
        formData.append('_method', 'PUT');

        const response = await window.axios.post(`${apiPrefix.value}/save-product/${props.product.id}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        if (response.data.success) {
            router.visit(backUrl.value, {
                onSuccess: () => {
                    alert('Product updated successfully!');
                }
            });
        }
    } catch (error) {
        if (error.response?.status === 422) {
            errors.value = error.response.data.errors || {};
        } else {
            alert('Error updating product: ' + (error.response?.data?.message || error.message));
        }
    } finally {
        loading.value = false;
    }
};

const handleFeaturedImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
        form.featured_image = file;
    }
};

const handleGalleryImagesChange = (event) => {
    const files = Array.from(event.target.files);
    form.gallery_images = files;
};
</script>

<template>
    <Head title="Edit Product" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        Edit Product: {{ product?.name }}
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <Link :href="backUrl" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Products
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <!-- Current Images Display -->
                        <div v-if="product?.featured_image || product?.images?.length" class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Current Images</h3>
                            <div class="flex flex-wrap gap-4">
                                <div v-if="product.featured_image" class="relative">
                                    <img
                                        :src="product.featured_image?.startsWith('http') ? product.featured_image : `/storage/${product.featured_image}`"
                                        :alt="product.name"
                                        class="w-24 h-24 object-cover rounded border"
                                    >
                                    <span class="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">Featured</span>
                                </div>
                                <div v-for="image in product.images" :key="image.id" class="relative">
                                    <img
                                        :src="image.full_url || (image.image_path?.startsWith('http') ? image.image_path : `/storage/${image.image_path}`)"
                                        :alt="image.alt_text"
                                        class="w-24 h-24 object-cover rounded border"
                                    >
                                </div>
                            </div>
                        </div>

                        <form @submit.prevent="handleSubmit" class="space-y-6">
                            <!-- Basic Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Product Name *</label>
                                    <input 
                                        v-model="form.name"
                                        type="text" 
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.name" class="text-red-500 text-sm">{{ errors.name[0] }}</span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">SKU *</label>
                                    <input 
                                        v-model="form.sku"
                                        type="text" 
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.sku" class="text-red-500 text-sm">{{ errors.sku[0] }}</span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Product Type *</label>
                                    <select 
                                        v-model="form.type"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                        <option value="physical">Physical Product</option>
                                        <option value="digital">Digital Product</option>
                                    </select>
                                    <span v-if="errors.type" class="text-red-500 text-sm">{{ errors.type[0] }}</span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category *</label>
                                    <select 
                                        v-model="form.category_id"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                        <option value="">Select Category</option>
                                        <option v-for="category in categories" :key="category.id" :value="category.id">
                                            {{ category.name }}
                                        </option>
                                    </select>
                                    <span v-if="errors.category_id" class="text-red-500 text-sm">{{ errors.category_id[0] }}</span>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Short Description</label>
                                <textarea 
                                    v-model="form.short_description"
                                    rows="2"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                ></textarea>
                                <span v-if="errors.short_description" class="text-red-500 text-sm">{{ errors.short_description[0] }}</span>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description *</label>
                                <textarea 
                                    v-model="form.description"
                                    rows="4"
                                    required
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                ></textarea>
                                <span v-if="errors.description" class="text-red-500 text-sm">{{ errors.description[0] }}</span>
                            </div>

                            <!-- Pricing -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Price *</label>
                                    <input 
                                        v-model="form.price"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.price" class="text-red-500 text-sm">{{ errors.price[0] }}</span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sale Price</label>
                                    <input 
                                        v-model="form.sale_price"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.sale_price" class="text-red-500 text-sm">{{ errors.sale_price[0] }}</span>
                                </div>
                            </div>

                            <!-- Physical Product Fields -->
                            <div v-if="!isDigital" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stock Quantity *</label>
                                    <input 
                                        v-model="form.stock_quantity"
                                        type="number" 
                                        min="0"
                                        required
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.stock_quantity" class="text-red-500 text-sm">{{ errors.stock_quantity[0] }}</span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Weight (kg)</label>
                                    <input 
                                        v-model="form.weight"
                                        type="number" 
                                        step="0.01"
                                        min="0"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.weight" class="text-red-500 text-sm">{{ errors.weight[0] }}</span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dimensions</label>
                                    <input 
                                        v-model="form.dimensions"
                                        type="text" 
                                        placeholder="L x W x H"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.dimensions" class="text-red-500 text-sm">{{ errors.dimensions[0] }}</span>
                                </div>
                            </div>

                            <!-- Images -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Replace Featured Image</label>
                                    <input 
                                        type="file" 
                                        accept="image/*"
                                        @change="handleFeaturedImageChange"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.featured_image" class="text-red-500 text-sm">{{ errors.featured_image[0] }}</span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Add Gallery Images</label>
                                    <input 
                                        type="file" 
                                        accept="image/*"
                                        multiple
                                        @change="handleGalleryImagesChange"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <span v-if="errors.gallery_images" class="text-red-500 text-sm">{{ errors.gallery_images[0] }}</span>
                                </div>
                            </div>

                            <!-- Status Options -->
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input 
                                        v-model="form.is_featured"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Featured Product</span>
                                </label>

                                <label class="flex items-center">
                                    <input 
                                        v-model="form.is_active"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Active</span>
                                </label>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="flex items-center justify-end space-x-3">
                                <Link 
                                    :href="backUrl"
                                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    Cancel
                                </Link>
                                <button 
                                    type="submit"
                                    :disabled="loading"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                                >
                                    {{ loading ? 'Updating...' : 'Update Product' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
