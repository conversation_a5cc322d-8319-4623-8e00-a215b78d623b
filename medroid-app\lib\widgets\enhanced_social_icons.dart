import 'package:flutter/material.dart';
import 'package:medroid_app/utils/enhanced_icon_helper.dart';
import 'package:medroid_app/utils/enhanced_gradient_helper.dart';
import 'package:medroid_app/utils/animation_helper.dart';

/// Enhanced social media icons with modern design and animations
class EnhancedSocialIcons extends StatelessWidget {
  final double size;
  final bool animated;
  final VoidCallback? onInstagramTap;
  final VoidCallback? onTikTokTap;
  final VoidCallback? onSocialTap;

  const EnhancedSocialIcons({
    Key? key,
    this.size = 40.0,
    this.animated = true,
    this.onInstagramTap,
    this.onTikTokTap,
    this.onSocialTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSocialIcon(
          icon: AppIcons.instagram(size: size * 0.6),
          gradient: EnhancedGradientHelper.instagram,
          onTap: onInstagramTap,
          semanticLabel: 'Instagram',
        ),
        SizedBox(width: size * 0.3),
        _buildSocialIcon(
          icon: AppIcons.tiktok(size: size * 0.6),
          gradient: EnhancedGradientHelper.tiktok,
          onTap: onTikTokTap,
          semanticLabel: 'TikTok',
        ),
        SizedBox(width: size * 0.3),
        _buildSocialIcon(
          icon: AppIcons.social(size: size * 0.6),
          gradient: EnhancedGradientHelper.primaryButton,
          onTap: onSocialTap,
          semanticLabel: 'Social Media',
        ),
      ],
    );
  }

  Widget _buildSocialIcon({
    required Widget icon,
    required LinearGradient gradient,
    VoidCallback? onTap,
    required String semanticLabel,
  }) {
    Widget iconButton = GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(size / 4),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withOpacity(0.3),
              blurRadius: size / 8,
              offset: Offset(0, size / 16),
            ),
          ],
        ),
        child: Center(child: icon),
      ),
    );

    if (animated) {
      iconButton = AnimationHelper.scaleIn(
        iconButton,
        duration: AnimationHelper.normal,
        curve: AnimationHelper.bounceCurve,
      );
    }

    return Semantics(
      label: semanticLabel,
      button: true,
      child: iconButton,
    );
  }
}

/// Individual social icon widget for more flexibility
class SocialIconButton extends StatefulWidget {
  final Widget icon;
  final LinearGradient gradient;
  final VoidCallback? onTap;
  final double size;
  final String semanticLabel;
  final bool enableHoverEffect;

  const SocialIconButton({
    Key? key,
    required this.icon,
    required this.gradient,
    this.onTap,
    this.size = 40.0,
    required this.semanticLabel,
    this.enableHoverEffect = true,
  }) : super(key: key);

  @override
  State<SocialIconButton> createState() => _SocialIconButtonState();
}

class _SocialIconButtonState extends State<SocialIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AnimationHelper.fast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (!widget.enableHoverEffect) return;
    
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: widget.semanticLabel,
      button: true,
      child: MouseRegion(
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        child: GestureDetector(
          onTap: widget.onTap,
          onTapDown: (_) => _onHover(true),
          onTapUp: (_) => _onHover(false),
          onTapCancel: () => _onHover(false),
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    gradient: widget.gradient,
                    borderRadius: BorderRadius.circular(widget.size / 4),
                    boxShadow: [
                      BoxShadow(
                        color: widget.gradient.colors.first.withOpacity(
                          _isHovered ? 0.5 : 0.3,
                        ),
                        blurRadius: widget.size / 8,
                        offset: Offset(0, widget.size / 16),
                        spreadRadius: _isHovered ? 2.0 : 0.0,
                      ),
                    ],
                  ),
                  child: Center(child: widget.icon),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

/// Floating action button with social icon
class SocialFloatingActionButton extends StatelessWidget {
  final Widget icon;
  final LinearGradient gradient;
  final VoidCallback? onPressed;
  final double size;
  final String semanticLabel;

  const SocialFloatingActionButton({
    Key? key,
    required this.icon,
    required this.gradient,
    this.onPressed,
    this.size = 56.0,
    required this.semanticLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: Colors.transparent,
      elevation: 0,
      heroTag: semanticLabel,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          gradient: gradient,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withOpacity(0.4),
              blurRadius: size / 6,
              offset: Offset(0, size / 12),
            ),
          ],
        ),
        child: Center(child: icon),
      ),
    );
  }
}