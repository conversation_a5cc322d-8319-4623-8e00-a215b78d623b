import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/file.dart';
import '../services/file_service.dart';
import '../widgets/file_upload_widget.dart';
import '../utils/app_colors.dart';

class FilePickerDialog extends StatefulWidget {
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final String? category;
  final Function(List<FileModel>) onFilesSelected;

  const FilePickerDialog({
    super.key,
    this.allowMultiple = false,
    this.allowedExtensions,
    this.category,
    required this.onFilesSelected,
  });

  @override
  State<FilePickerDialog> createState() => _FilePickerDialogState();
}

class _FilePickerDialogState extends State<FilePickerDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FileService _fileService = FileService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<FileModel> _files = [];
  Set<int> _selectedFiles = {};
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String _selectedCategory = 'all';
  String _selectedType = 'all';
  int _currentPage = 1;
  bool _hasMorePages = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadFiles();
    _scrollController.addListener(_onScroll);
    
    // Set initial category filter if provided
    if (widget.category != null) {
      _selectedCategory = widget.category!;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMorePages) {
        _loadMoreFiles();
      }
    }
  }

  Future<void> _loadFiles({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _files.clear();
        _selectedFiles.clear();
      });
    }

    setState(() {
      _isLoading = refresh || _files.isEmpty;
    });

    try {
      final response = await _fileService.getFiles(
        page: _currentPage,
        search: _searchController.text.isNotEmpty ? _searchController.text : null,
        category: _selectedCategory != 'all' ? _selectedCategory : null,
        type: _selectedType != 'all' ? _selectedType : null,
      );

      setState(() {
        if (refresh || _currentPage == 1) {
          _files = response.data;
        } else {
          _files.addAll(response.data);
        }
        _hasMorePages = response.hasMorePages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading files: $e')),
        );
      }
    }
  }

  Future<void> _loadMoreFiles() async {
    if (_isLoadingMore || !_hasMorePages) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final response = await _fileService.getFiles(
        page: _currentPage,
        search: _searchController.text.isNotEmpty ? _searchController.text : null,
        category: _selectedCategory != 'all' ? _selectedCategory : null,
        type: _selectedType != 'all' ? _selectedType : null,
      );

      setState(() {
        _files.addAll(response.data);
        _hasMorePages = response.hasMorePages;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        _currentPage--;
      });
    }
  }

  void _onSearchChanged() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchController.text == _searchController.text) {
        _loadFiles(refresh: true);
      }
    });
  }

  void _toggleFileSelection(int fileId) {
    setState(() {
      if (widget.allowMultiple) {
        if (_selectedFiles.contains(fileId)) {
          _selectedFiles.remove(fileId);
        } else {
          _selectedFiles.add(fileId);
        }
      } else {
        _selectedFiles.clear();
        _selectedFiles.add(fileId);
      }
    });
  }

  void _onFilesUploaded(List<FileModel> uploadedFiles) {
    setState(() {
      _files.insertAll(0, uploadedFiles);
      // Auto-select uploaded files
      for (final file in uploadedFiles) {
        _selectedFiles.add(file.id);
      }
    });
  }

  void _confirmSelection() {
    final selectedFileModels = _files.where((file) => _selectedFiles.contains(file.id)).toList();
    widget.onFilesSelected(selectedFileModels);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: kIsWeb ? 800 : MediaQuery.of(context).size.width * 0.9,
        height: kIsWeb ? 600 : MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
              ),
              child: Row(
                children: [
                  const Text(
                    'Select Files',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            // Tabs
            TabBar(
              controller: _tabController,
              labelColor: AppColors.primaryColor,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: AppColors.primaryColor,
              tabs: const [
                Tab(text: 'Browse Files'),
                Tab(text: 'Upload New'),
              ],
            ),
            // Tab Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildBrowseTab(),
                  _buildUploadTab(),
                ],
              ),
            ),
            // Footer
            if (_selectedFiles.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  border: Border(top: BorderSide(color: Colors.grey[300]!)),
                ),
                child: Row(
                  children: [
                    Text(
                      '${_selectedFiles.length} file(s) selected',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => setState(() => _selectedFiles.clear()),
                      child: const Text('Clear'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _confirmSelection,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Select'),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBrowseTab() {
    return Column(
      children: [
        // Search and Filters
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search files...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onChanged: (_) => _onSearchChanged(),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      decoration: InputDecoration(
                        labelText: 'Category',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('All Categories')),
                        DropdownMenuItem(value: 'product_images', child: Text('Product Images')),
                        DropdownMenuItem(value: 'profile_images', child: Text('Profile Images')),
                        DropdownMenuItem(value: 'documents', child: Text('Documents')),
                        DropdownMenuItem(value: 'videos', child: Text('Videos')),
                        DropdownMenuItem(value: 'audio', child: Text('Audio')),
                        DropdownMenuItem(value: 'general', child: Text('General')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value!;
                        });
                        _loadFiles(refresh: true);
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedType,
                      decoration: InputDecoration(
                        labelText: 'Type',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('All Types')),
                        DropdownMenuItem(value: 'images', child: Text('Images')),
                        DropdownMenuItem(value: 'documents', child: Text('Documents')),
                        DropdownMenuItem(value: 'videos', child: Text('Videos')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedType = value!;
                        });
                        _loadFiles(refresh: true);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        // Files Grid
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _files.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.folder_open, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('No files found', style: TextStyle(color: Colors.grey)),
                        ],
                      ),
                    )
                  : GridView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: kIsWeb ? 4 : 3,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                        childAspectRatio: 0.8,
                      ),
                      itemCount: _files.length + (_isLoadingMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index >= _files.length) {
                          return const Center(child: CircularProgressIndicator());
                        }
                        return _buildFileItem(_files[index]);
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildUploadTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: FileUploadWidget(
          category: widget.category,
          allowedExtensions: widget.allowedExtensions,
          allowMultiple: widget.allowMultiple,
          onFilesUploaded: _onFilesUploaded,
          buttonText: 'Upload Files',
          buttonIcon: Icons.cloud_upload,
        ),
      ),
    );
  }

  Widget _buildFileItem(FileModel file) {
    final isSelected = _selectedFiles.contains(file.id);
    
    return GestureDetector(
      onTap: () => _toggleFileSelection(file.id),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // File Preview
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                ),
                child: Stack(
                  children: [
                    if (file.isImage)
                      ClipRRect(
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                        child: Image.network(
                          file.url,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => 
                              Center(child: Text(file.fileTypeIcon, style: const TextStyle(fontSize: 32))),
                        ),
                      )
                    else
                      Center(
                        child: Text(
                          file.fileTypeIcon,
                          style: const TextStyle(fontSize: 32),
                        ),
                      ),
                    if (isSelected)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            // File Info
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    file.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    file.sizeFormatted,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
