import 'package:flutter/material.dart';
import 'package:medroid_app/utils/app_colors.dart';

/// Enhanced gradient utilities for beautiful UI components
class EnhancedGradientHelper {
  /// Medical theme gradients
  static const LinearGradient medicalPrimary = LinearGradient(
    colors: [AppColors.coralPop, AppColors.tealSurge],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient medicalSecondary = LinearGradient(
    colors: [AppColors.mintGlow, AppColors.coralPop],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient healthyGlow = LinearGradient(
    colors: [
      Color(0xFF4CAF50),
      Color(0xFF81C784),
      Color(0xFFA5D6A7),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Glassmorphism gradients
  static LinearGradient glassLight = LinearGradient(
    colors: [
      Colors.white.withOpacity(0.25),
      Colors.white.withOpacity(0.1),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static LinearGradient glassDark = LinearGradient(
    colors: [
      Colors.black.withOpacity(0.25),
      Colors.black.withOpacity(0.1),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Card gradients for different states
  static const LinearGradient cardDefault = LinearGradient(
    colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardHover = LinearGradient(
    colors: [Color(0xFFF5F5F5), Color(0xFFFAFAFA)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardSelected = LinearGradient(
    colors: [AppColors.coralPop, AppColors.mintGlow],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Button gradients
  static const LinearGradient primaryButton = LinearGradient(
    colors: [AppColors.coralPop, Color(0xFFFF6B9D)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryButton = LinearGradient(
    colors: [AppColors.mintGlow, AppColors.tealSurge],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient dangerButton = LinearGradient(
    colors: [Color(0xFFFF5252), Color(0xFFFF1744)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successButton = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF388E3C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Animated shimmer gradient
  static LinearGradient shimmerGradient = LinearGradient(
    colors: [
      Colors.grey[300]!,
      Colors.grey[100]!,
      Colors.grey[300]!,
    ],
    stops: const [0.1, 0.3, 0.4],
    begin: const Alignment(-1.0, -0.3),
    end: const Alignment(1.0, 0.3),
    tileMode: TileMode.clamp,
  );

  /// Social media brand gradients
  static const LinearGradient instagram = LinearGradient(
    colors: [
      Color(0xFFFFDC80),
      Color(0xFFFCAF45),
      Color(0xFFF77737),
      Color(0xFFF56040),
      Color(0xFFFD1D1D),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient tiktok = LinearGradient(
    colors: [Color(0xFF000000), Color(0xFF333333)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  /// Status gradients
  static const LinearGradient online = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient offline = LinearGradient(
    colors: [Color(0xFF9E9E9E), Color(0xFFBDBDBD)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient busy = LinearGradient(
    colors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Helper methods for creating custom gradients
  static LinearGradient createRadial({
    required List<Color> colors,
    Alignment center = Alignment.center,
    double radius = 0.5,
  }) {
    return LinearGradient(
      colors: colors,
      begin: center,
      end: Alignment.centerRight,
    );
  }

  static LinearGradient createCustom({
    required List<Color> colors,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      colors: colors,
      begin: begin,
      end: end,
      stops: stops,
    );
  }

  /// Gradient with opacity
  static LinearGradient withOpacity(LinearGradient gradient, double opacity) {
    return LinearGradient(
      colors:
          gradient.colors.map((color) => color.withOpacity(opacity)).toList(),
      begin: gradient.begin,
      end: gradient.end,
      stops: gradient.stops,
    );
  }

  /// Get gradient based on theme mode
  static LinearGradient forTheme(
      bool isDark, LinearGradient lightGradient, LinearGradient darkGradient) {
    return isDark ? darkGradient : lightGradient;
  }
}
