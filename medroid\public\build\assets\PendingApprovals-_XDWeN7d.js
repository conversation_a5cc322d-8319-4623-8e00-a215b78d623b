import{r as i,o as E,d as o,e as n,f as N,u as S,m as B,g as R,i as e,n as m,A as U,t as r,F as _,p as j,l as M,v as F}from"./vendor-BhKTHoN5.js";import{_ as T}from"./AppLayout.vue_vue_type_script_setup_true_lang-CoIJexfc.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./Primitive-DSQomZit.js";import"./createLucideIcon-YxmScYOV.js";const V={class:"flex items-center justify-between"},z={class:"flex mt-2","aria-label":"Breadcrumb"},I={class:"inline-flex items-center space-x-1 md:space-x-3"},L=["href"],q={key:1,class:"text-sm font-medium text-gray-700 dark:text-gray-400"},G={key:2,class:"w-3 h-3 mx-1 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},H={class:"py-12"},J={class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},K={class:"mb-6"},O={class:"border-b border-gray-200 dark:border-gray-700"},Q={class:"-mb-px flex space-x-8"},W={key:0,class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},X={class:"p-6 text-gray-900 dark:text-gray-100"},Y={key:0,class:"text-center py-8"},Z={key:1,class:"text-center py-8"},ee={key:2,class:"space-y-4"},te={class:"flex justify-between items-start"},se={class:"flex-1"},re={class:"text-lg font-medium text-gray-900 dark:text-gray-100"},ae={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},oe={class:"text-sm text-gray-700 dark:text-gray-300 mt-2"},ne={class:"flex items-center space-x-4 mt-2"},de={class:"text-sm font-medium text-green-600 dark:text-green-400"},le={class:"text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded"},ie={class:"text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300 px-2 py-1 rounded"},ce={class:"flex space-x-2 ml-4"},ue=["onClick"],ge=["onClick"],pe={key:1,class:"bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg"},xe={class:"p-6 text-gray-900 dark:text-gray-100"},ve={key:0,class:"text-center py-8"},ye={key:1,class:"text-center py-8"},me={key:2,class:"space-y-4"},he={class:"flex justify-between items-start"},be={class:"flex-1"},_e={class:"text-lg font-medium text-gray-900 dark:text-gray-100"},fe={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},ke={class:"text-sm text-gray-700 dark:text-gray-300 mt-2"},we={class:"flex items-center space-x-4 mt-2"},Ce={class:"text-sm font-medium text-green-600 dark:text-green-400"},je={class:"text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded"},$e={class:"text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300 px-2 py-1 rounded"},Pe={key:0,class:"text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300 px-2 py-1 rounded"},Ae={class:"flex space-x-2 ml-4"},Ne=["onClick"],Re=["onClick"],Ue={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},De={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Ee={class:"mt-3"},Se={class:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4"},Be={class:"text-sm text-gray-600 dark:text-gray-400 mb-4"},Me={class:"flex justify-end space-x-3 mt-4"},qe={__name:"PendingApprovals",setup(Fe){const f=[{title:"Dashboard",href:"/dashboard"},{title:"Pending Approvals",href:"/pending-approvals"}],c=i(!1),g=i([]),p=i([]),u=i("products"),x=i(""),h=i(!1),b=i(null),l=i(""),k=async()=>{c.value=!0;try{const a=await window.axios.get("/pending-products");g.value=a.data.data||[]}catch(a){console.error("Error fetching pending products:",a),g.value=[]}finally{c.value=!1}},w=async()=>{c.value=!0;try{const a=await window.axios.get("/pending-services");p.value=a.data.data||[]}catch(a){console.error("Error fetching pending services:",a),p.value=[]}finally{c.value=!1}},$=async(a,s)=>{try{const d=s==="product"?`/approve-product/${a}`:`/approve-service/${a}`;await window.axios.post(d),s==="product"?await k():await w(),alert(`${s.charAt(0).toUpperCase()+s.slice(1)} approved successfully!`)}catch(d){console.error(`Error approving ${s}:`,d),alert(`Failed to approve ${s}. Please try again.`)}},P=(a,s)=>{b.value=a,l.value=s,x.value="",h.value=!0},D=async()=>{if(!x.value.trim()){alert("Please provide a rejection reason.");return}try{const a=l.value==="product"?`/reject-product/${b.value.id}`:`/reject-service/${b.value.id}`;await window.axios.post(a,{rejection_reason:x.value}),l.value==="product"?await k():await w(),h.value=!1,alert(`${l.value.charAt(0).toUpperCase()+l.value.slice(1)} rejected successfully!`)}catch(a){console.error(`Error rejecting ${l.value}:`,a),alert(`Failed to reject ${l.value}. Please try again.`)}},A=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return E(()=>{k(),w()}),(a,s)=>(n(),o(_,null,[N(S(B),{title:"Pending Approvals"}),N(T,null,{header:R(()=>[e("div",V,[e("div",null,[s[5]||(s[5]=e("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Pending Approvals ",-1)),e("nav",z,[e("ol",I,[(n(),o(_,null,j(f,(d,t)=>e("li",{key:t,class:"inline-flex items-center"},[t<f.length-1?(n(),o("a",{key:0,href:d.href,class:"text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"},r(d.title),9,L)):(n(),o("span",q,r(d.title),1)),t<f.length-1?(n(),o("svg",G,s[4]||(s[4]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):m("",!0)])),64))])])])])]),default:R(()=>{var d;return[e("div",H,[e("div",J,[e("div",K,[e("div",O,[e("nav",Q,[e("button",{onClick:s[0]||(s[0]=t=>u.value="products"),class:U([u.value==="products"?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"])}," Products ("+r(g.value.length)+") ",3),e("button",{onClick:s[1]||(s[1]=t=>u.value="services"),class:U([u.value==="services"?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"])}," Services ("+r(p.value.length)+") ",3)])])]),u.value==="products"?(n(),o("div",W,[e("div",X,[c.value?(n(),o("div",Y,s[6]||(s[6]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):g.value.length===0?(n(),o("div",Z,s[7]||(s[7]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"No pending products found.",-1)]))):(n(),o("div",ee,[(n(!0),o(_,null,j(g.value,t=>{var v,y;return n(),o("div",{key:t.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("div",te,[e("div",se,[e("h3",re,r(t.name),1),e("p",ae," by "+r(((v=t.user)==null?void 0:v.name)||"Unknown")+" • "+r(A(t.created_at)),1),e("p",oe,r(t.short_description||t.description),1),e("div",ne,[e("span",de," $"+r(t.price),1),e("span",le,r(t.type),1),e("span",ie,r(((y=t.category)==null?void 0:y.name)||"No Category"),1)])]),e("div",ce,[e("button",{onClick:C=>$(t.id,"product"),class:"bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm font-medium"}," Approve ",8,ue),e("button",{onClick:C=>P(t,"product"),class:"bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm font-medium"}," Reject ",8,ge)])])])}),128))]))])])):m("",!0),u.value==="services"?(n(),o("div",pe,[e("div",xe,[c.value?(n(),o("div",ve,s[8]||(s[8]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"},null,-1)]))):p.value.length===0?(n(),o("div",ye,s[9]||(s[9]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"No pending services found.",-1)]))):(n(),o("div",me,[(n(!0),o(_,null,j(p.value,t=>{var v,y;return n(),o("div",{key:t.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("div",he,[e("div",be,[e("h3",_e,r(t.name),1),e("p",fe," by "+r(((y=(v=t.provider)==null?void 0:v.user)==null?void 0:y.name)||"Unknown")+" • "+r(A(t.created_at)),1),e("p",ke,r(t.description),1),e("div",we,[e("span",Ce," $"+r(t.price),1),e("span",je,r(t.duration)+" min ",1),e("span",$e,r(t.category||"No Category"),1),t.is_telemedicine?(n(),o("span",Pe," Telemedicine ")):m("",!0)])]),e("div",Ae,[e("button",{onClick:C=>$(t.id,"service"),class:"bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm font-medium"}," Approve ",8,Ne),e("button",{onClick:C=>P(t,"service"),class:"bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm font-medium"}," Reject ",8,Re)])])])}),128))]))])])):m("",!0)])]),h.value?(n(),o("div",Ue,[e("div",De,[e("div",Ee,[e("h3",Se," Reject "+r(l.value.charAt(0).toUpperCase()+l.value.slice(1)),1),e("p",Be,' Please provide a reason for rejecting "'+r((d=b.value)==null?void 0:d.name)+'": ',1),M(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=t=>x.value=t),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Enter rejection reason..."},null,512),[[F,x.value]]),e("div",Me,[e("button",{onClick:s[3]||(s[3]=t=>h.value=!1),class:"px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"}," Cancel "),e("button",{onClick:D,class:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"}," Reject ")])])])])):m("",!0)]}),_:1})],64))}};export{qe as default};
