<script setup>
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'

const props = defineProps({
    product: Object,
    relatedProducts: Array,
})

const breadcrumbs = computed(() => [
    { title: 'Shop', href: '/shop' },
    { title: props.product?.category?.name || 'Products', href: `/shop?category=${props.product?.category?.id}` },
    { title: props.product?.name || 'Product', href: '#' },
])

// Reactive data
const quantity = ref(1)
const selectedImage = ref(0)
const addingToCart = ref(false)
const activeTab = ref('description')
const showImageModal = ref(false)
const selectedReview = ref(null)

// Reviews data - loaded from backend
const reviews = ref([])

const averageRating = computed(() => {
    if (!reviews.value || !Array.isArray(reviews.value) || reviews.value.length === 0) return 0
    const sum = reviews.value.reduce((acc, review) => acc + review.rating, 0)
    return (sum / reviews.value.length).toFixed(1)
})

const ratingDistribution = computed(() => {
    const distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
    if (reviews.value && Array.isArray(reviews.value)) {
        reviews.value.forEach(review => {
            distribution[review.rating]++
        })
    }
    return distribution
})

// Computed properties
const currentImage = computed(() => {
    if (props.product?.images?.length > 0) {
        const imageObj = props.product.images[selectedImage.value]
        if (imageObj?.full_url) {
            return imageObj.full_url
        }
        if (imageObj?.image_path) {
            return imageObj.image_path.startsWith('http') ? imageObj.image_path : `/storage/${imageObj.image_path}`
        }
    }

    if (props.product?.primary_image) {
        return props.product.primary_image
    }

    // Use a default product image that exists
    return '/images/default-product.png'
})

const totalPrice = computed(() => {
    return (props.product?.effective_price || 0) * quantity.value
})

const formattedTotalPrice = computed(() => {
    return '£' + totalPrice.value.toFixed(2)
})

// Methods
const addToCart = async () => {
    if (!props.product?.can_purchase) {
        alert('Product is not available for purchase')
        return
    }

    addingToCart.value = true
    try {
        const response = await axios.post('/shop/cart/add', {
            product_id: props.product.id,
            quantity: quantity.value
        })
        
        if (response.data.success) {
            alert('Product added to cart!')
            // Optionally redirect to cart or show success message
        } else {
            alert(response.data.message || 'Failed to add product to cart')
        }
    } catch (error) {
        console.error('Error adding to cart:', error)
        alert('Failed to add product to cart')
    } finally {
        addingToCart.value = false
    }
}

const increaseQuantity = () => {
    if (props.product?.type === 'physical' && props.product?.manage_stock) {
        if (quantity.value < props.product.stock_quantity) {
            quantity.value++
        }
    } else {
        quantity.value++
    }
}

const decreaseQuantity = () => {
    if (quantity.value > 1) {
        quantity.value--
    }
}

// Fetch reviews from backend
const fetchReviews = async () => {
    if (!props.product?.id) return

    try {
        const response = await axios.get(`/shop/products/${props.product.id}/reviews`)
        reviews.value = response.data.reviews || []
    } catch (error) {
        console.error('Error fetching reviews:', error)
        reviews.value = []
    }
}

// Load reviews when component mounts
onMounted(() => {
    fetchReviews()
})
</script>

<template>
    <Head :title="`${product?.name || 'Product'} - Medroid Shop`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="py-8 bg-gray-50 min-h-screen">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Main Product Section -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 p-6">
                        <!-- Product Images - Left Side -->
                        <div class="lg:col-span-5 space-y-4">
                            <!-- Main Image -->
                            <div class="relative bg-white rounded-lg border border-gray-200 overflow-hidden group aspect-square">
                                <img
                                    :src="currentImage"
                                    :alt="product?.name"
                                    class="w-full h-full object-contain object-center cursor-zoom-in transition-transform duration-300 group-hover:scale-105"
                                    @click="showImageModal = true"
                                />
                                <!-- Sale Badge -->
                                <div v-if="product?.is_on_sale" class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-red-600 text-white text-sm font-bold px-3 py-2 rounded-full shadow-lg">
                                    -{{ product.discount_percentage }}%
                                </div>
                                <!-- Digital Badge -->
                                <div v-if="product?.type === 'digital'" class="absolute top-4 right-4">
                                    <span class="bg-gradient-to-r from-purple-500 to-purple-600 text-white text-sm font-semibold px-3 py-2 rounded-full shadow-lg">
                                        <i class="fas fa-download mr-1"></i>
                                        Digital
                                    </span>
                                </div>
                                <!-- Zoom Icon -->
                                <div class="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-search-plus"></i>
                                </div>
                            </div>

                            <!-- Thumbnail Images -->
                            <div v-if="product?.images?.length > 1" class="flex space-x-3 overflow-x-auto pb-2">
                                <button
                                    v-for="(image, index) in product.images"
                                    :key="index"
                                    @click="selectedImage = index"
                                    :class="[
                                        'flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200',
                                        selectedImage === index
                                            ? 'border-teal-500 ring-2 ring-teal-200 shadow-md'
                                            : 'border-gray-200 hover:border-teal-300'
                                    ]"
                                >
                                    <img
                                        :src="image.full_url || image.image_path || '/images/default-product.png'"
                                        :alt="image.alt_text || product.name"
                                        class="w-full h-full object-cover"
                                    />
                                </button>
                            </div>
                        </div>

                        <!-- Product Info - Right Side -->
                        <div class="lg:col-span-7 space-y-6">
                            <!-- Category and Brand -->
                            <div class="space-y-2">
                                <Link
                                    :href="`/shop?category=${product?.category?.id}`"
                                    class="text-teal-600 hover:text-teal-700 text-sm font-medium hover:underline"
                                >
                                    {{ product?.category?.name }}
                                </Link>
                                <h1 class="text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">{{ product?.name }}</h1>
                            </div>

                            <!-- Reviews and Rating -->
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-1">
                                    <div class="flex items-center">
                                        <span v-for="i in 5" :key="i" class="text-lg">
                                            <i :class="i <= Math.floor(averageRating) ? 'fas fa-star text-yellow-400' : 'far fa-star text-gray-300'"></i>
                                        </span>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700">{{ averageRating }}</span>
                                </div>
                                <span class="text-sm text-teal-600 hover:text-teal-700 cursor-pointer hover:underline">
                                    {{ reviews.length }} customer reviews
                                </span>
                                <span class="text-gray-400">|</span>
                                <span class="text-sm text-gray-500">SKU: {{ product?.sku }}</span>
                            </div>

                            <!-- Price Section -->
                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div class="flex items-baseline space-x-3 mb-2">
                                    <span class="text-3xl font-bold text-gray-900">{{ product?.formatted_price }}</span>
                                    <span v-if="product?.is_on_sale" class="text-xl text-gray-500 line-through">{{ product?.formatted_original_price }}</span>
                                    <span v-if="product?.is_on_sale" class="text-sm font-semibold text-red-600 bg-red-100 px-2 py-1 rounded">
                                        Save {{ product.discount_percentage }}%
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <span v-if="product?.type === 'physical'">+ shipping costs</span>
                                    <span v-else class="text-green-600 font-medium">
                                        <i class="fas fa-download mr-1"></i>
                                        Instant digital download
                                    </span>
                                </div>
                            </div>

                            <!-- Stock Status -->
                            <div v-if="product?.type === 'physical'" class="flex items-center space-x-4">
                                <div v-if="product?.stock_quantity > 0" class="flex items-center text-green-600">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <span class="font-semibold">In Stock</span>
                                    <span v-if="product.stock_quantity <= 5" class="ml-2 text-orange-600 font-medium">
                                        (Only {{ product.stock_quantity }} left!)
                                    </span>
                                </div>
                                <div v-else class="flex items-center text-red-600">
                                    <i class="fas fa-times-circle mr-2"></i>
                                    <span class="font-semibold">Out of Stock</span>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <i class="fas fa-truck mr-1"></i>
                                    Free delivery on orders over £50
                                </div>
                            </div>
                            <div v-else class="flex items-center text-green-600">
                                <i class="fas fa-download mr-2"></i>
                                <span class="font-semibold">Available for instant download</span>
                            </div>

                                <!-- Description -->
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                                    <p class="text-gray-600 leading-relaxed">{{ product?.description }}</p>
                                </div>

                                <!-- Digital Product Info -->
                                <div v-if="product?.type === 'digital'" class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                    <h4 class="font-semibold text-purple-900 mb-2">Digital Product Details</h4>
                                    <ul class="text-purple-800 text-sm space-y-1">
                                        <li v-if="product.download_limit">• Download limit: {{ product.download_limit }} times</li>
                                        <li v-if="product.download_expiry_days">• Access expires: {{ product.download_expiry_days }} days after purchase</li>
                                        <li>• Instant download after payment</li>
                                        <li>• No shipping required</li>
                                    </ul>
                                </div>

                            <!-- Quantity and Purchase Section -->
                            <div class="bg-white border border-gray-200 rounded-lg p-6 space-y-6">
                                <!-- Quantity Selector -->
                                <div v-if="product?.type === 'physical'" class="space-y-3">
                                    <label class="text-sm font-semibold text-gray-700">Quantity:</label>
                                    <div class="flex items-center space-x-4">
                                        <div class="flex items-center border border-gray-300 rounded-lg">
                                            <button
                                                @click="decreaseQuantity"
                                                :disabled="quantity <= 1"
                                                class="w-12 h-12 flex items-center justify-center text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed rounded-l-lg transition-colors"
                                            >
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <span class="w-16 h-12 flex items-center justify-center font-semibold text-lg border-x border-gray-300">{{ quantity }}</span>
                                            <button
                                                @click="increaseQuantity"
                                                :disabled="product?.manage_stock && quantity >= product?.stock_quantity"
                                                class="w-12 h-12 flex items-center justify-center text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed rounded-r-lg transition-colors"
                                            >
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <div class="text-lg font-bold text-gray-900">
                                            Total: {{ formattedTotalPrice }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="space-y-3">
                                    <button
                                        @click="addToCart"
                                        :disabled="!product?.can_purchase || addingToCart"
                                        :class="[
                                            'w-full py-4 px-6 rounded-lg font-bold text-lg transition-all duration-200 transform',
                                            product?.can_purchase && !addingToCart
                                                ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 hover:scale-105 shadow-lg hover:shadow-xl'
                                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        ]"
                                    >
                                        <span v-if="addingToCart" class="flex items-center justify-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Adding to Cart...
                                        </span>
                                        <span v-else-if="product?.can_purchase">
                                            <i class="fas fa-shopping-cart mr-2"></i>
                                            Add to Cart
                                        </span>
                                        <span v-else>
                                            <i class="fas fa-times mr-2"></i>
                                            Out of Stock
                                        </span>
                                    </button>

                                    <Link
                                        href="/shop/cart"
                                        class="w-full py-3 px-4 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-semibold text-center block"
                                    >
                                        <i class="fas fa-shopping-bag mr-2"></i>
                                        View Cart
                                    </Link>
                                </div>

                                <!-- Trust Badges -->
                                <div class="border-t border-gray-200 pt-4">
                                    <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <i class="fas fa-shield-alt text-green-500 mr-2"></i>
                                            Secure Payment
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-undo text-blue-500 mr-2"></i>
                                            30-Day Returns
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-shipping-fast text-purple-500 mr-2"></i>
                                            Fast Shipping
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-headset text-teal-500 mr-2"></i>
                                            24/7 Support
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Details Tabs -->
                <div class="mt-8 bg-white rounded-xl shadow-lg overflow-hidden">
                    <!-- Tab Navigation -->
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button
                                @click="activeTab = 'description'"
                                :class="[
                                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                                    activeTab === 'description'
                                        ? 'border-teal-500 text-teal-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                ]"
                            >
                                Description
                            </button>
                            <button
                                @click="activeTab = 'specifications'"
                                :class="[
                                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                                    activeTab === 'specifications'
                                        ? 'border-teal-500 text-teal-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                ]"
                            >
                                Specifications
                            </button>
                            <button
                                @click="activeTab = 'reviews'"
                                :class="[
                                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                                    activeTab === 'reviews'
                                        ? 'border-teal-500 text-teal-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                ]"
                            >
                                Reviews ({{ reviews.length }})
                            </button>
                            <button
                                v-if="product?.type === 'digital'"
                                @click="activeTab = 'digital'"
                                :class="[
                                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                                    activeTab === 'digital'
                                        ? 'border-teal-500 text-teal-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                ]"
                            >
                                Digital Details
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- Description Tab -->
                        <div v-if="activeTab === 'description'" class="prose max-w-none">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Product Description</h3>
                            <div class="text-gray-700 leading-relaxed whitespace-pre-line">
                                {{ product?.description || 'No description available.' }}
                            </div>
                            <div v-if="product?.short_description" class="mt-6 p-4 bg-teal-50 border border-teal-200 rounded-lg">
                                <h4 class="font-semibold text-teal-900 mb-2">Key Features</h4>
                                <p class="text-teal-800">{{ product.short_description }}</p>
                            </div>
                        </div>

                        <!-- Specifications Tab -->
                        <div v-if="activeTab === 'specifications'" class="space-y-6">
                            <h3 class="text-xl font-bold text-gray-900">Product Specifications</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-3">
                                    <div class="flex justify-between py-2 border-b border-gray-200">
                                        <span class="font-medium text-gray-700">SKU</span>
                                        <span class="text-gray-900">{{ product?.sku }}</span>
                                    </div>
                                    <div class="flex justify-between py-2 border-b border-gray-200">
                                        <span class="font-medium text-gray-700">Type</span>
                                        <span class="text-gray-900 capitalize">{{ product?.type }}</span>
                                    </div>
                                    <div class="flex justify-between py-2 border-b border-gray-200">
                                        <span class="font-medium text-gray-700">Category</span>
                                        <span class="text-gray-900">{{ product?.category?.name }}</span>
                                    </div>
                                    <div v-if="product?.weight" class="flex justify-between py-2 border-b border-gray-200">
                                        <span class="font-medium text-gray-700">Weight</span>
                                        <span class="text-gray-900">{{ product.weight }}kg</span>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div v-if="product?.type === 'physical'" class="flex justify-between py-2 border-b border-gray-200">
                                        <span class="font-medium text-gray-700">Stock Status</span>
                                        <span :class="product?.stock_quantity > 0 ? 'text-green-600' : 'text-red-600'" class="font-medium">
                                            {{ product?.stock_quantity > 0 ? 'In Stock' : 'Out of Stock' }}
                                        </span>
                                    </div>
                                    <div v-if="product?.dimensions" class="flex justify-between py-2 border-b border-gray-200">
                                        <span class="font-medium text-gray-700">Dimensions</span>
                                        <span class="text-gray-900">{{ product.dimensions }}</span>
                                    </div>
                                    <div class="flex justify-between py-2 border-b border-gray-200">
                                        <span class="font-medium text-gray-700">Availability</span>
                                        <span class="text-green-600 font-medium">Available</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reviews Tab -->
                        <div v-if="activeTab === 'reviews'" class="space-y-6">
                            <!-- Reviews Summary -->
                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                                <div class="lg:col-span-1">
                                    <div class="text-center p-6 bg-gray-50 rounded-lg">
                                        <div class="text-4xl font-bold text-gray-900 mb-2">{{ averageRating }}</div>
                                        <div class="flex justify-center items-center mb-2">
                                            <span v-for="i in 5" :key="i" class="text-xl">
                                                <i :class="i <= Math.floor(averageRating) ? 'fas fa-star text-yellow-400' : 'far fa-star text-gray-300'"></i>
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-600">Based on {{ reviews.length }} reviews</div>
                                    </div>

                                    <!-- Rating Distribution -->
                                    <div class="mt-6 space-y-2">
                                        <div v-for="rating in [5, 4, 3, 2, 1]" :key="rating" class="flex items-center space-x-2">
                                            <span class="text-sm font-medium text-gray-700 w-8">{{ rating }}</span>
                                            <i class="fas fa-star text-yellow-400 text-sm"></i>
                                            <div class="flex-1 bg-gray-200 rounded-full h-2">
                                                <div
                                                    class="bg-yellow-400 h-2 rounded-full"
                                                    :style="{ width: `${reviews.length > 0 ? (ratingDistribution[rating] / reviews.length) * 100 : 0}%` }"
                                                ></div>
                                            </div>
                                            <span class="text-sm text-gray-600 w-8">{{ ratingDistribution[rating] }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Individual Reviews -->
                                <div class="lg:col-span-2 space-y-6">
                                    <div v-for="(review, index) in reviews" :key="review?.id || index" class="border border-gray-200 rounded-lg p-6">
                                        <div class="flex items-start justify-between mb-3">
                                            <div>
                                                <div class="flex items-center space-x-2 mb-1">
                                                    <span class="font-semibold text-gray-900">{{ review?.user || 'Anonymous' }}</span>
                                                    <span v-if="review?.verified" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                                        Verified Purchase
                                                    </span>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <div class="flex">
                                                        <span v-for="i in 5" :key="i" class="text-sm">
                                                            <i :class="i <= (review?.rating || 0) ? 'fas fa-star text-yellow-400' : 'far fa-star text-gray-300'"></i>
                                                        </span>
                                                    </div>
                                                    <span class="text-sm text-gray-500">{{ review?.date || 'Date not available' }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <h4 v-if="review?.title" class="font-semibold text-gray-900 mb-2">{{ review.title }}</h4>
                                        <p class="text-gray-700">{{ review?.comment || 'No comment provided' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Digital Details Tab -->
                        <div v-if="activeTab === 'digital' && product?.type === 'digital'" class="space-y-6">
                            <h3 class="text-xl font-bold text-gray-900">Digital Product Information</h3>
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="space-y-4">
                                        <div class="flex items-center text-purple-800">
                                            <i class="fas fa-download mr-3 text-lg"></i>
                                            <span class="font-semibold">Instant Download</span>
                                        </div>
                                        <div v-if="product.download_limit" class="flex items-center text-purple-800">
                                            <i class="fas fa-sync-alt mr-3 text-lg"></i>
                                            <span>Download limit: {{ product.download_limit }} times</span>
                                        </div>
                                        <div v-if="product.download_expiry_days" class="flex items-center text-purple-800">
                                            <i class="fas fa-calendar-alt mr-3 text-lg"></i>
                                            <span>Access expires: {{ product.download_expiry_days }} days after purchase</span>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <div class="flex items-center text-purple-800">
                                            <i class="fas fa-shield-alt mr-3 text-lg"></i>
                                            <span>Secure download link</span>
                                        </div>
                                        <div class="flex items-center text-purple-800">
                                            <i class="fas fa-mobile-alt mr-3 text-lg"></i>
                                            <span>Compatible with all devices</span>
                                        </div>
                                        <div class="flex items-center text-purple-800">
                                            <i class="fas fa-headset mr-3 text-lg"></i>
                                            <span>24/7 download support</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Products -->
                <div v-if="relatedProducts?.length > 0" class="mt-8">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-2xl font-bold text-gray-900">You might also like</h2>
                                <Link href="/shop" class="text-teal-600 hover:text-teal-700 font-medium hover:underline">
                                    View all products
                                </Link>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <Link
                                    v-for="relatedProduct in relatedProducts"
                                    :key="relatedProduct.id"
                                    :href="`/shop/products/${relatedProduct.slug}`"
                                    class="group"
                                >
                                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                        <div class="relative overflow-hidden bg-gray-100">
                                            <img
                                                :src="relatedProduct.primary_image || '/images/placeholder-product.jpg'"
                                                :alt="relatedProduct.name"
                                                class="h-48 w-full object-cover object-center group-hover:scale-110 transition-transform duration-300"
                                            />
                                            <div v-if="relatedProduct.is_on_sale" class="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                                -{{ relatedProduct.discount_percentage }}%
                                            </div>
                                            <div v-if="relatedProduct.type === 'digital'" class="absolute top-2 right-2">
                                                <span class="bg-purple-500 text-white text-xs font-semibold px-2 py-1 rounded-full">
                                                    Digital
                                                </span>
                                            </div>
                                        </div>
                                        <div class="p-4">
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-teal-600 transition-colors">
                                                {{ relatedProduct.name }}
                                            </h3>
                                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ relatedProduct.short_description }}</p>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-lg font-bold text-gray-900">{{ relatedProduct.formatted_price }}</span>
                                                    <span v-if="relatedProduct.is_on_sale" class="text-sm text-gray-500 line-through">
                                                        {{ relatedProduct.formatted_original_price }}
                                                    </span>
                                                </div>
                                                <button class="bg-teal-500 text-white p-2 rounded-full hover:bg-teal-600 transition-colors opacity-0 group-hover:opacity-100">
                                                    <i class="fas fa-shopping-cart text-sm"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Modal -->
                <div v-if="showImageModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75" @click="showImageModal = false">
                    <div class="max-w-4xl max-h-full p-4">
                        <img
                            :src="currentImage"
                            :alt="product?.name"
                            class="max-w-full max-h-full object-contain rounded-lg"
                            @click.stop
                        />
                        <button
                            @click="showImageModal = false"
                            class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-colors"
                        >
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
