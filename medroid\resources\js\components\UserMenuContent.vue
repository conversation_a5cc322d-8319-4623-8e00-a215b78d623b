<script setup lang="ts">
import UserInfo from '@/components/UserInfo.vue';
import { DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import type { User } from '@/types';
import { Link } from '@inertiajs/vue3';
import { LogOut, Settings, DollarSign } from 'lucide-vue-next';
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useLogout } from '@/composables/useLogout';

interface Props {
    user: User;
}

const creditBalance = ref({
    balance: 0,
    total_earned: 0,
    total_used: 0,
    referral_earnings: 0,
    admin_credits: 0
});

const loadCreditBalance = async () => {
    try {
        const response = await axios.get('/credits-balance');
        creditBalance.value = response.data;
    } catch (error) {
        console.error('Error loading credit balance:', error);
        creditBalance.value = {
            balance: 0,
            total_earned: 0,
            total_used: 0,
            referral_earnings: 0,
            admin_credits: 0
        };
    }
};

const { logout } = useLogout();

const handleLogout = () => {
    // Clear cached data
    creditBalance.value = {
        balance: 0,
        total_earned: 0,
        total_used: 0,
        referral_earnings: 0,
        admin_credits: 0
    };

    // Use the logout composable
    logout();
};

onMounted(() => {
    loadCreditBalance();
});

defineProps<Props>();
</script>

<template>
    <DropdownMenuLabel class="p-0 font-normal">
        <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <UserInfo :user="user" :show-email="true" />
        </div>
    </DropdownMenuLabel>
    <DropdownMenuSeparator />

    <!-- Credit Balance -->
    <DropdownMenuLabel class="px-2 py-1.5">
        <div class="flex items-center justify-between text-xs">
            <div class="flex items-center">
                <DollarSign class="mr-1 h-3 w-3 text-green-600" />
                <span class="text-muted-foreground">Credits:</span>
            </div>
            <span class="font-semibold text-green-600">£{{ parseFloat(creditBalance.balance || 0).toFixed(2) }}</span>
        </div>
    </DropdownMenuLabel>
    <DropdownMenuSeparator />

    <DropdownMenuGroup>
        <DropdownMenuItem :as-child="true">
            <Link class="block w-full" :href="route('profile.edit')" prefetch as="button">
                <Settings class="mr-2 h-4 w-4" />
                Settings
            </Link>
        </DropdownMenuItem>
    </DropdownMenuGroup>
    <DropdownMenuSeparator />
    <DropdownMenuItem as="button" @click="handleLogout" class="w-full">
        <LogOut class="mr-2 h-4 w-4" />
        Log out
    </DropdownMenuItem>
</template>
