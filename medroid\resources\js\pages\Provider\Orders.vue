<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, usePage } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';

// Get user from page props
const page = usePage();
const user = computed(() => page.props.auth?.user);

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'My Orders', href: '/provider/orders' },
];

const loading = ref(false);
const orders = ref([]);
const stats = ref({});
const searchQuery = ref('');
const selectedStatus = ref('all');
const selectedPaymentStatus = ref('all');
const showDispatchModal = ref(false);
const selectedOrder = ref(null);
const shippingCompanies = ref([]);
const dispatchForm = ref({
    tracking_number: '',
    shipping_company: ''
});

const fetchOrders = async () => {
    loading.value = true;
    try {
        const params = new URLSearchParams();
        if (searchQuery.value) params.append('search', searchQuery.value);
        if (selectedStatus.value !== 'all') params.append('status', selectedStatus.value);
        if (selectedPaymentStatus.value !== 'all') params.append('payment_status', selectedPaymentStatus.value);

        const response = await window.axios.get(`/provider/orders?${params.toString()}`);
        orders.value = response.data.orders.data;
        stats.value = response.data.stats;
    } catch (error) {
        console.error('Error fetching orders:', error);
    } finally {
        loading.value = false;
    }
};

const fetchShippingCompanies = async () => {
    try {
        const response = await window.axios.get('/provider/shipping-companies');
        shippingCompanies.value = response.data.companies;
    } catch (error) {
        console.error('Error fetching shipping companies:', error);
    }
};

const openDispatchModal = (order) => {
    selectedOrder.value = order;
    showDispatchModal.value = true;
    dispatchForm.value = {
        tracking_number: '',
        shipping_company: ''
    };
};

const closeDispatchModal = () => {
    showDispatchModal.value = false;
    selectedOrder.value = null;
    dispatchForm.value = {
        tracking_number: '',
        shipping_company: ''
    };
};

const dispatchOrder = async () => {
    if (!selectedOrder.value) return;
    
    try {
        const response = await window.axios.post(`/provider/orders/${selectedOrder.value.id}/dispatch`, dispatchForm.value);
        
        // Update the order in the list
        const orderIndex = orders.value.findIndex(o => o.id === selectedOrder.value.id);
        if (orderIndex !== -1) {
            orders.value[orderIndex] = response.data.order;
        }
        
        closeDispatchModal();
        alert('Order dispatched successfully!');
        fetchOrders(); // Refresh the list
    } catch (error) {
        console.error('Error dispatching order:', error);
        if (error.response?.data?.message) {
            alert(`Error: ${error.response.data.message}`);
        } else {
            alert('Failed to dispatch order. Please try again.');
        }
    }
};

const getStatusBadgeClass = (status) => {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        'processing': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        'shipped': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
        'delivered': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'refunded': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const getPaymentStatusBadgeClass = (status) => {
    const classes = {
        'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        'paid': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'failed': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'refunded': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString();
};

const canDispatch = (order) => {
    return ['pending', 'processing'].includes(order.status) && order.payment_status === 'paid';
};

onMounted(() => {
    fetchOrders();
    fetchShippingCompanies();
});
</script>

<template>
    <Head title="My Orders" />

    <AppLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Order Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" :href="breadcrumb.href" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Orders</div>
                        <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ stats.total_orders || 0 }}</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</div>
                        <div class="text-2xl font-bold text-yellow-600">{{ stats.pending_orders || 0 }}</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Processing</div>
                        <div class="text-2xl font-bold text-blue-600">{{ stats.processing_orders || 0 }}</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Shipped</div>
                        <div class="text-2xl font-bold text-green-600">{{ stats.shipped_orders || 0 }}</div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <input 
                                    v-model="searchQuery"
                                    @input="fetchOrders"
                                    type="text" 
                                    placeholder="Search orders..."
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                            </div>
                            <div>
                                <select 
                                    v-model="selectedStatus"
                                    @change="fetchOrders"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="all">All Statuses</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div>
                                <select 
                                    v-model="selectedPaymentStatus"
                                    @change="fetchOrders"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="all">All Payment Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="paid">Paid</option>
                                    <option value="failed">Failed</option>
                                    <option value="refunded">Refunded</option>
                                </select>
                            </div>
                            <div>
                                <button 
                                    @click="fetchOrders"
                                    class="w-full bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                                >
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="orders.length === 0" class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No orders found.</p>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Order
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Customer
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Payment
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Total
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="order in orders" :key="order.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ order.order_number }}
                                            </div>
                                            <div v-if="order.tracking_number" class="text-sm text-gray-500 dark:text-gray-400">
                                                Tracking: {{ order.tracking_number }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">{{ order.user?.name || 'N/A' }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ order.user?.email || 'N/A' }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(order.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ order.status_label }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getPaymentStatusBadgeClass(order.payment_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ order.payment_status_label }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ order.formatted_total }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ formatDate(order.created_at) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <Link :href="`/provider/orders/${order.id}`" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                                View
                                            </Link>
                                            <button 
                                                v-if="canDispatch(order)" 
                                                @click="openDispatchModal(order)"
                                                class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                            >
                                                Dispatch
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dispatch Modal -->
        <div v-if="showDispatchModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-50" @click="closeDispatchModal">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Dispatch Order {{ selectedOrder?.order_number }}
                    </h3>
                    <form @submit.prevent="dispatchOrder" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Shipping Company</label>
                            <select 
                                v-model="dispatchForm.shipping_company"
                                required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                                <option value="">Select shipping company</option>
                                <option v-for="company in shippingCompanies" :key="company" :value="company">
                                    {{ company }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tracking Number</label>
                            <input 
                                v-model="dispatchForm.tracking_number"
                                type="text" 
                                required
                                placeholder="Enter tracking number"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                            >
                        </div>
                        <div class="flex space-x-3 pt-4">
                            <button 
                                type="submit"
                                class="flex-1 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Dispatch Order
                            </button>
                            <button 
                                type="button"
                                @click="closeDispatchModal"
                                class="flex-1 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
