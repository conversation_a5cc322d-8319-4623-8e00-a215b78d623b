<?php

/**
 * Test Session Configuration Script
 * 
 * This script tests if the session configuration is working properly
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== TESTING SESSION CONFIGURATION ===\n\n";

// Test 1: Check environment variables
echo "1. ENVIRONMENT VARIABLES\n";
echo "========================\n";

$envVars = [
    'APP_URL',
    'SESSION_DRIVER',
    'SESSION_DOMAIN',
    'SESSION_SECURE_COOKIE',
    'SESSION_SAME_SITE',
    'SESSION_LIFETIME',
];

foreach ($envVars as $var) {
    $value = env($var, 'NOT SET');
    echo "$var: $value\n";
}

// Test 2: Check session configuration
echo "\n2. SESSION CONFIGURATION\n";
echo "=========================\n";

$sessionConfig = config('session');
echo "Driver: " . $sessionConfig['driver'] . "\n";
echo "Domain: " . ($sessionConfig['domain'] ?? 'null') . "\n";
echo "Secure: " . ($sessionConfig['secure'] ? 'true' : 'false') . "\n";
echo "Same Site: " . $sessionConfig['same_site'] . "\n";
echo "HTTP Only: " . ($sessionConfig['http_only'] ? 'true' : 'false') . "\n";
echo "Lifetime: " . $sessionConfig['lifetime'] . " minutes\n";

// Test 3: Check if session files directory exists (if using file driver)
if ($sessionConfig['driver'] === 'file') {
    echo "\n3. SESSION FILES DIRECTORY\n";
    echo "===========================\n";
    
    $sessionPath = $sessionConfig['files'];
    echo "Path: $sessionPath\n";
    echo "Exists: " . (is_dir($sessionPath) ? 'Yes' : 'No') . "\n";
    echo "Writable: " . (is_writable($sessionPath) ? 'Yes' : 'No') . "\n";
    
    if (is_dir($sessionPath)) {
        $files = glob($sessionPath . '/*');
        echo "Session files count: " . count($files) . "\n";
    }
}

// Test 4: Check CSRF configuration
echo "\n4. CSRF CONFIGURATION\n";
echo "======================\n";

$csrfMiddleware = new \App\Http\Middleware\VerifyCsrfToken();
$reflection = new ReflectionClass($csrfMiddleware);
$exceptProperty = $reflection->getProperty('except');
$exceptProperty->setAccessible(true);
$exceptions = $exceptProperty->getValue($csrfMiddleware);

echo "CSRF Exceptions:\n";
foreach ($exceptions as $exception) {
    echo "- $exception\n";
}

// Test 5: Check if app key is set
echo "\n5. APPLICATION KEY\n";
echo "===================\n";

$appKey = config('app.key');
if ($appKey) {
    echo "App key is set ✓\n";
} else {
    echo "App key is NOT set ✗\n";
    echo "Run: php artisan key:generate\n";
}

// Test 6: Check URL configuration
echo "\n6. URL CONFIGURATION\n";
echo "====================\n";

$appUrl = config('app.url');
echo "App URL: $appUrl\n";

$parsedUrl = parse_url($appUrl);
echo "Scheme: " . ($parsedUrl['scheme'] ?? 'none') . "\n";
echo "Host: " . ($parsedUrl['host'] ?? 'none') . "\n";

// Test 7: Recommendations
echo "\n7. RECOMMENDATIONS\n";
echo "===================\n";

$recommendations = [];

if ($sessionConfig['domain'] !== '.medroid.ai') {
    $recommendations[] = "Set SESSION_DOMAIN=.medroid.ai in .env file";
}

if ($sessionConfig['same_site'] !== 'lax') {
    $recommendations[] = "Set SESSION_SAME_SITE=lax in .env file";
}

if (!$sessionConfig['secure'] && strpos($appUrl, 'https://') === 0) {
    $recommendations[] = "Set SESSION_SECURE_COOKIE=true for HTTPS";
}

if (empty($recommendations)) {
    echo "✓ Configuration looks good!\n";
} else {
    echo "Issues found:\n";
    foreach ($recommendations as $rec) {
        echo "- $rec\n";
    }
}

echo "\n=== TEST COMPLETED ===\n";
