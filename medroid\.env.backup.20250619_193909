APP_NAME="Medroid App"
APP_ENV=local
APP_KEY=base64:eFsLZXeHgqclfGvhkk2Jqsejp/7VWlRLz0G4RX8PkM4=
APP_DEBUG=true
APP_URL=https://api.medroid.ai
# Force HTTPS for LocalTunnel
FORCE_HTTPS=true
LOG_CHANNEL=stack
LOG_LEVEL=debug
# MySQL Configuration (MAMP)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=8889
DB_DATABASE=medroid_app
DB_USERNAME=root
DB_PASSWORD=root
BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
ASSET_URL=https://api.medroid.ai
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_DOMAIN=.medroid.ai
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=lax
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1
CACHE_STORE=file
# Instagram API Configuration (Instagram API with Instagram Login)
# App: Medroid AI-IG (ID: 951526903656551)
# For ngrok development, update INSTAGRAM_REDIRECT_URI to: https://YOUR_NGROK_URL.ngrok.io/auth/instagram/callback
INSTAGRAM_APP_ID=951526903656551
INSTAGRAM_APP_SECRET=ef3cee0cbb6f9a303702864445dbc2e6
INSTAGRAM_REDIRECT_URI=https://api.medroid.ai/auth/instagram/callback
INSTAGRAM_WEBHOOK_VERIFY_TOKEN=test123
TIKTOK_CLIENT_KEY=
TIKTOK_CLIENT_SECRET=
TIKTOK_REDIRECT_URI=https://api.medroid.ai/api/auth/tiktok/callback
MISTRAL_NEMO_API_KEY=vZabYNGkXr4F3XNidMLxH9QjZsYSH6a7
MISTRAL_NEMO_API_URL=
MISTRAL_NEMO_MODEL_NAME=mistral-nemo
# Use Groq (default)
AI_SERVICE_PROVIDER=groq
# Waitlist Configuration
# Set to 'true' to enable waitlist mode (invitation-only signup)
WAITLIST_MODE=false
# Or use OpenAI
# AI_SERVICE_PROVIDER=openai
# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_ORGANIZATION=
GROQ_API_KEY=********************************************************
GROQ_MODEL=meta-llama/Llama-4-Scout-17B-16E-Instruct
# NScale Configuration (Primary AI Service)
NSCALE_API_KEY=ns_8500e8e6034446cfbad846f48f0fd24b
NSCALE_MODEL=meta-llama/Llama-4-Scout-17B-16E-Instruct
# Chat Service Configuration
CHAT_PRIMARY_PROVIDER=nscale
CHAT_FALLBACK_PROVIDER=groq
CHAT_RETRY_ATTEMPTS=3
CHAT_TIMEOUT=60
# Agora Configuration
AGORA_APP_ID=********************************
AGORA_APP_CERTIFICATE=********************************
# Leave this empty for local development to use the built-in token generator
# For production, set this to your token service URL
AGORA_TOKEN_SERVICE_URL=
# Stripe Configuration
STRIPE_KEY=pk_test_51PqAN3JIfUPGWv0eve8hGg1uKv00m2aDr7AlgDa4HFRTufLhzX10mzRridpGm5ugwMpzB6uwFXjwllYTUUyFKnGj00xg54CmSG
STRIPE_SECRET=sk_test_51PqAN3JIfUPGWv0eWcaYGss8omgQb1XYlSWx2AqpoxXc7UupXPzE3yWLilD4RJC5ye4DwIiQvhUXWu1CezqzKSRS009Y6WUYSC
STRIPE_WEBHOOK_SECRET=
STRIPE_WEBHOOK_VERIFY=false
CASHIER_CURRENCY=usd
CASHIER_CURRENCY_LOCALE=en
# Brevo SMTP Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp-relay.brevo.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=xsmtpsib-44a5f86e09b174f6847d27455d3bd90b953283ef23edd85a7cc9f6d0c770ca00-tj0XT6YwFLsVJRfA
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Medroid"
# Firebase Configuration
FIREBASE_API_KEY=AIzaSyCowNEHlBVI-87pUoJs4IZ8COIK8zLxHRM
FIREBASE_PROJECT_ID=medroid-app
FIREBASE_MESSAGING_SENDER_ID=904964125270
FIREBASE_APP_ID=1:904964125270:android:39f41fb20de7957ea441f7
FIREBASE_SERVER_KEY=AIzaSyCowNEHlBVI-87pUoJs4IZ8COIK8zLxHRM

