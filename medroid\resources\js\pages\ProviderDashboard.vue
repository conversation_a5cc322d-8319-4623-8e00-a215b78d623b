<script setup>
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
  provider: {
    type: Object,
    required: true
  },
  stats: {
    type: Object,
    default: () => ({
      totalPatients: 0,
      upcomingAppointments: 0,
      monthlyEarnings: 0,
      completionRate: 0
    })
  },
  todayAppointments: {
    type: Array,
    default: () => []
  },
  upcomingAppointments: {
    type: Array,
    default: () => []
  }
});

const breadcrumbs = [
    {
        title: 'Provider Dashboard',
        href: '/provider/dashboard',
    },
];

// Methods
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusClass = (status) => {
  const classes = {
    'scheduled': 'bg-blue-100 text-blue-800',
    'confirmed': 'bg-green-100 text-green-800',
    'in_progress': 'bg-yellow-100 text-yellow-800',
    'completed': 'bg-gray-100 text-gray-800',
    'cancelled': 'bg-red-100 text-red-800'
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const quickActions = [
  {
    title: 'Manage Availability',
    href: '/provider/availability',
    icon: 'calendar-alt',
    description: 'Set your working hours and time slots',
    color: 'blue'
  },
  {
    title: 'View Appointments',
    href: '/provider/appointments',
    icon: 'calendar-check',
    description: 'See your upcoming appointments',
    color: 'green'
  },
  {
    title: 'Patient List',
    href: '/provider/patients',
    icon: 'users',
    description: 'Manage your patient records',
    color: 'purple'
  },
  {
    title: 'Earnings',
    href: '/provider/earnings',
    icon: 'dollar-sign',
    description: 'Track your earnings and payments',
    color: 'yellow'
  }
];
</script>

<template>
    <Head title="Provider Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="p-6">
            <div class="dashboard-container">
                <!-- Header Section -->
                <div class="dashboard-header mb-6">
                    <div class="header-content">
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">Provider Dashboard</h1>
                        <p class="text-gray-600">Manage your patients, availability, and appointments</p>
                    </div>
                </div>

                <!-- Provider Stats Section -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                        <div class="flex justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Total Patients</p>
                                <p class="text-2xl font-bold text-blue-600">{{ stats.totalPatients || 0 }}</p>
                                <p class="text-xs text-gray-400">Active patients</p>
                            </div>
                            <div class="text-blue-500">
                                <i class="fas fa-users text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                        <div class="flex justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Upcoming Appointments</p>
                                <p class="text-2xl font-bold text-green-600">{{ stats.upcomingAppointments || 0 }}</p>
                                <p class="text-xs text-gray-400">Next 7 days</p>
                            </div>
                            <div class="text-green-500">
                                <i class="fas fa-calendar-check text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
                        <div class="flex justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Completion Rate</p>
                                <p class="text-2xl font-bold text-purple-600">{{ stats.completionRate || 0 }}%</p>
                                <p class="text-xs text-gray-400">This month</p>
                            </div>
                            <div class="text-purple-500">
                                <i class="fas fa-chart-line text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 rounded-lg p-4 border-l-4 border-yellow-500">
                        <div class="flex justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Monthly Earnings</p>
                                <p class="text-2xl font-bold text-yellow-600">${{ stats.monthlyEarnings || 0 }}</p>
                                <p class="text-xs text-gray-400">Current month</p>
                            </div>
                            <div class="text-yellow-500">
                                <i class="fas fa-dollar-sign text-2xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Link
                            v-for="action in quickActions"
                            :key="action.title"
                            :href="action.href"
                            class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow duration-200 border border-gray-200"
                        >
                            <div class="flex items-center mb-3">
                                <div :class="`p-2 rounded-lg mr-3 bg-${action.color}-100`">
                                    <i :class="`fas fa-${action.icon} text-${action.color}-600`"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900">{{ action.title }}</h4>
                            </div>
                            <p class="text-sm text-gray-600">{{ action.description }}</p>
                        </Link>
                    </div>
                </div>

                <!-- Today's Appointments -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4">Today's Appointments</h3>
                        <div v-if="todayAppointments.length === 0" class="text-center py-8">
                            <i class="fas fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">No appointments scheduled for today</p>
                        </div>
                        <div v-else class="space-y-3">
                            <div
                                v-for="appointment in todayAppointments"
                                :key="appointment.id"
                                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900">{{ appointment.patient?.name || 'Unknown Patient' }}</p>
                                    <p class="text-sm text-gray-600">{{ formatDate(appointment.scheduled_at) }}</p>
                                </div>
                                <span :class="`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(appointment.status)}`">
                                    {{ appointment.status }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Upcoming Appointments -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4">Upcoming Appointments</h3>
                        <div v-if="upcomingAppointments.length === 0" class="text-center py-8">
                            <i class="fas fa-calendar-plus text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-500">No upcoming appointments</p>
                        </div>
                        <div v-else class="space-y-3">
                            <div
                                v-for="appointment in upcomingAppointments"
                                :key="appointment.id"
                                class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900">{{ appointment.patient?.name || 'Unknown Patient' }}</p>
                                    <p class="text-sm text-gray-600">{{ formatDate(appointment.scheduled_at) }}</p>
                                </div>
                                <span :class="`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(appointment.status)}`">
                                    {{ appointment.status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: #6b7280;
    font-size: 1rem;
}
</style>
