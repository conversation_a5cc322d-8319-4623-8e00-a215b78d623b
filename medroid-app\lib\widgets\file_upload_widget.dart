import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../models/file.dart';
import '../services/file_service.dart';
import '../utils/app_colors.dart';

class FileUploadWidget extends StatefulWidget {
  final String? category;
  final List<String>? allowedExtensions;
  final int? maxFileSizeBytes;
  final bool allowMultiple;
  final Function(List<FileModel>)? onFilesUploaded;
  final Function(String)? onError;
  final Widget? child;
  final String? buttonText;
  final IconData? buttonIcon;

  const FileUploadWidget({
    super.key,
    this.category,
    this.allowedExtensions,
    this.maxFileSizeBytes,
    this.allowMultiple = false,
    this.onFilesUploaded,
    this.onError,
    this.child,
    this.buttonText,
    this.buttonIcon,
  });

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  final FileService _fileService = FileService();
  final ImagePicker _imagePicker = ImagePicker();
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  List<String> _uploadingFiles = [];

  Future<void> _pickFiles() async {
    try {
      if (widget.allowedExtensions != null && 
          widget.allowedExtensions!.any((ext) => ['jpg', 'jpeg', 'png', 'gif'].contains(ext.toLowerCase()))) {
        // Show options for image files
        await _showImagePickerOptions();
      } else {
        // Use file picker for other file types
        await _pickDocuments();
      }
    } catch (e) {
      _handleError('Error picking files: $e');
    }
  }

  Future<void> _showImagePickerOptions() async {
    await showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.insert_drive_file),
              title: const Text('Choose File'),
              onTap: () {
                Navigator.pop(context);
                _pickDocuments();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );
      
      if (image != null) {
        await _uploadFile(image);
      }
    } catch (e) {
      _handleError('Error taking photo: $e');
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      if (widget.allowMultiple) {
        final List<XFile> images = await _imagePicker.pickMultiImage(
          imageQuality: 80,
        );
        
        if (images.isNotEmpty) {
          for (final image in images) {
            await _uploadFile(image);
          }
        }
      } else {
        final XFile? image = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );
        
        if (image != null) {
          await _uploadFile(image);
        }
      }
    } catch (e) {
      _handleError('Error picking images: $e');
    }
  }

  Future<void> _pickDocuments() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: widget.allowedExtensions != null ? FileType.custom : FileType.any,
        allowedExtensions: widget.allowedExtensions,
        allowMultiple: widget.allowMultiple,
        withData: kIsWeb, // For web, we need the bytes
      );

      if (result != null) {
        for (final file in result.files) {
          if (kIsWeb) {
            // Web platform - use bytes
            if (file.bytes != null) {
              await _uploadFileBytes(file.bytes!, file.name);
            }
          } else {
            // Mobile platform - use path
            if (file.path != null) {
              await _uploadFile(File(file.path!), customName: file.name);
            }
          }
        }
      }
    } catch (e) {
      _handleError('Error picking files: $e');
    }
  }

  Future<void> _uploadFile(dynamic file, {String? customName}) async {
    String fileName;
    
    if (file is XFile) {
      fileName = customName ?? file.name;
    } else if (file is File) {
      fileName = customName ?? file.path.split('/').last;
    } else {
      _handleError('Invalid file type');
      return;
    }

    // Check file size if limit is set
    if (widget.maxFileSizeBytes != null) {
      int fileSize = 0;
      if (file is XFile) {
        fileSize = await file.length();
      } else if (file is File) {
        fileSize = await file.length();
      }
      
      if (fileSize > widget.maxFileSizeBytes!) {
        _handleError('File size exceeds limit of ${_formatBytes(widget.maxFileSizeBytes!)}');
        return;
      }
    }

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _uploadingFiles.add(fileName);
    });

    try {
      final response = await _fileService.uploadFile(
        file: file,
        fileName: fileName,
        category: widget.category,
      );

      setState(() {
        _uploadingFiles.remove(fileName);
        if (_uploadingFiles.isEmpty) {
          _isUploading = false;
          _uploadProgress = 0.0;
        }
      });

      if (widget.onFilesUploaded != null) {
        widget.onFilesUploaded!([response.file]);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${response.file.name} uploaded successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _uploadingFiles.remove(fileName);
        if (_uploadingFiles.isEmpty) {
          _isUploading = false;
          _uploadProgress = 0.0;
        }
      });
      _handleError('Upload failed: $e');
    }
  }

  Future<void> _uploadFileBytes(Uint8List bytes, String fileName) async {
    // Check file size if limit is set
    if (widget.maxFileSizeBytes != null && bytes.length > widget.maxFileSizeBytes!) {
      _handleError('File size exceeds limit of ${_formatBytes(widget.maxFileSizeBytes!)}');
      return;
    }

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _uploadingFiles.add(fileName);
    });

    try {
      final response = await _fileService.uploadFile(
        file: bytes,
        fileName: fileName,
        category: widget.category,
      );

      setState(() {
        _uploadingFiles.remove(fileName);
        if (_uploadingFiles.isEmpty) {
          _isUploading = false;
          _uploadProgress = 0.0;
        }
      });

      if (widget.onFilesUploaded != null) {
        widget.onFilesUploaded!([response.file]);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${response.file.name} uploaded successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _uploadingFiles.remove(fileName);
        if (_uploadingFiles.isEmpty) {
          _isUploading = false;
          _uploadProgress = 0.0;
        }
      });
      _handleError('Upload failed: $e');
    }
  }

  void _handleError(String error) {
    if (widget.onError != null) {
      widget.onError!(error);
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _formatBytes(int bytes) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB'];
    int i = 0;
    double size = bytes.toDouble();
    
    while (size >= 1024 && i < suffixes.length - 1) {
      size /= 1024;
      i++;
    }
    
    return '${size.toStringAsFixed(1)} ${suffixes[i]}';
  }

  @override
  Widget build(BuildContext context) {
    if (widget.child != null) {
      return GestureDetector(
        onTap: _isUploading ? null : _pickFiles,
        child: Stack(
          children: [
            widget.child!,
            if (_isUploading)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withValues(alpha: 0.5),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ElevatedButton.icon(
          onPressed: _isUploading ? null : _pickFiles,
          icon: Icon(widget.buttonIcon ?? Icons.upload_file),
          label: Text(widget.buttonText ?? 'Upload File'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
        if (_isUploading) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 8),
                Text(
                  'Uploading ${_uploadingFiles.length} file(s)...',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (_uploadingFiles.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  ...(_uploadingFiles.take(3).map((fileName) => 
                    Text(
                      fileName,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )),
                  if (_uploadingFiles.length > 3)
                    Text(
                      'and ${_uploadingFiles.length - 3} more...',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }
}
