<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ref, onMounted, computed, watch } from 'vue';

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Users', href: '/users' },
];

const loading = ref(false);
const users = ref([]);
const searchQuery = ref('');
const selectedRole = ref('all');
const impersonating = ref(false);

// Pagination state
const currentPage = ref(1);
const perPage = ref(10);
const totalUsers = ref(0);
const totalPages = ref(0);

const filteredUsers = computed(() => {
    return users.value.filter(user => user && user.id);
});

const fetchUsers = async (page = 1) => {
    loading.value = true;
    try {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('per_page', perPage.value.toString());

        if (searchQuery.value.trim()) {
            params.append('search', searchQuery.value.trim());
        }

        if (selectedRole.value !== 'all') {
            params.append('role', selectedRole.value);
        }

        console.log('Fetching users with params:', params.toString());
        const response = await window.axios.get(`/users-list?${params.toString()}`);
        console.log('Users API response:', response.data);

        // Handle paginated response
        users.value = response.data.data || [];
        currentPage.value = response.data.current_page || 1;
        totalUsers.value = response.data.total || 0;
        totalPages.value = response.data.last_page || 1;

        console.log('Processed users:', users.value);
        console.log('Pagination info:', {
            currentPage: currentPage.value,
            totalUsers: totalUsers.value,
            totalPages: totalPages.value
        });
    } catch (error) {
        console.error('Error fetching users:', error);
        users.value = []; // Set empty array on error
    } finally {
        loading.value = false;
    }
};

const getRoleBadgeClass = (role) => {
    if (!role) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';

    const classes = {
        admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        provider: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        patient: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        manager: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
    };
    return classes[role] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
};

// Get the primary role for a user (from Spatie roles or role column)
const getUserPrimaryRole = (user) => {
    if (!user) return 'N/A';

    // Check if user has Spatie roles
    if (user.roles && user.roles.length > 0) {
        return user.roles[0].name;
    }

    // Fall back to role column
    return user.role || 'patient';
};

const impersonateUser = async (userId) => {
    if (confirm('Are you sure you want to login as this user?')) {
        impersonating.value = true;
        try {
            const response = await window.axios.post(`/impersonate/${userId}`);
            if (response.data.redirect_url) {
                window.location.href = response.data.redirect_url;
            }
        } catch (error) {
            console.error('Error impersonating user:', error);
            alert('Failed to impersonate user. Please try again.');
        } finally {
            impersonating.value = false;
        }
    }
};

const toggleUserStatus = async (userId, currentStatus) => {
    const newStatus = !currentStatus;
    const action = newStatus ? 'activate' : 'deactivate';

    if (confirm(`Are you sure you want to ${action} this user?`)) {
        try {
            const response = await window.axios.patch(`/users/${userId}/toggle-status`, {
                is_active: newStatus
            });

            if (response.data.success) {
                // Update the user in the local array
                const userIndex = users.value.findIndex(user => user.id === userId);
                if (userIndex !== -1) {
                    users.value[userIndex].is_active = newStatus;
                }
                alert(`User ${action}d successfully.`);
            }
        } catch (error) {
            console.error('Error toggling user status:', error);
            alert(`Failed to ${action} user. Please try again.`);
        }
    }
};

// Debounce function
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Debounced search function
const debouncedSearch = debounce(() => {
    currentPage.value = 1;
    fetchUsers(1);
}, 500);

// Watch for search changes with debounce
watch(searchQuery, () => {
    debouncedSearch();
});

// Watch for role filter changes (immediate)
watch(selectedRole, () => {
    currentPage.value = 1;
    fetchUsers(1);
});

// Pagination methods
const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
        fetchUsers(page);
    }
};

const nextPage = () => {
    if (currentPage.value < totalPages.value) {
        goToPage(currentPage.value + 1);
    }
};

const prevPage = () => {
    if (currentPage.value > 1) {
        goToPage(currentPage.value - 1);
    }
};

const changePerPage = (newPerPage) => {
    perPage.value = newPerPage;
    currentPage.value = 1;
    fetchUsers(1);
};

onMounted(() => {
    console.log('Users component mounted - with pagination and search');
    console.log('Search query:', searchQuery.value);
    console.log('Selected role:', selectedRole.value);
    fetchUsers();
});
</script>

<template>
    <Head title="User Management" />

    <AppLayout>
        <template #header>
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
                        User Management
                    </h2>
                    <nav class="flex mt-2" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li v-for="(breadcrumb, index) in breadcrumbs" :key="index" class="inline-flex items-center">
                                <Link v-if="index < breadcrumbs.length - 1" 
                                    :href="breadcrumb.href" 
                                    class="text-sm font-medium text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    {{ breadcrumb.title }}
                                </Link>
                                <span v-else class="text-sm font-medium text-gray-700 dark:text-gray-400">
                                    {{ breadcrumb.title }}
                                </span>
                                <svg v-if="index < breadcrumbs.length - 1" class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </li>
                        </ol>
                    </nav>
                </div>
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add User
                </button>
            </div>
        </template>

        <div class="py-12">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Filters -->
                <div class="mb-6 bg-red-100 border-4 border-red-500 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-bold text-red-800 mb-4">🔍 SEARCH & FILTER SECTION</h3>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Search Users</label>
                                <div class="relative">
                                    <input
                                        v-model="searchQuery"
                                        type="text"
                                        placeholder="Search by name or email..."
                                        class="w-full px-3 py-2 pr-10 border-2 border-blue-500 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                    <button
                                        v-if="searchQuery"
                                        @click="searchQuery = ''"
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                    >
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <i v-else class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Role</label>
                                <select
                                    v-model="selectedRole"
                                    class="px-3 py-2 border-2 border-green-500 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="all">All Roles</option>
                                    <option value="admin">Admin</option>
                                    <option value="provider">Provider</option>
                                    <option value="patient">Patient</option>
                                    <option value="manager">Manager</option>
                                    <option value="bot">Bot</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                        </div>

                        <div v-else-if="filteredUsers.length === 0" class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No users found.</p>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            User
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Role
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Last Login
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                        <i class="fas fa-user text-gray-500 dark:text-gray-400"></i>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ user?.name || 'N/A' }}
                                                    </div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        {{ user?.email || 'N/A' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getRoleBadgeClass(getUserPrimaryRole(user))" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ getUserPrimaryRole(user) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center space-x-2">
                                                <span :class="user?.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'"
                                                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                    {{ user?.is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                                <button
                                                    v-if="getUserPrimaryRole(user) !== 'admin'"
                                                    @click="toggleUserStatus(user.id, user.is_active)"
                                                    :class="user?.is_active ? 'bg-red-500 hover:bg-red-700' : 'bg-green-500 hover:bg-green-700'"
                                                    class="text-white text-xs px-2 py-1 rounded transition-colors duration-200"
                                                >
                                                    {{ user?.is_active ? 'Deactivate' : 'Activate' }}
                                                </button>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ user?.last_login || 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">
                                                Edit
                                            </button>
                                            <button
                                                v-if="getUserPrimaryRole(user) !== 'admin'"
                                                @click="impersonateUser(user.id)"
                                                :disabled="impersonating"
                                                class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3 disabled:opacity-50"
                                            >
                                                <i class="fas fa-user-secret mr-1"></i>
                                                Login As
                                            </button>
                                            <button v-if="getUserPrimaryRole(user) !== 'admin'" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination Controls -->
                        <div v-if="!loading && filteredUsers.length > 0" class="mt-6 flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-700 dark:text-gray-300">
                                    Showing {{ ((currentPage - 1) * perPage) + 1 }} to {{ Math.min(currentPage * perPage, totalUsers) }} of {{ totalUsers }} results
                                </span>
                            </div>

                            <div class="flex items-center space-x-2">
                                <!-- Per page selector -->
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-700 dark:text-gray-300">Show:</span>
                                    <select
                                        :value="perPage"
                                        @change="changePerPage(parseInt($event.target.value))"
                                        class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    >
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>

                                <!-- Pagination buttons -->
                                <div class="flex items-center space-x-1">
                                    <button
                                        @click="prevPage"
                                        :disabled="currentPage <= 1"
                                        class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700 dark:text-white"
                                    >
                                        Previous
                                    </button>

                                    <!-- Page numbers -->
                                    <template v-for="page in Math.min(totalPages, 5)" :key="page">
                                        <button
                                            v-if="page <= totalPages"
                                            @click="goToPage(page)"
                                            :class="page === currentPage ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700'"
                                            class="px-3 py-1 text-sm border border-gray-300 rounded dark:border-gray-600"
                                        >
                                            {{ page }}
                                        </button>
                                    </template>

                                    <span v-if="totalPages > 5" class="px-2 text-gray-500">...</span>

                                    <button
                                        v-if="totalPages > 5 && currentPage < totalPages - 2"
                                        @click="goToPage(totalPages)"
                                        :class="totalPages === currentPage ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700'"
                                        class="px-3 py-1 text-sm border border-gray-300 rounded dark:border-gray-600"
                                    >
                                        {{ totalPages }}
                                    </button>

                                    <button
                                        @click="nextPage"
                                        :disabled="currentPage >= totalPages"
                                        class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700 dark:text-white"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
