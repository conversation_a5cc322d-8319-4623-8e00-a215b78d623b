<?php

/**
 * Live Server Debug Script
 * 
 * This script helps diagnose the 500 error on the live server
 * Run this script on your live server to get detailed error information
 */

echo "=== LIVE SERVER DEBUG SCRIPT ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";

// Function to safely execute and capture output
function safeExec($command, $description) {
    echo "=== $description ===\n";
    echo "Command: $command\n";
    echo "Output:\n";
    
    $output = shell_exec($command . ' 2>&1');
    echo $output . "\n";
    
    return $output;
}

// Function to check file/directory
function checkPath($path, $description) {
    echo "=== $description ===\n";
    echo "Path: $path\n";
    echo "Exists: " . (file_exists($path) ? 'YES' : 'NO') . "\n";
    
    if (file_exists($path)) {
        echo "Type: " . (is_dir($path) ? 'Directory' : 'File') . "\n";
        echo "Readable: " . (is_readable($path) ? 'YES' : 'NO') . "\n";
        echo "Writable: " . (is_writable($path) ? 'YES' : 'NO') . "\n";
        
        if (is_file($path)) {
            echo "Size: " . filesize($path) . " bytes\n";
            echo "Modified: " . date('Y-m-d H:i:s', filemtime($path)) . "\n";
        }
    }
    echo "\n";
}

// 1. Check Laravel installation and basic info
echo "1. LARAVEL BASIC INFO\n";
echo "=====================\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Current Directory: " . getcwd() . "\n";
echo "Script Location: " . __FILE__ . "\n";

// Check if we're in the right directory
if (!file_exists('artisan')) {
    echo "ERROR: artisan file not found. Make sure you're running this from the Laravel root directory.\n";
    exit(1);
}

echo "Laravel Root: CONFIRMED\n\n";

// 2. Check environment and configuration
safeExec('php artisan --version', 'Laravel Version');
safeExec('php artisan env', 'Environment Check');

// 3. Check critical files and directories
checkPath('.env', 'Environment File');
checkPath('storage/logs', 'Log Directory');
checkPath('storage/framework/sessions', 'Sessions Directory');
checkPath('bootstrap/cache', 'Bootstrap Cache Directory');

// 4. Check recent Laravel logs
echo "=== RECENT LARAVEL LOGS ===\n";
$logFile = 'storage/logs/laravel.log';
if (file_exists($logFile)) {
    echo "Log file exists. Getting last 50 lines:\n";
    $output = shell_exec("tail -50 $logFile 2>&1");
    echo $output . "\n";
} else {
    echo "No Laravel log file found.\n";
}

// 5. Check web server error logs (common locations)
echo "=== WEB SERVER ERROR LOGS ===\n";
$errorLogPaths = [
    '/var/log/nginx/error.log',
    '/var/log/apache2/error.log',
    '/var/log/httpd/error_log',
    '/usr/local/var/log/nginx/error.log',
];

foreach ($errorLogPaths as $logPath) {
    if (file_exists($logPath) && is_readable($logPath)) {
        echo "Found error log: $logPath\n";
        echo "Last 20 lines:\n";
        $output = shell_exec("tail -20 $logPath 2>&1");
        echo $output . "\n";
        break;
    }
}

// 6. Check Laravel configuration
safeExec('php artisan config:show app', 'App Configuration');
safeExec('php artisan config:show session', 'Session Configuration');

// 7. Check routes
safeExec('php artisan route:list --compact', 'Routes List');

// 8. Check for specific Instagram-related issues
echo "=== INSTAGRAM SERVICE CHECK ===\n";
if (file_exists('app/Services/InstagramService.php')) {
    echo "InstagramService.php exists\n";
    
    // Check for the missing method
    $serviceContent = file_get_contents('app/Services/InstagramService.php');
    if (strpos($serviceContent, 'importUserMedia') !== false) {
        echo "importUserMedia method: FOUND\n";
    } else {
        echo "importUserMedia method: MISSING (This is likely the cause of the 500 error)\n";
    }
    
    // List all methods in the service
    echo "Available methods in InstagramService:\n";
    preg_match_all('/public function (\w+)\(/', $serviceContent, $matches);
    if (!empty($matches[1])) {
        foreach ($matches[1] as $method) {
            echo "- $method()\n";
        }
    }
} else {
    echo "InstagramService.php: NOT FOUND\n";
}

// 9. Check Instagram Controller
echo "\n=== INSTAGRAM CONTROLLER CHECK ===\n";
if (file_exists('app/Http/Controllers/InstagramController.php')) {
    echo "InstagramController.php exists\n";
    
    $controllerContent = file_get_contents('app/Http/Controllers/InstagramController.php');
    if (strpos($controllerContent, 'importUserMedia') !== false) {
        echo "Controller calls importUserMedia: YES (This is the problem)\n";
        
        // Find the line number
        $lines = explode("\n", $controllerContent);
        foreach ($lines as $lineNum => $line) {
            if (strpos($line, 'importUserMedia') !== false) {
                echo "Found on line " . ($lineNum + 1) . ": " . trim($line) . "\n";
            }
        }
    }
} else {
    echo "InstagramController.php: NOT FOUND\n";
}

// 10. Check permissions
echo "\n=== PERMISSIONS CHECK ===\n";
$paths = [
    'storage',
    'storage/logs',
    'storage/framework',
    'storage/framework/sessions',
    'bootstrap/cache',
];

foreach ($paths as $path) {
    if (file_exists($path)) {
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        echo "$path: $perms\n";
    }
}

// 11. Test a simple route
echo "\n=== ROUTE TEST ===\n";
echo "Testing if basic Laravel routing works...\n";
$testOutput = shell_exec('php artisan tinker --execute="echo \'Laravel is working\';" 2>&1');
echo "Tinker test: $testOutput\n";

// 12. Memory and system info
echo "\n=== SYSTEM INFO ===\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
echo "Post Max Size: " . ini_get('post_max_size') . "\n";

// 13. Final recommendations
echo "\n=== RECOMMENDATIONS ===\n";
echo "Based on the log analysis, the main issue appears to be:\n";
echo "- Missing 'importUserMedia' method in InstagramService class\n";
echo "- This method is being called from InstagramController line 233\n\n";

echo "TO FIX:\n";
echo "1. Add the missing importUserMedia method to InstagramService\n";
echo "2. Or update the controller to use the correct method name\n";
echo "3. Clear caches after fixing: php artisan optimize:clear\n\n";

echo "=== DEBUG SCRIPT COMPLETED ===\n";
echo "Please share this output to help diagnose the issue.\n";
