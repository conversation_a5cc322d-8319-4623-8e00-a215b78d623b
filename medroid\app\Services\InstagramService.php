<?php

namespace App\Services;

use App\Models\InstagramAccount;
use App\Models\SocialMediaPost;
use App\Models\SocialContent;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class InstagramService
{
    protected $baseUrl = 'https://graph.instagram.com';
    protected $authUrl = 'https://www.instagram.com/oauth/authorize';
    protected $tokenUrl = 'https://api.instagram.com/oauth/access_token';
    protected $appId;
    protected $appSecret;
    protected $redirectUri;

    public function __construct()
    {
        $this->appId = config('services.instagram.app_id');
        $this->appSecret = config('services.instagram.app_secret');
        $this->redirectUri = config('services.instagram.redirect_uri');

        Log::info('Instagram Service initialized', [
            'app_id' => $this->appId,
            'redirect_uri' => $this->redirectUri
        ]);
    }

    /**
     * Handle media change webhook
     */
    public function handleMediaChange($userId, $value)
    {
        Log::info('Handling Instagram media change', [
            'user_id' => $userId,
            'value' => $value
        ]);

        // Find the Instagram account
        $instagramAccount = InstagramAccount::where('instagram_user_id', $userId)
            ->where('is_active', true)
            ->first();

        if (!$instagramAccount) {
            Log::warning('Instagram account not found for user ID: ' . $userId);
            return;
        }

        // Sync the specific media or trigger a full sync
        if (isset($value['media_id'])) {
            $this->syncSpecificMedia($instagramAccount, $value['media_id']);
        } else {
            $this->syncAccountContent($instagramAccount);
        }
    }

    /**
     * Sync content for a specific Instagram account
     */
    public function syncAccountContent(InstagramAccount $account)
    {
        Log::info('Starting Instagram content sync for account: ' . $account->instagram_user_id);

        $importedCount = 0;
        $after = null;
        $maxPages = 10; // Limit to prevent infinite loops
        $currentPage = 0;

        try {
            do {
                $currentPage++;

                // Get user's media with pagination
                $media = $this->getUserMedia($account->access_token, $account->instagram_user_id, 25, $after);

                if (!$media || !isset($media['data']) || empty($media['data'])) {
                    Log::info('No more media found for Instagram account: ' . $account->instagram_user_id);
                    break;
                }

                Log::info('Processing Instagram media batch', [
                    'account_id' => $account->id,
                    'page' => $currentPage,
                    'posts_in_batch' => count($media['data'])
                ]);

                foreach ($media['data'] as $mediaItem) {
                    try {
                        // Import all posts (not just health-related)
                        $this->syncMediaItem($account, $mediaItem);
                        $importedCount++;

                        Log::debug('Synced Instagram post', [
                            'post_id' => $mediaItem['id'],
                            'caption_preview' => substr($mediaItem['caption'] ?? '', 0, 50) . '...'
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Failed to sync individual Instagram post', [
                            'post_id' => $mediaItem['id'],
                            'error' => $e->getMessage()
                        ]);
                        // Continue with other posts
                    }
                }

                // Check for next page
                $after = $media['paging']['cursors']['after'] ?? null;

                Log::info('Instagram media batch processed', [
                    'account_id' => $account->id,
                    'page' => $currentPage,
                    'imported_this_batch' => count($media['data']),
                    'total_imported' => $importedCount,
                    'has_next_page' => !empty($after)
                ]);

            } while ($after && $currentPage < $maxPages);

            // Update last sync time
            $account->update(['last_sync_at' => now()]);

            Log::info('Successfully synced Instagram content for account: ' . $account->instagram_user_id, [
                'total_imported' => $importedCount,
                'pages_processed' => $currentPage
            ]);

            return $importedCount;

        } catch (\Exception $e) {
            Log::error('Failed to sync Instagram content', [
                'account_id' => $account->id,
                'error' => $e->getMessage(),
                'imported_before_error' => $importedCount
            ]);
            throw $e;
        }
    }
    
    /**
     * Get user's media from Instagram API
     */
    protected function getUserMedia($accessToken, $userId, $limit = 25, $after = null)
    {
        try {
            $params = [
                'fields' => 'id,caption,media_type,media_url,thumbnail_url,permalink,timestamp,username,like_count,comments_count',
                'access_token' => $accessToken,
                'limit' => $limit,
            ];

            if ($after) {
                $params['after'] = $after;
            }

            $url = $userId === 'me' ? $this->baseUrl . '/me/media' : $this->baseUrl . '/' . $userId . '/media';
            $response = Http::get($url, $params);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Instagram media fetch failed', ['response' => $response->body()]);
            return null;
        } catch (\Exception $e) {
            Log::error('Instagram media fetch error', ['error' => $e->getMessage()]);
            return null;
        }
    }
    
    /**
     * Download and store Instagram media locally
     */
    protected function downloadAndStoreMedia($mediaUrl, $postId, $mediaType = 'IMAGE')
    {
        if (!$mediaUrl) {
            return null;
        }

        try {
            // Determine file extension based on media type and URL
            $extension = $this->getMediaExtension($mediaUrl, $mediaType);
            $filename = "instagram/{$postId}.{$extension}";

            // Check if file already exists
            if (Storage::disk('public')->exists($filename)) {
                Log::info('Media file already exists locally', ['filename' => $filename]);
                return Storage::url($filename);
            }

            // Download media with proper headers to avoid 403 errors
            $headers = [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept' => $mediaType === 'VIDEO' ? 'video/*,*/*;q=0.8' : 'image/webp,image/avif,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.9',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Referer' => 'https://www.instagram.com/',
                'Sec-Fetch-Dest' => $mediaType === 'VIDEO' ? 'video' : 'image',
                'Sec-Fetch-Mode' => 'no-cors',
                'Sec-Fetch-Site' => 'cross-site',
            ];

            $response = Http::withHeaders($headers)
                ->timeout(30)
                ->retry(2, 1000)
                ->get($mediaUrl);

            if ($response->successful()) {
                $mediaContent = $response->body();

                // Ensure directory exists
                Storage::disk('public')->makeDirectory('instagram');

                // Store the file
                Storage::disk('public')->put($filename, $mediaContent);

                Log::info('Successfully downloaded and stored Instagram media', [
                    'post_id' => $postId,
                    'filename' => $filename,
                    'size' => strlen($mediaContent)
                ]);

                return Storage::url($filename);
            } else {
                Log::warning('Failed to download Instagram media', [
                    'post_id' => $postId,
                    'url' => $mediaUrl,
                    'status' => $response->status()
                ]);
                return $mediaUrl; // Fallback to original URL
            }

        } catch (\Exception $e) {
            Log::error('Error downloading Instagram media', [
                'post_id' => $postId,
                'url' => $mediaUrl,
                'error' => $e->getMessage()
            ]);
            return $mediaUrl; // Fallback to original URL
        }
    }

    /**
     * Get appropriate file extension for media
     */
    protected function getMediaExtension($url, $mediaType)
    {
        // Try to get extension from URL
        $urlPath = parse_url($url, PHP_URL_PATH);
        $extension = pathinfo($urlPath, PATHINFO_EXTENSION);

        if ($extension) {
            return strtolower($extension);
        }

        // Fallback based on media type
        return $mediaType === 'VIDEO' ? 'mp4' : 'jpg';
    }

    /**
     * Sync a single media item
     */
    protected function syncMediaItem(InstagramAccount $account, $mediaItem)
    {
        // Download and store media locally
        $localMediaUrl = $this->downloadAndStoreMedia(
            $mediaItem['media_url'] ?? null,
            $mediaItem['id'],
            $mediaItem['media_type']
        );

        $localThumbnailUrl = null;
        if (isset($mediaItem['thumbnail_url'])) {
            $localThumbnailUrl = $this->downloadAndStoreMedia(
                $mediaItem['thumbnail_url'],
                $mediaItem['id'] . '_thumb',
                'IMAGE'
            );
        }

        // Check if post already exists
        $existingPost = SocialMediaPost::where('platform_post_id', $mediaItem['id'])->first();

        if ($existingPost) {
            // Update existing post with local URLs
            $existingPost->update([
                'media_type' => $mediaItem['media_type'],
                'media_url' => $localMediaUrl,
                'thumbnail_url' => $localThumbnailUrl,
                'display_media_url' => $localMediaUrl, // Use main media URL for display
                'caption' => $mediaItem['caption'] ?? '',
                'permalink' => $mediaItem['permalink'] ?? null,
                'like_count' => $mediaItem['like_count'] ?? 0,
                'comment_count' => $mediaItem['comments_count'] ?? 0,
                'posted_at' => Carbon::parse($mediaItem['timestamp']),
                'updated_at' => now()
            ]);

            // Update corresponding SocialContent only for health-related posts
            $caption = $mediaItem['caption'] ?? '';
            if ($this->isHealthRelated($caption)) {
                $this->createSocialContentFromPost($existingPost, $account);
            }

            Log::info('Updated existing Instagram post with local media', [
                'post_id' => $mediaItem['id'],
                'account_id' => $account->id,
                'local_url' => $localMediaUrl
            ]);
            return;
        }

        // Create new post in SocialMediaPost with local URLs
        $socialMediaPost = SocialMediaPost::create([
            'user_id' => $account->user_id,
            'instagram_account_id' => $account->id,
            'platform' => 'instagram',
            'platform_post_id' => $mediaItem['id'],
            'media_type' => $mediaItem['media_type'],
            'media_url' => $localMediaUrl,
            'thumbnail_url' => $localThumbnailUrl,
            'display_media_url' => $localMediaUrl, // Use main media URL for display
            'caption' => $mediaItem['caption'] ?? '',
            'permalink' => $mediaItem['permalink'] ?? null,
            'like_count' => $mediaItem['like_count'] ?? 0,
            'comment_count' => $mediaItem['comments_count'] ?? 0,
            'posted_at' => Carbon::parse($mediaItem['timestamp']),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Only create in SocialContent for health-related posts (for discover feed)
        $caption = $mediaItem['caption'] ?? '';
        if ($this->isHealthRelated($caption)) {
            $this->createSocialContentFromPost($socialMediaPost, $account);
        }

        Log::info('Created new Instagram post with local media', [
            'post_id' => $mediaItem['id'],
            'account_id' => $account->id,
            'local_url' => $localMediaUrl
        ]);
    }
    
    /**
     * Get Instagram user profile information
     */
    public function getUserProfile($accessToken, $userId)
    {
        try {
            $url = "{$this->baseUrl}/{$userId}";

            $params = [
                'fields' => 'id,username,account_type,media_count',
                'access_token' => $accessToken
            ];

            Log::info('Fetching Instagram user profile', [
                'url' => $url,
                'user_id' => $userId
            ]);

            $response = Http::get($url, $params);

            if ($response->successful()) {
                $profile = $response->json();

                Log::info('Instagram profile fetched successfully', [
                    'username' => $profile['username'] ?? 'unknown',
                    'account_type' => $profile['account_type'] ?? 'unknown'
                ]);

                return $profile;
            }

            Log::error('Failed to fetch Instagram user profile', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('Instagram profile fetch error', ['error' => $e->getMessage()]);
            return null;
        }
    }
    
    /**
     * Exchange short-lived token for long-lived token
     */
    public function exchangeForLongLivedToken($shortLivedToken)
    {
        $url = "{$this->baseUrl}/access_token";

        $response = Http::get($url, [
            'grant_type' => 'ig_exchange_token',
            'client_secret' => $this->appSecret,
            'access_token' => $shortLivedToken
        ]);

        if (!$response->successful()) {
            Log::error('Failed to exchange Instagram token', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            return null;
        }

        Log::info('Successfully exchanged for long-lived token');
        return $response->json();
    }

    /**
     * Refresh long-lived access token
     */
    public function refreshAccessToken($accessToken)
    {
        $url = "{$this->baseUrl}/refresh_access_token";

        $response = Http::get($url, [
            'grant_type' => 'ig_refresh_token',
            'access_token' => $accessToken
        ]);

        if (!$response->successful()) {
            Log::error('Failed to refresh Instagram token', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            return null;
        }

        return $response->json();
    }

    /**
     * Sync user media - refresh media content for the account
     */
    public function syncUserMedia(InstagramAccount $account)
    {
        Log::info('Starting Instagram media sync for account: ' . $account->instagram_user_id);

        try {
            $syncedCount = 0;
            $totalCount = 0;
            $after = null;

            do {
                $mediaData = $this->getUserMedia($account->access_token, $account->instagram_user_id, 25, $after);

                if (!$mediaData || !isset($mediaData['data'])) {
                    break;
                }

                $totalCount += count($mediaData['data']);

                foreach ($mediaData['data'] as $media) {
                    try {
                        $this->syncMediaItem($account, $media);
                        $syncedCount++;
                    } catch (\Exception $e) {
                        Log::warning('Failed to sync individual media item', [
                            'media_id' => $media['id'] ?? 'unknown',
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                $after = $mediaData['paging']['cursors']['after'] ?? null;

            } while ($after && $totalCount < 100); // Limit to 100 posts for media refresh

            // Update account sync timestamp
            $account->update([
                'last_sync_at' => now(),
                'media_count' => $totalCount
            ]);

            Log::info('Instagram media sync completed', [
                'account_id' => $account->id,
                'synced' => $syncedCount,
                'total' => $totalCount
            ]);

            return [
                'synced' => $syncedCount,
                'total' => $totalCount
            ];

        } catch (\Exception $e) {
            Log::error('Failed to sync Instagram media', [
                'account_id' => $account->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Sync specific media by ID
     */
    protected function syncSpecificMedia(InstagramAccount $instagramAccount, $mediaId)
    {
        try {
            $response = Http::get("{$this->baseUrl}/{$mediaId}", [
                'fields' => 'id,media_type,media_url,thumbnail_url,caption,permalink,timestamp,like_count,comments_count',
                'access_token' => $instagramAccount->access_token
            ]);

            if (!$response->successful()) {
                Log::error('Failed to get specific Instagram media', [
                    'media_id' => $mediaId,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return;
            }

            $mediaData = $response->json();
            $this->syncMediaItem($instagramAccount, $mediaData);

            Log::info('Synced specific Instagram media', [
                'media_id' => $mediaId,
                'account_id' => $instagramAccount->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to sync specific media', [
                'media_id' => $mediaId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get authorization URL for Instagram Business API
     */
    public function getAuthorizationUrl($state = null)
    {
        $params = [
            'client_id' => $this->appId,
            'redirect_uri' => $this->redirectUri,
            'scope' => 'instagram_business_basic,instagram_business_content_publish,instagram_business_manage_messages,instagram_business_manage_comments',
            'response_type' => 'code',
        ];

        if ($state) {
            $params['state'] = $state;
        }

        $authUrl = $this->authUrl . '?' . http_build_query($params);

        Log::info('Generated Instagram auth URL', [
            'url' => $authUrl,
            'params' => $params
        ]);

        return $authUrl;
    }

    /**
     * Exchange authorization code for access token (Instagram Business Login)
     */
    public function getAccessToken($code)
    {
        try {
            $params = [
                'client_id' => $this->appId,
                'client_secret' => $this->appSecret,
                'grant_type' => 'authorization_code',
                'redirect_uri' => $this->redirectUri,
                'code' => $code,
            ];

            Log::info('Exchanging code for Instagram access token', [
                'params' => array_merge($params, ['client_secret' => '[HIDDEN]'])
            ]);

            $response = Http::asForm()->post($this->tokenUrl, $params);

            if ($response->successful()) {
                $data = $response->json();

                Log::info('Instagram token exchange successful', [
                    'user_id' => $data['user_id'] ?? 'unknown',
                    'has_access_token' => isset($data['access_token'])
                ]);

                // For Instagram Business Login, we get a short-lived token
                // Exchange it for a long-lived token
                if (isset($data['access_token'])) {
                    $longLivedToken = $this->exchangeForLongLivedToken($data['access_token']);
                    if ($longLivedToken) {
                        $data['access_token'] = $longLivedToken['access_token'];
                        $data['expires_in'] = $longLivedToken['expires_in'] ?? 5184000; // 60 days default
                    }
                }

                return $data;
            }

            $responseBody = $response->body();
            Log::error('Instagram token exchange failed', [
                'status' => $response->status(),
                'response' => $responseBody
            ]);
            
            // Check if this is a personal account error
            if (strpos($responseBody, 'Invalid account type') !== false || 
                strpos($responseBody, 'business') !== false ||
                $response->status() === 400) {
                return [
                    'error' => 'personal_account',
                    'message' => 'Personal Instagram accounts cannot be connected. Please use a Business or Creator account.'
                ];
            }
            
            return null;
        } catch (\Exception $e) {
            Log::error('Instagram token exchange error', ['error' => $e->getMessage()]);
            return null;
        }
    }




    /**
     * Connect Instagram account for a user (Instagram Business API)
     */
    public function connectAccount($user, $code)
    {
        try {
            Log::info('Starting Instagram account connection', [
                'user_id' => $user->id,
                'code_length' => strlen($code)
            ]);

            $cacheKey = "instagram_progress_{$user->id}";

            // Set initial progress
            Cache::put($cacheKey, [
                'status' => 'connecting',
                'step' => 'exchanging_token',
                'progress' => 20,
                'message' => 'Exchanging authorization code for access token...',
                'imported_count' => 0
            ], 300);

            // Exchange code for access token
            $tokenData = $this->getAccessToken($code);

            if (!$tokenData) {
                Cache::put($cacheKey, [
                    'status' => 'error',
                    'step' => 'token_exchange_failed',
                    'progress' => 0,
                    'message' => 'Failed to get Instagram access token',
                    'imported_count' => 0
                ], 60);

                return [
                    'success' => false,
                    'message' => 'Failed to get Instagram access token'
                ];
            }

            // Check if this is a personal account error
            if (isset($tokenData['error']) && $tokenData['error'] === 'personal_account') {
                Cache::put($cacheKey, [
                    'status' => 'error',
                    'step' => 'personal_account_error',
                    'progress' => 0,
                    'message' => $tokenData['message'],
                    'imported_count' => 0
                ], 60);

                return [
                    'success' => false,
                    'message' => $tokenData['message']
                ];
            }

            // Update progress
            Cache::put($cacheKey, [
                'status' => 'connecting',
                'step' => 'fetching_profile',
                'progress' => 40,
                'message' => 'Fetching Instagram profile information...',
                'imported_count' => 0
            ], 300);

            // Get user profile
            $profile = $this->getUserProfile($tokenData['access_token'], $tokenData['user_id']);

            if (!$profile) {
                Cache::put($cacheKey, [
                    'status' => 'error',
                    'step' => 'profile_fetch_failed',
                    'progress' => 0,
                    'message' => 'Failed to get Instagram profile',
                    'imported_count' => 0
                ], 60);

                return [
                    'success' => false,
                    'message' => 'Failed to get Instagram profile'
                ];
            }

            // Check if account type is supported (Business, Creator, or Media Creator only)
            $accountType = $profile['account_type'] ?? 'PERSONAL';
            $supportedTypes = ['BUSINESS', 'CREATOR', 'MEDIA_CREATOR'];

            if (!in_array($accountType, $supportedTypes)) {
                Log::warning('Unsupported Instagram account type', [
                    'account_type' => $accountType,
                    'username' => $profile['username'] ?? 'unknown',
                    'supported_types' => $supportedTypes
                ]);

                Cache::put($cacheKey, [
                    'status' => 'error',
                    'step' => 'unsupported_account_type',
                    'progress' => 0,
                    'message' => 'Only Instagram Business and Creator accounts can be connected. Personal accounts are not supported by the Instagram API.',
                    'imported_count' => 0
                ], 60);

                return [
                    'success' => false,
                    'message' => 'Only Instagram Business and Creator accounts can be connected. Personal accounts are not supported by the Instagram API.'
                ];
            }

            // Update progress
            Cache::put($cacheKey, [
                'status' => 'connecting',
                'step' => 'saving_account',
                'progress' => 60,
                'message' => 'Saving Instagram account information...',
                'imported_count' => 0
            ], 300);

            // Save Instagram account
            $instagramAccount = InstagramAccount::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'instagram_user_id' => $tokenData['user_id']
                ],
                [
                    'username' => $profile['username'] ?? 'Unknown',
                    'access_token' => $tokenData['access_token'],
                    'account_type' => $accountType,
                    'media_count' => $profile['media_count'] ?? 0,
                    'expires_at' => now()->addDays(60), // Instagram Business API tokens are long-lived
                    'is_active' => true,
                ]
            );

            Log::info('Instagram account connected successfully', [
                'account_id' => $instagramAccount->id,
                'username' => $instagramAccount->username,
                'account_type' => $instagramAccount->account_type
            ]);

            // Update progress
            Cache::put($cacheKey, [
                'status' => 'importing',
                'step' => 'importing_posts',
                'progress' => 80,
                'message' => 'Importing your health-related Instagram posts...',
                'imported_count' => 0
            ], 300);

            // Sync initial content
            $importedCount = 0;
            try {
                $importedCount = $this->syncAccountContent($instagramAccount);
            } catch (\Exception $e) {
                Log::warning('Initial content sync failed, but account connected', [
                    'error' => $e->getMessage()
                ]);
            }

            // Final progress
            Cache::put($cacheKey, [
                'status' => 'completed',
                'step' => 'completed',
                'progress' => 100,
                'message' => "Instagram account connected successfully! Imported {$importedCount} health-related posts.",
                'imported_count' => $importedCount
            ], 120); // Keep for 2 minutes

            return [
                'success' => true,
                'message' => 'Instagram account connected successfully',
                'account' => $instagramAccount,
                'imported_count' => $importedCount
            ];

        } catch (\Exception $e) {
            Log::error('Instagram account connection failed', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'trace' => $e->getTraceAsString()
            ]);

            $cacheKey = "instagram_progress_{$user->id}";
            Cache::put($cacheKey, [
                'status' => 'error',
                'step' => 'connection_failed',
                'progress' => 0,
                'message' => 'Failed to connect Instagram account: ' . $e->getMessage(),
                'imported_count' => 0
            ], 60);

            return [
                'success' => false,
                'message' => 'Failed to connect Instagram account: ' . $e->getMessage()
            ];
        }
    }



    /**
     * Check if content is health-related using AI-powered intelligent filtering
     */
    public function isHealthRelated($caption)
    {
        if (!$caption) return false;

        // First, try quick keyword-based filtering for obvious cases
        $quickResult = $this->quickHealthCheck($caption);
        if ($quickResult !== null) {
            return $quickResult;
        }

        // For ambiguous cases, use AI analysis
        return $this->aiHealthAnalysis($caption);
    }

    /**
     * Quick keyword-based health check for obvious cases
     */
    private function quickHealthCheck($caption)
    {
        $caption = strtolower($caption);

        // Obvious health indicators
        $strongHealthIndicators = [
            '#health', '#wellness', '#fitness', '#meditation', '#yoga',
            '#mindfulness', '#selfcare', '#mentalhealth', '#nutrition',
            '#workout', '#exercise', '#healing', '#therapy', '#medical',
            '#motivation', '#inspiration', '#personalgrowth', '#mindset',
            '#positivity', '#wellbeing', '#balance', '#innerpeace',
            '#selflove', '#growth', '#love', '#spiritual', '#soul',
            '#courage', '#passion', '#empowerment', '#journey',
            'mental health', 'physical therapy', 'doctor visit', 'hospital',
            'medication', 'treatment', 'recovery', 'rehabilitation',
            'motivation', 'inspiration', 'personal growth', 'mindset',
            'positivity', 'well-being', 'inner peace', 'life goals',
            'self love', 'self-love', 'emotional wellness', 'soul mate'
        ];

        foreach ($strongHealthIndicators as $indicator) {
            if (strpos($caption, $indicator) !== false) {
                return true; // Definitely health-related
            }
        }

        // Obvious non-health indicators
        $nonHealthIndicators = [
            '#food', '#travel', '#fashion', '#shopping', '#party',
            '#nightlife', '#gaming', '#technology', '#business', '#work',
            '#money', '#car', '#music', '#movie', '#entertainment'
        ];

        foreach ($nonHealthIndicators as $indicator) {
            if (strpos($caption, $indicator) !== false) {
                // Check if it also has health context
                $hasHealthContext = false;
                foreach ($strongHealthIndicators as $healthIndicator) {
                    if (strpos($caption, $healthIndicator) !== false) {
                        $hasHealthContext = true;
                        break;
                    }
                }
                if (!$hasHealthContext) {
                    return false; // Definitely not health-related
                }
            }
        }

        return null; // Ambiguous, needs AI analysis
    }

    /**
     * AI-powered health content analysis (economical approach)
     */
    private function aiHealthAnalysis($caption)
    {
        // Check cache first to avoid redundant API calls
        $cacheKey = 'health_analysis_' . md5($caption);
        $cachedResult = Cache::get($cacheKey);

        if ($cachedResult !== null) {
            return $cachedResult;
        }

        try {
            // Use OpenAI with minimal tokens for cost efficiency
            $prompt = "Analyze if this social media post is related to health, wellness, mental health, fitness, nutrition, meditation, self-care, or personal wellbeing. Respond only with 'YES' or 'NO'.\n\nPost: " . substr($caption, 0, 200);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('OPENAI_API_KEY'),
                'Content-Type' => 'application/json',
            ])->timeout(10)->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-3.5-turbo', // Most economical model
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a health content classifier. Respond only with YES or NO.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 5, // Minimal tokens for cost efficiency
                'temperature' => 0, // Consistent results
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $aiResponse = trim(strtoupper($result['choices'][0]['message']['content'] ?? ''));
                $isHealthRelated = $aiResponse === 'YES';

                // Cache result for 7 days to avoid re-analyzing same content
                Cache::put($cacheKey, $isHealthRelated, 60 * 24 * 7);

                Log::info('AI health analysis completed', [
                    'caption_preview' => substr($caption, 0, 50),
                    'ai_response' => $aiResponse,
                    'is_health_related' => $isHealthRelated
                ]);

                return $isHealthRelated;
            }

        } catch (\Exception $e) {
            Log::warning('AI health analysis failed, falling back to keyword analysis', [
                'error' => $e->getMessage(),
                'caption_preview' => substr($caption, 0, 50)
            ]);
        }

        // Fallback to enhanced keyword analysis if AI fails
        return $this->fallbackHealthCheck($caption);
    }

    /**
     * Fallback health check using enhanced keyword analysis
     */
    private function fallbackHealthCheck($caption)
    {
        $caption = strtolower($caption);

        // Wellness-related terms and phrases
        $wellnessTerms = [
            'peaceful', 'peace', 'calm', 'relaxation', 'zen', 'tranquil',
            'serenity', 'balance', 'mindful', 'breathe', 'gratitude',
            'positive', 'motivation', 'inspiration', 'spiritual', 'soul',
            'inner peace', 'good vibes', 'feel good', 'self love', 'selflove',
            'personal growth', 'transformation', 'healing journey', 'growth',
            'journey', 'dreams', 'goals', 'success mindset', 'empowerment',
            'begin today', 'start now', 'take action', 'seize the day', 'seize',
            'make it count', 'time management', 'productivity', 'life goals',
            'hustle', 'craft your story', 'masterpiece', 'path to success',
            'limitless', 'conquer', 'break free', 'reclaim', 'time is now',
            'trust your gut', 'embrace', 'courage', 'passion', 'fire',
            'fuel your fire', 'ignite', 'bright', 'smile', 'love conquers',
            'challenge', 'quest', 'mirror', 'wish', 'worth', 'recognize',
            'opportunity', 'moment', 'heart', 'ready', 'arrive', 'soulmate'
        ];

        foreach ($wellnessTerms as $term) {
            if (strpos($caption, $term) !== false) {
                return true;
            }
        }

        // Check for wellness emojis
        $wellnessEmojis = ['🧘', '🕉️', '☮️', '🌱', '🌿', '✨', '🙏', '💚', '💙', '💜'];
        foreach ($wellnessEmojis as $emoji) {
            if (strpos($caption, $emoji) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create SocialContent entry from SocialMediaPost for discover feed
     */
    protected function createSocialContentFromPost(SocialMediaPost $post, InstagramAccount $account)
    {
        try {
            // Check if SocialContent already exists for this post
            $existingContent = SocialContent::where('source_id', $post->platform_post_id)
                ->where('source', 'instagram')
                ->first();

            if ($existingContent) {
                // Update existing content with Instagram-specific data
                // For video content, use the actual video URL as media_url, not the thumbnail
                $mediaUrl = ($post->media_type === 'VIDEO' && $post->media_url)
                    ? $post->media_url
                    : $post->display_media_url;

                $existingContent->update([
                    'caption' => $post->caption,
                    'media_url' => $mediaUrl,
                    'thumbnail_url' => $post->thumbnail_url,
                    'video_url' => $post->media_url, // Always use the main media_url for video_url
                    'published_at' => $post->posted_at,
                    'username' => $account->username, // Instagram username
                    'instagram_username' => $account->username, // For display
                    'permalink' => $post->permalink, // Instagram post URL
                    'health_topics' => $this->extractHealthTopics($post->caption),
                    'relevance_score' => $this->calculateRelevanceScore($post->caption),
                    'engagement_metrics' => [
                        'likes' => $post->like_count,
                        'comments' => $post->comment_count,
                        'shares' => 0,
                        'saves' => 0,
                        'original_instagram_likes' => $post->like_count,
                        'original_instagram_comments' => $post->comment_count
                    ]
                ]);

                Log::debug('Updated existing SocialContent for Instagram post', [
                    'post_id' => $post->platform_post_id
                ]);
            } else {
                // Create new SocialContent entry with Instagram-specific data
                // For video content, use the actual video URL as media_url, not the thumbnail
                $mediaUrl = ($post->media_type === 'VIDEO' && $post->media_url)
                    ? $post->media_url
                    : $post->display_media_url;

                SocialContent::create([
                    'user_id' => $post->user_id,
                    'source' => 'instagram',
                    'source_id' => $post->platform_post_id,
                    'content_type' => $this->mapMediaTypeToContentType($post->media_type),
                    'media_url' => $mediaUrl,
                    'thumbnail_url' => $post->thumbnail_url,
                    'video_url' => $post->media_url, // Always use the main media_url for video_url
                    'caption' => $post->caption,
                    'username' => $account->username, // Instagram username
                    'instagram_username' => $account->username, // For display purposes
                    'permalink' => $post->permalink, // Instagram post URL for "View on Instagram"
                    'health_topics' => $this->extractHealthTopics($post->caption),
                    'relevance_score' => $this->calculateRelevanceScore($post->caption),
                    'engagement_metrics' => [
                        'likes' => $post->like_count,
                        'comments' => $post->comment_count,
                        'shares' => 0,
                        'saves' => 0,
                        'original_instagram_likes' => $post->like_count,
                        'original_instagram_comments' => $post->comment_count
                    ],
                    'filtered_status' => 'approved', // Auto-approve Instagram content
                    'published_at' => $post->posted_at,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                Log::debug('Created new SocialContent for Instagram post', [
                    'post_id' => $post->platform_post_id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to create SocialContent from Instagram post', [
                'post_id' => $post->platform_post_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Map Instagram media type to content type
     */
    protected function mapMediaTypeToContentType($mediaType)
    {
        return match($mediaType) {
            'VIDEO', 'REELS' => 'video',
            'IMAGE', 'CAROUSEL_ALBUM' => 'image',
            default => 'image'
        };
    }

    /**
     * Extract health topics from caption with intelligent categorization
     */
    protected function extractHealthTopics($caption)
    {
        if (!$caption) return [];

        $topics = [];
        $caption = strtolower($caption);

        // Define topic categories
        $topicCategories = [
            'physical_fitness' => [
                'fitness', 'workout', 'exercise', 'gym', 'training', 'cardio',
                'strength', 'muscle', 'bodybuilding', 'crossfit', 'running',
                'cycling', 'swimming', 'hiking', 'walking', 'jogging', 'marathon'
            ],
            'nutrition' => [
                'nutrition', 'diet', 'healthy eating', 'organic', 'vitamin',
                'supplement', 'protein', 'calories', 'detox', 'cleanse'
            ],
            'mental_wellness' => [
                'mental health', 'anxiety', 'depression', 'stress', 'mindfulness',
                'meditation', 'peaceful', 'peace', 'calm', 'relaxation', 'zen',
                'stress-free', 'stressfree', 'tranquil', 'serenity', 'balance'
            ],
            'self_care' => [
                'self-care', 'selfcare', 'self care', 'wellbeing', 'well-being',
                'spa', 'massage', 'skincare', 'sleep', 'rest', 'recovery'
            ],
            'holistic_health' => [
                'yoga', 'pilates', 'tai chi', 'qigong', 'acupuncture',
                'ayurveda', 'homeopathy', 'herbal', 'essential oils',
                'aromatherapy', 'holistic', 'alternative', 'natural'
            ],
            'emotional_wellness' => [
                'motivation', 'inspiration', 'positivity', 'happiness', 'joy',
                'spiritual', 'spirituality', 'soul', 'inner peace', 'healing',
                'personal development', 'transformation', 'self-love'
            ]
        ];

        // Extract topics by category
        foreach ($topicCategories as $category => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($caption, $keyword) !== false) {
                    $topics[] = $keyword;
                }
            }
        }

        // Extract health-related hashtags
        preg_match_all('/#(\w+)/', $caption, $matches);
        if (!empty($matches[1])) {
            $healthHashtags = [
                'health', 'wellness', 'fitness', 'meditation', 'yoga',
                'mindfulness', 'selfcare', 'mentalhealth', 'nutrition',
                'healthy', 'workout', 'exercise', 'peace', 'calm',
                'healing', 'recovery', 'balance', 'wellbeing', 'zen',
                'stressfree', 'peaceful', 'tranquil', 'breathe', 'gratitude',
                'positivity', 'motivation', 'inspiration', 'spiritual',
                'holistic', 'natural', 'organic', 'detox', 'cleanse'
            ];

            foreach ($matches[1] as $hashtag) {
                $hashtagLower = strtolower($hashtag);
                if (in_array($hashtagLower, $healthHashtags)) {
                    $topics[] = '#' . $hashtagLower;
                }
            }
        }

        // Extract wellness phrases
        $wellnessPhrases = [
            'mental health', 'self care', 'work life balance', 'inner peace',
            'good vibes', 'positive vibes', 'healthy lifestyle', 'personal growth',
            'mind body', 'body and soul', 'healing journey', 'stress relief'
        ];

        foreach ($wellnessPhrases as $phrase) {
            if (strpos($caption, $phrase) !== false) {
                $topics[] = $phrase;
            }
        }

        return array_unique($topics);
    }

    /**
     * Calculate relevance score based on content
     */
    protected function calculateRelevanceScore($caption)
    {
        if (!$caption) return 0.5;

        $score = 0.5; // Base score

        // Boost score for health-related content
        if ($this->isHealthRelated($caption)) {
            $score += 0.3;
        }

        // Boost for hashtags
        $hashtagCount = substr_count($caption, '#');
        $score += min($hashtagCount * 0.05, 0.2);

        return min($score, 1.0);
    }
}
