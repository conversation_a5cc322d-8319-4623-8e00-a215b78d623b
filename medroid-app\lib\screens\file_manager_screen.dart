import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/file.dart';
import '../services/file_service.dart';
import '../widgets/responsive_centered_container.dart';
import '../widgets/file_upload_widget.dart';
import '../utils/app_colors.dart';

class FileManagerScreen extends StatefulWidget {
  const FileManagerScreen({super.key});

  @override
  State<FileManagerScreen> createState() => _FileManagerScreenState();
}

class _FileManagerScreenState extends State<FileManagerScreen> {
  final FileService _fileService = FileService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<FileModel> _files = [];
  Map<String, String> _categories = {};
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String _selectedCategory = 'all';
  String _selectedType = 'all';
  String _viewMode = 'grid'; // 'grid' or 'list'
  String _sortBy = 'created_at';
  String _sortOrder = 'desc';
  int _currentPage = 1;
  bool _hasMorePages = false;
  Set<int> _selectedFiles = {};

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadFiles();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMorePages) {
        _loadMoreFiles();
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _fileService.getCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading categories: $e')),
        );
      }
    }
  }

  Future<void> _loadFiles({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _files.clear();
        _selectedFiles.clear();
      });
    }

    setState(() {
      _isLoading = refresh || _files.isEmpty;
    });

    try {
      final response = await _fileService.getFiles(
        page: _currentPage,
        search:
            _searchController.text.isNotEmpty ? _searchController.text : null,
        category: _selectedCategory != 'all' ? _selectedCategory : null,
        type: _selectedType != 'all' ? _selectedType : null,
        sortBy: _sortBy,
        sortOrder: _sortOrder,
      );

      setState(() {
        if (refresh || _currentPage == 1) {
          _files = response.data;
        } else {
          _files.addAll(response.data);
        }
        _hasMorePages = response.hasMorePages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading files: $e')),
        );
      }
    }
  }

  Future<void> _loadMoreFiles() async {
    if (_isLoadingMore || !_hasMorePages) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final response = await _fileService.getFiles(
        page: _currentPage,
        search:
            _searchController.text.isNotEmpty ? _searchController.text : null,
        category: _selectedCategory != 'all' ? _selectedCategory : null,
        type: _selectedType != 'all' ? _selectedType : null,
        sortBy: _sortBy,
        sortOrder: _sortOrder,
      );

      setState(() {
        _files.addAll(response.data);
        _hasMorePages = response.hasMorePages;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
        _currentPage--; // Revert page increment on error
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading more files: $e')),
        );
      }
    }
  }

  void _onSearchChanged() {
    // Debounce search
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchController.text == _searchController.text) {
        _loadFiles(refresh: true);
      }
    });
  }

  void _onFilterChanged() {
    _loadFiles(refresh: true);
  }

  void _toggleFileSelection(int fileId) {
    setState(() {
      if (_selectedFiles.contains(fileId)) {
        _selectedFiles.remove(fileId);
      } else {
        _selectedFiles.add(fileId);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedFiles.clear();
    });
  }

  Future<void> _deleteSelectedFiles() async {
    if (_selectedFiles.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Files'),
        content: Text(
            'Are you sure you want to delete ${_selectedFiles.length} file(s)?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _fileService.bulkDeleteFiles(_selectedFiles.toList());
        _clearSelection();
        _loadFiles(refresh: true);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Files deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting files: $e')),
          );
        }
      }
    }
  }

  void _showUploadDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: kIsWeb ? 500 : MediaQuery.of(context).size.width * 0.9,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Text(
                    'Upload Files',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              FileUploadWidget(
                allowMultiple: true,
                onFilesUploaded: (files) {
                  Navigator.of(context).pop();
                  _loadFiles(refresh: true);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content:
                          Text('${files.length} file(s) uploaded successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                onError: (error) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(error),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('File Manager'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_viewMode == 'grid' ? Icons.list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _viewMode = _viewMode == 'grid' ? 'list' : 'grid';
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              final parts = value.split('_');
              setState(() {
                _sortBy = parts[0];
                _sortOrder = parts[1];
              });
              _onFilterChanged();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                  value: 'created_at_desc', child: Text('Newest First')),
              const PopupMenuItem(
                  value: 'created_at_asc', child: Text('Oldest First')),
              const PopupMenuItem(value: 'name_asc', child: Text('Name A-Z')),
              const PopupMenuItem(value: 'name_desc', child: Text('Name Z-A')),
              const PopupMenuItem(
                  value: 'size_desc', child: Text('Largest First')),
              const PopupMenuItem(
                  value: 'size_asc', child: Text('Smallest First')),
            ],
          ),
        ],
      ),
      body: ResponsiveCenteredContainer(
        child: Column(
          children: [
            // Search and Filters
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: Column(
                children: [
                  // Search Bar
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search files...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    onChanged: (_) => _onSearchChanged(),
                  ),
                  const SizedBox(height: 12),
                  // Filter Row
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedCategory,
                          decoration: InputDecoration(
                            labelText: 'Category',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem(
                                value: 'all', child: Text('All Categories')),
                            ..._categories.entries.map(
                              (entry) => DropdownMenuItem(
                                  value: entry.key, child: Text(entry.value)),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value!;
                            });
                            _onFilterChanged();
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedType,
                          decoration: InputDecoration(
                            labelText: 'Type',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: 'all', child: Text('All Types')),
                            DropdownMenuItem(
                                value: 'images', child: Text('Images')),
                            DropdownMenuItem(
                                value: 'documents', child: Text('Documents')),
                            DropdownMenuItem(
                                value: 'videos', child: Text('Videos')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value!;
                            });
                            _onFilterChanged();
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Bulk Actions Bar
            if (_selectedFiles.isNotEmpty)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                color: AppColors.primaryColor.withOpacity(0.1),
                child: Row(
                  children: [
                    Text(
                      '${_selectedFiles.length} file(s) selected',
                      style: TextStyle(
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: _deleteSelectedFiles,
                      icon: const Icon(Icons.delete, color: Colors.red),
                      label: const Text('Delete',
                          style: TextStyle(color: Colors.red)),
                    ),
                    TextButton(
                      onPressed: _clearSelection,
                      child: const Text('Clear'),
                    ),
                  ],
                ),
              ),
            // Files Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _files.isEmpty
                      ? _buildEmptyState()
                      : _buildFilesList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showUploadDialog();
        },
        backgroundColor: AppColors.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No files found',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Upload your first file to get started',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilesList() {
    return RefreshIndicator(
      onRefresh: () => _loadFiles(refresh: true),
      child: ListView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        children: [
          if (_viewMode == 'grid') _buildGridView() else _buildListView(),
          if (_isLoadingMore)
            const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: kIsWeb ? 6 : 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: _files.length,
      itemBuilder: (context, index) => _buildFileGridItem(_files[index]),
    );
  }

  Widget _buildListView() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _files.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) => _buildFileListItem(_files[index]),
    );
  }

  Widget _buildFileGridItem(FileModel file) {
    final isSelected = _selectedFiles.contains(file.id);

    return GestureDetector(
      onTap: () => _toggleFileSelection(file.id),
      onLongPress: () => _toggleFileSelection(file.id),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // File Preview
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(8)),
                ),
                child: file.isImage
                    ? ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(8)),
                        child: Image.network(
                          file.url,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Center(
                              child: Text(file.fileTypeIcon,
                                  style: const TextStyle(fontSize: 32))),
                        ),
                      )
                    : Center(
                        child: Text(
                          file.fileTypeIcon,
                          style: const TextStyle(fontSize: 32),
                        ),
                      ),
              ),
            ),
            // File Info
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    file.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    file.sizeFormatted,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileListItem(FileModel file) {
    final isSelected = _selectedFiles.contains(file.id);

    return Container(
      color:
          isSelected ? AppColors.primaryColor.withOpacity(0.1) : Colors.white,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.grey[100],
          child: file.isImage
              ? ClipOval(
                  child: Image.network(
                    file.url,
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Text(
                        file.fileTypeIcon,
                        style: const TextStyle(fontSize: 20)),
                  ),
                )
              : Text(file.fileTypeIcon, style: const TextStyle(fontSize: 20)),
        ),
        title: Text(
          file.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(file.categoryDisplayName),
            Text(
              '${file.sizeFormatted} • ${file.createdAt.toString().split(' ')[0]}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
        trailing: isSelected
            ? Icon(Icons.check_circle, color: AppColors.primaryColor)
            : const Icon(Icons.more_vert),
        onTap: () => _toggleFileSelection(file.id),
        onLongPress: () => _toggleFileSelection(file.id),
      ),
    );
  }
}
